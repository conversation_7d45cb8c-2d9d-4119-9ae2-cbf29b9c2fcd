# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.env.local
.env.*.local
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
logs/
*.log
exports/
data/
backups/
downloads/
*.xlsx
*.csv
*.xls
*.json
!requirements.json
!package.json
!tsconfig.json
!.vscode/settings.json

# Test results and temporary files
test_result_*.json
production_test_results.json
*_test_results.json

# Docker
docker-compose.override.yml
.dockerignore

# Nginx logs and configs
nginx/logs/
nginx/ssl/
nginx/conf.d/

# Environment and secrets
.env
.env.local
.env.*.local
!.env.example

# API keys and credentials
**/api_keys/
**/credentials/
*secret*
*key*
*.pem
*.key
*.crt

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary and cache files
tmp/
temp/
.cache/
*.tmp
*.bak
*.swp
*.swo
*~

# Development tools
.vscode/
.idea/
*.iml

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Chinese filename encoding issues
%*

# Project specific directories that should be ignored
参考项目/*/logs/
参考项目/*/exports/
参考项目/*/downloads/
参考项目/*/backups/
