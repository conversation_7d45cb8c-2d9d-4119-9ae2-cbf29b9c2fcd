#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Integration tests for 星图超级接口线上化 with real Xingtu data
"""

import pytest
import asyncio
import os
import sys
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from core.excel_exporter import ExcelExporter
from config.api_endpoints import XINGTU_API_ENDPOINTS


class TestIntegration:
    """Integration tests with real Xingtu API"""
    
    @pytest.fixture(scope="class")
    def cookie_manager(self):
        """Cookie manager fixture"""
        return CookieManager()
    
    @pytest.fixture(scope="class")
    def xingtu_client(self, cookie_manager):
        """Xingtu client fixture"""
        return XingtuClient(cookie_manager)
    
    @pytest.fixture(scope="class")
    def excel_exporter(self):
        """Excel exporter fixture"""
        return ExcelExporter()
    
    @pytest.fixture
    def test_author_id(self):
        """Test author ID - replace with actual valid ID"""
        return os.getenv("TEST_AUTHOR_ID", "1798563067188323")
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    class TestRealDataFetching:
        """Tests with real Xingtu data"""
        
        @pytest.mark.asyncio
        async def test_fetch_single_endpoint(self, xingtu_client, test_author_id):
            """Test fetching data from a single endpoint"""
            result = await xingtu_client.get_author_info(test_author_id, ["base_info"])
            
            assert isinstance(result, dict)
            assert "_metadata" in result
            assert "base_info" in result
            
            metadata = result["_metadata"]
            assert metadata["author_id"] == test_author_id
            assert metadata["total_endpoints"] == 1
            
            # Check if request was successful
            if metadata["successful_requests"] > 0:
                base_info = result["base_info"]
                assert base_info.get("success", False)
                assert "data" in base_info
        
        @pytest.mark.asyncio
        async def test_fetch_by_category(self, xingtu_client, test_author_id):
            """Test fetching data by category"""
            result = await xingtu_client.get_author_info_by_category(test_author_id, "basic")
            
            assert isinstance(result, dict)
            assert "_metadata" in result
            
            metadata = result["_metadata"]
            assert metadata["author_id"] == test_author_id
            assert metadata["total_endpoints"] > 0
        
        @pytest.mark.asyncio
        async def test_fetch_all_endpoints(self, xingtu_client, test_author_id):
            """Test fetching data from all endpoints"""
            result = await xingtu_client.get_author_info(test_author_id)
            
            assert isinstance(result, dict)
            assert "_metadata" in result
            
            metadata = result["_metadata"]
            assert metadata["author_id"] == test_author_id
            assert metadata["total_endpoints"] == len(XINGTU_API_ENDPOINTS)
            
            # Check success rate
            success_rate = metadata.get("success_rate", 0)
            print(f"Success rate: {success_rate:.2%}")
            
            # At least some endpoints should succeed if cookies are valid
            if metadata["successful_requests"] > 0:
                assert success_rate > 0
        
        @pytest.mark.asyncio
        async def test_rate_limiting(self, xingtu_client, test_author_id):
            """Test rate limiting functionality"""
            start_time = time.time()
            
            # Make multiple requests
            tasks = []
            for i in range(3):
                task = xingtu_client.get_author_info(test_author_id, ["base_info"])
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Should take some time due to rate limiting
            assert duration > 1  # At least 1 second for 3 requests
            
            # All requests should complete
            assert len(results) == 3
            
            # Check that results are valid
            for result in results:
                if not isinstance(result, Exception):
                    assert isinstance(result, dict)
                    assert "_metadata" in result
    
    @pytest.mark.skipif(
        not all([
            os.getenv("COOKIECLOUD_SERVER_URL"),
            os.getenv("COOKIECLOUD_UUID"),
            os.getenv("COOKIECLOUD_PASSWORD")
        ]),
        reason="CookieCloud configuration not available"
    )
    class TestExcelExport:
        """Tests for Excel export functionality"""
        
        @pytest.mark.asyncio
        async def test_excel_export_single_author(self, xingtu_client, excel_exporter, test_author_id):
            """Test Excel export for single author"""
            # Fetch author data
            author_data = await xingtu_client.get_author_info(test_author_id, ["base_info", "global_info"])
            
            # Export to Excel
            file_path = excel_exporter.export_to_excel(author_data)
            
            if file_path:
                assert file_path.exists()
                assert file_path.suffix == ".xlsx"
                assert file_path.stat().st_size > 0
                
                print(f"Excel file created: {file_path}")
                print(f"File size: {file_path.stat().st_size} bytes")
                
                # Clean up
                file_path.unlink()
        
        @pytest.mark.asyncio
        async def test_csv_export_single_author(self, xingtu_client, excel_exporter, test_author_id):
            """Test CSV export for single author"""
            # Fetch author data
            author_data = await xingtu_client.get_author_info(test_author_id, ["base_info"])
            
            # Export to CSV
            file_path = excel_exporter.export_to_csv(author_data)
            
            if file_path:
                assert file_path.exists()
                assert file_path.suffix == ".csv"
                assert file_path.stat().st_size > 0
                
                print(f"CSV file created: {file_path}")
                print(f"File size: {file_path.stat().st_size} bytes")
                
                # Clean up
                file_path.unlink()
        
        @pytest.mark.asyncio
        async def test_batch_excel_export(self, xingtu_client, excel_exporter, test_author_id):
            """Test batch Excel export"""
            # Fetch data for multiple authors (using same ID for testing)
            authors_data = []
            for i in range(2):
                author_data = await xingtu_client.get_author_info(test_author_id, ["base_info"])
                authors_data.append(author_data)
            
            # Export batch to Excel
            file_path = excel_exporter.export_multiple_authors(authors_data)
            
            if file_path:
                assert file_path.exists()
                assert file_path.suffix == ".xlsx"
                assert file_path.stat().st_size > 0
                
                print(f"Batch Excel file created: {file_path}")
                print(f"File size: {file_path.stat().st_size} bytes")
                
                # Clean up
                file_path.unlink()
    
    class TestCookieManagement:
        """Tests for cookie management"""
        
        @pytest.mark.skipif(
            not all([
                os.getenv("COOKIECLOUD_SERVER_URL"),
                os.getenv("COOKIECLOUD_UUID"),
                os.getenv("COOKIECLOUD_PASSWORD")
            ]),
            reason="CookieCloud configuration not available"
        )
        def test_cookie_refresh_cycle(self, cookie_manager):
            """Test complete cookie refresh cycle"""
            # Get initial cookie info
            initial_info = cookie_manager.get_cookies_info()
            print(f"Initial cookies: {initial_info}")
            
            # Force refresh
            refresh_result = cookie_manager.refresh_cookies()
            print(f"Refresh result: {refresh_result}")
            
            # Get updated info
            updated_info = cookie_manager.get_cookies_info()
            print(f"Updated cookies: {updated_info}")
            
            # Validate cookies
            validation_result = cookie_manager.validate_xingtu_cookies()
            print(f"Validation result: {validation_result}")
            
            # Health check
            health_result = cookie_manager.is_healthy()
            print(f"Health check: {health_result}")
        
        @pytest.mark.skipif(
            not all([
                os.getenv("COOKIECLOUD_SERVER_URL"),
                os.getenv("COOKIECLOUD_UUID"),
                os.getenv("COOKIECLOUD_PASSWORD")
            ]),
            reason="CookieCloud configuration not available"
        )
        def test_csrf_token_extraction(self, cookie_manager):
            """Test CSRF token extraction"""
            csrf_token = cookie_manager.get_csrf_token()
            print(f"CSRF token: {csrf_token}")
            
            if csrf_token:
                assert isinstance(csrf_token, str)
                assert len(csrf_token) > 0
        
        @pytest.mark.skipif(
            not all([
                os.getenv("COOKIECLOUD_SERVER_URL"),
                os.getenv("COOKIECLOUD_UUID"),
                os.getenv("COOKIECLOUD_PASSWORD")
            ]),
            reason="CookieCloud configuration not available"
        )
        def test_cookie_header_format(self, cookie_manager):
            """Test cookie header formatting"""
            cookie_header = cookie_manager.get_cookie_header()
            print(f"Cookie header length: {len(cookie_header)}")
            
            if cookie_header:
                assert isinstance(cookie_header, str)
                assert "=" in cookie_header  # Should contain key=value pairs
    
    class TestErrorHandling:
        """Tests for error handling"""
        
        @pytest.mark.asyncio
        async def test_invalid_author_id(self, xingtu_client):
            """Test handling of invalid author ID"""
            result = await xingtu_client.get_author_info("invalid_id")
            
            assert isinstance(result, dict)
            assert "error" in result
            assert "Invalid author ID format" in result["error"]
        
        @pytest.mark.asyncio
        async def test_nonexistent_category(self, xingtu_client, test_author_id):
            """Test handling of nonexistent category"""
            result = await xingtu_client.get_author_info_by_category(test_author_id, "nonexistent")
            
            assert isinstance(result, dict)
            assert "error" in result
            assert "No endpoints found for category" in result["error"]
        
        def test_export_invalid_data(self, excel_exporter):
            """Test export with invalid data"""
            invalid_data = {"invalid": "data"}
            
            result = excel_exporter.export_to_excel(invalid_data)
            assert result is None
            
            result = excel_exporter.export_to_csv(invalid_data)
            assert result is None


def run_integration_tests():
    """Run integration tests with proper setup"""
    # Check if required environment variables are set
    required_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID",
        "COOKIECLOUD_PASSWORD"
    ]
    
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"Missing required environment variables: {missing_vars}")
        print("Please set up CookieCloud configuration to run integration tests.")
        return
    
    # Run tests
    pytest.main([__file__, "-v", "-s"])


if __name__ == "__main__":
    run_integration_tests()
