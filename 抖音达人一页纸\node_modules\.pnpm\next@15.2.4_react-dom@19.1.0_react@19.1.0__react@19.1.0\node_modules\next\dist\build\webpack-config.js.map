{"version": 3, "sources": ["../../src/build/webpack-config.ts"], "sourcesContent": ["import React from 'react'\nimport ReactRefreshWebpackPlugin from 'next/dist/compiled/@next/react-refresh-utils/dist/ReactRefreshWebpackPlugin'\nimport { yellow, bold } from '../lib/picocolors'\nimport crypto from 'crypto'\nimport { webpack } from 'next/dist/compiled/webpack/webpack'\nimport path from 'path'\n\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES } from '../lib/constants'\nimport type { WebpackLayerName } from '../lib/constants'\nimport {\n  isWebpackBundledLayer,\n  isWebpackClientOnlyLayer,\n  isWebpackDefaultLayer,\n  isWebpackServerOnlyLayer,\n} from './utils'\nimport type { CustomRoutes } from '../lib/load-custom-routes.js'\nimport {\n  CLIENT_STATIC_FILES_RUNTIME_AMP,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN,\n  CLIENT_STATIC_FILES_RUNTIME_MAIN_APP,\n  CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL,\n  CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH,\n  CLIENT_STATIC_FILES_RUNTIME_WEBPACK,\n  MIDDLEWARE_REACT_LOADABLE_MANIFEST,\n  REACT_LOADABLE_MANIFEST,\n  SERVER_DIRECTORY,\n  COMPILER_NAMES,\n} from '../shared/lib/constants'\nimport type { CompilerNameValues } from '../shared/lib/constants'\nimport { execOnce } from '../shared/lib/utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport { finalizeEntrypoint } from './entries'\nimport * as Log from './output/log'\nimport { buildConfiguration } from './webpack/config'\nimport MiddlewarePlugin, {\n  getEdgePolyfilledModules,\n  handleWebpackExternalForEdgeRuntime,\n} from './webpack/plugins/middleware-plugin'\nimport BuildManifestPlugin from './webpack/plugins/build-manifest-plugin'\nimport { JsConfigPathsPlugin } from './webpack/plugins/jsconfig-paths-plugin'\nimport { DropClientPage } from './webpack/plugins/next-drop-client-page-plugin'\nimport PagesManifestPlugin from './webpack/plugins/pages-manifest-plugin'\nimport { ProfilingPlugin } from './webpack/plugins/profiling-plugin'\nimport { ReactLoadablePlugin } from './webpack/plugins/react-loadable-plugin'\nimport { WellKnownErrorsPlugin } from './webpack/plugins/wellknown-errors-plugin'\nimport { regexLikeCss } from './webpack/config/blocks/css'\nimport { CopyFilePlugin } from './webpack/plugins/copy-file-plugin'\nimport { ClientReferenceManifestPlugin } from './webpack/plugins/flight-manifest-plugin'\nimport { FlightClientEntryPlugin as NextFlightClientEntryPlugin } from './webpack/plugins/flight-client-entry-plugin'\nimport { RspackFlightClientEntryPlugin } from './webpack/plugins/rspack-flight-client-entry-plugin'\nimport { NextTypesPlugin } from './webpack/plugins/next-types-plugin'\nimport type {\n  Feature,\n  SWC_TARGET_TRIPLE,\n} from './webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { Span } from '../trace'\nimport type { MiddlewareMatcher } from './analysis/get-page-static-info'\nimport loadJsConfig, {\n  type JsConfig,\n  type ResolvedBaseUrl,\n} from './load-jsconfig'\nimport { loadBindings } from './swc'\nimport { AppBuildManifestPlugin } from './webpack/plugins/app-build-manifest-plugin'\nimport { SubresourceIntegrityPlugin } from './webpack/plugins/subresource-integrity-plugin'\nimport { NextFontManifestPlugin } from './webpack/plugins/next-font-manifest-plugin'\nimport { getSupportedBrowsers } from './utils'\nimport { MemoryWithGcCachePlugin } from './webpack/plugins/memory-with-gc-cache-plugin'\nimport { getBabelConfigFile } from './get-babel-config-file'\nimport { needsExperimentalReact } from '../lib/needs-experimental-react'\nimport { getDefineEnvPlugin } from './webpack/plugins/define-env-plugin'\nimport type { SWCLoaderOptions } from './webpack/loaders/next-swc-loader'\nimport { isResourceInPackages, makeExternalHandler } from './handle-externals'\nimport {\n  getMainField,\n  edgeConditionNames,\n} from './webpack-config-rules/resolve'\nimport { OptionalPeerDependencyResolverPlugin } from './webpack/plugins/optional-peer-dependency-resolve-plugin'\nimport {\n  createWebpackAliases,\n  createServerOnlyClientOnlyAliases,\n  createRSCAliases,\n  createNextApiEsmAliases,\n  createAppRouterApiAliases,\n} from './create-compiler-aliases'\nimport { hasCustomExportOutput } from '../export/utils'\nimport { CssChunkingPlugin } from './webpack/plugins/css-chunking-plugin'\nimport {\n  getBabelLoader,\n  getReactCompilerLoader,\n} from './get-babel-loader-config'\nimport {\n  NEXT_PROJECT_ROOT,\n  NEXT_PROJECT_ROOT_DIST_CLIENT,\n} from './next-dir-paths'\nimport { getRspackReactRefresh } from '../shared/lib/get-rspack'\nimport { RspackProfilingPlugin } from './webpack/plugins/rspack-profiling-plugin'\n\ntype ExcludesFalse = <T>(x: T | false) => x is T\ntype ClientEntries = {\n  [key: string]: string | string[]\n}\n\nconst EXTERNAL_PACKAGES =\n  require('../lib/server-external-packages.json') as string[]\n\nconst DEFAULT_TRANSPILED_PACKAGES =\n  require('../lib/default-transpiled-packages.json') as string[]\n\nif (parseInt(React.version) < 18) {\n  throw new Error('Next.js requires react >= 18.2.0 to be installed.')\n}\n\nexport const babelIncludeRegexes: RegExp[] = [\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?shared[\\\\/]lib/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?client/,\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?pages/,\n  /[\\\\/](strip-ansi|ansi-regex|styled-jsx)[\\\\/]/,\n]\n\nconst browserNonTranspileModules = [\n  // Transpiling `process/browser` will trigger babel compilation error due to value replacement.\n  // TypeError: Property left of AssignmentExpression expected node to be of a type [\"LVal\"] but instead got \"BooleanLiteral\"\n  // e.g. `process.browser = true` will become `true = true`.\n  /[\\\\/]node_modules[\\\\/]process[\\\\/]browser/,\n  // Exclude precompiled react packages from browser compilation due to SWC helper insertion (#61791),\n  // We fixed the issue but it's safer to exclude them from compilation since they don't need to be re-compiled.\n  /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/](react|react-dom|react-server-dom-webpack)(-experimental)?($|[\\\\/])/,\n]\nconst precompileRegex = /[\\\\/]next[\\\\/]dist[\\\\/]compiled[\\\\/]/\n\nconst asyncStoragesRegex =\n  /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]app-render[\\\\/](work-async-storage|action-async-storage|work-unit-async-storage)/\n\n// Support for NODE_PATH\nconst nodePathList = (process.env.NODE_PATH || '')\n  .split(process.platform === 'win32' ? ';' : ':')\n  .filter((p) => !!p)\n\nconst baseWatchOptions: webpack.Configuration['watchOptions'] = Object.freeze({\n  aggregateTimeout: 5,\n  ignored:\n    // Matches **/node_modules/**, **/.git/** and **/.next/**\n    /^((?:[^/]*(?:\\/|$))*)(\\.(git|next)|node_modules)(\\/((?:[^/]*(?:\\/|$))*)(?:$|\\/))?/,\n})\n\nfunction isModuleCSS(module: { type: string }) {\n  return (\n    // mini-css-extract-plugin\n    module.type === `css/mini-extract` ||\n    // extract-css-chunks-webpack-plugin (old)\n    module.type === `css/extract-chunks` ||\n    // extract-css-chunks-webpack-plugin (new)\n    module.type === `css/extract-css-chunks`\n  )\n}\n\nconst devtoolRevertWarning = execOnce(\n  (devtool: webpack.Configuration['devtool']) => {\n    console.warn(\n      yellow(bold('Warning: ')) +\n        bold(`Reverting webpack devtool to '${devtool}'.\\n`) +\n        'Changing the webpack devtool in development mode will cause severe performance regressions.\\n' +\n        'Read more: https://nextjs.org/docs/messages/improper-devtool'\n    )\n  }\n)\n\nlet loggedSwcDisabled = false\nlet loggedIgnoredCompilerOptions = false\nconst reactRefreshLoaderName =\n  'next/dist/compiled/@next/react-refresh-utils/dist/loader'\n\nexport function attachReactRefresh(\n  webpackConfig: webpack.Configuration,\n  targetLoader: webpack.RuleSetUseItem\n) {\n  const reactRefreshLoader = require.resolve(reactRefreshLoaderName)\n  webpackConfig.module?.rules?.forEach((rule) => {\n    if (rule && typeof rule === 'object' && 'use' in rule) {\n      const curr = rule.use\n      // When the user has configured `defaultLoaders.babel` for a input file:\n      if (curr === targetLoader) {\n        rule.use = [reactRefreshLoader, curr as webpack.RuleSetUseItem]\n      } else if (\n        Array.isArray(curr) &&\n        curr.some((r) => r === targetLoader) &&\n        // Check if loader already exists:\n        !curr.some(\n          (r) => r === reactRefreshLoader || r === reactRefreshLoaderName\n        )\n      ) {\n        const idx = curr.findIndex((r) => r === targetLoader)\n        // Clone to not mutate user input\n        rule.use = [...curr]\n\n        // inject / input: [other, babel] output: [other, refresh, babel]:\n        rule.use.splice(idx, 0, reactRefreshLoader)\n      }\n    }\n  })\n}\n\nexport const NODE_RESOLVE_OPTIONS = {\n  dependencyType: 'commonjs',\n  modules: ['node_modules'],\n  fallback: false,\n  exportsFields: ['exports'],\n  importsFields: ['imports'],\n  conditionNames: ['node', 'require'],\n  descriptionFiles: ['package.json'],\n  extensions: ['.js', '.json', '.node'],\n  enforceExtensions: false,\n  symlinks: true,\n  mainFields: ['main'],\n  mainFiles: ['index'],\n  roots: [],\n  fullySpecified: false,\n  preferRelative: false,\n  preferAbsolute: false,\n  restrictions: [],\n}\n\nexport const NODE_BASE_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const NODE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_RESOLVE_OPTIONS,\n  alias: false,\n  dependencyType: 'esm',\n  conditionNames: ['node', 'import'],\n  fullySpecified: true,\n}\n\nexport const NODE_BASE_ESM_RESOLVE_OPTIONS = {\n  ...NODE_ESM_RESOLVE_OPTIONS,\n  alias: false,\n}\n\nexport const nextImageLoaderRegex =\n  /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i\n\nexport async function loadProjectInfo({\n  dir,\n  config,\n  dev,\n}: {\n  dir: string\n  config: NextConfigComplete\n  dev: boolean\n}): Promise<{\n  jsConfig: JsConfig\n  jsConfigPath?: string\n  resolvedBaseUrl: ResolvedBaseUrl\n  supportedBrowsers: string[] | undefined\n}> {\n  const { jsConfig, jsConfigPath, resolvedBaseUrl } = await loadJsConfig(\n    dir,\n    config\n  )\n  const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  return {\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n  }\n}\n\nexport function hasExternalOtelApiPackage(): boolean {\n  try {\n    require('@opentelemetry/api')\n    return true\n  } catch {\n    return false\n  }\n}\n\nconst UNSAFE_CACHE_REGEX = /[\\\\/]pages[\\\\/][^\\\\/]+(?:$|\\?|#)/\n\nexport default async function getBaseWebpackConfig(\n  dir: string,\n  {\n    buildId,\n    encryptionKey,\n    config,\n    compilerType,\n    dev = false,\n    entrypoints,\n    isDevFallback = false,\n    pagesDir,\n    reactProductionProfiling = false,\n    rewrites,\n    originalRewrites,\n    originalRedirects,\n    runWebpackSpan,\n    appDir,\n    middlewareMatchers,\n    noMangling,\n    jsConfig,\n    jsConfigPath,\n    resolvedBaseUrl,\n    supportedBrowsers,\n    clientRouterFilters,\n    fetchCacheKeyPrefix,\n    edgePreviewProps,\n  }: {\n    buildId: string\n    encryptionKey: string\n    config: NextConfigComplete\n    compilerType: CompilerNameValues\n    dev?: boolean\n    entrypoints: webpack.EntryObject\n    isDevFallback?: boolean\n    pagesDir: string | undefined\n    reactProductionProfiling?: boolean\n    rewrites: CustomRoutes['rewrites']\n    originalRewrites: CustomRoutes['rewrites'] | undefined\n    originalRedirects: CustomRoutes['redirects'] | undefined\n    runWebpackSpan: Span\n    appDir: string | undefined\n    middlewareMatchers?: MiddlewareMatcher[]\n    noMangling?: boolean\n    jsConfig: any\n    jsConfigPath?: string\n    resolvedBaseUrl: ResolvedBaseUrl\n    supportedBrowsers: string[] | undefined\n    edgePreviewProps?: Record<string, string>\n    clientRouterFilters?: {\n      staticFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n      dynamicFilter: ReturnType<\n        import('../shared/lib/bloom-filter').BloomFilter['export']\n      >\n    }\n    fetchCacheKeyPrefix?: string\n  }\n): Promise<webpack.Configuration> {\n  const isClient = compilerType === COMPILER_NAMES.client\n  const isEdgeServer = compilerType === COMPILER_NAMES.edgeServer\n  const isNodeServer = compilerType === COMPILER_NAMES.server\n\n  const isRspack = Boolean(process.env.NEXT_RSPACK)\n\n  const FlightClientEntryPlugin =\n    isRspack && process.env.BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN\n      ? RspackFlightClientEntryPlugin\n      : NextFlightClientEntryPlugin\n\n  // If the current compilation is aimed at server-side code instead of client-side code.\n  const isNodeOrEdgeCompilation = isNodeServer || isEdgeServer\n\n  const hasRewrites =\n    rewrites.beforeFiles.length > 0 ||\n    rewrites.afterFiles.length > 0 ||\n    rewrites.fallback.length > 0\n\n  const hasAppDir = !!appDir\n  const disableOptimizedLoading = true\n  const enableTypedRoutes = !!config.experimental.typedRoutes && hasAppDir\n  const bundledReactChannel = needsExperimentalReact(config)\n    ? '-experimental'\n    : ''\n\n  const babelConfigFile = getBabelConfigFile(dir)\n\n  if (!dev && hasCustomExportOutput(config)) {\n    config.distDir = '.next'\n  }\n  const distDir = path.join(dir, config.distDir)\n\n  let useSWCLoader = !babelConfigFile || config.experimental.forceSwcTransforms\n  let SWCBinaryTarget: [Feature, boolean] | undefined = undefined\n  if (useSWCLoader) {\n    // TODO: we do not collect wasm target yet\n    const binaryTarget = require('./swc')?.getBinaryMetadata?.()\n      ?.target as SWC_TARGET_TRIPLE\n    SWCBinaryTarget = binaryTarget\n      ? [`swc/target/${binaryTarget}` as const, true]\n      : undefined\n  }\n\n  if (!loggedSwcDisabled && !useSWCLoader && babelConfigFile) {\n    Log.info(\n      `Disabled SWC as replacement for Babel because of custom Babel configuration \"${path.relative(\n        dir,\n        babelConfigFile\n      )}\" https://nextjs.org/docs/messages/swc-disabled`\n    )\n    loggedSwcDisabled = true\n  }\n\n  // eagerly load swc bindings instead of waiting for transform calls\n  if (!babelConfigFile && isClient) {\n    await loadBindings(config.experimental.useWasmBinary)\n  }\n\n  // since `pages` doesn't always bundle by default we need to\n  // auto-include optimizePackageImports in transpilePackages\n  const finalTranspilePackages: string[] = (\n    config.transpilePackages || []\n  ).concat(DEFAULT_TRANSPILED_PACKAGES)\n\n  for (const pkg of config.experimental.optimizePackageImports || []) {\n    if (!finalTranspilePackages.includes(pkg)) {\n      finalTranspilePackages.push(pkg)\n    }\n  }\n\n  if (!loggedIgnoredCompilerOptions && !useSWCLoader && config.compiler) {\n    Log.info(\n      '`compiler` options in `next.config.js` will be ignored while using Babel https://nextjs.org/docs/messages/ignored-compiler-options'\n    )\n    loggedIgnoredCompilerOptions = true\n  }\n\n  const shouldIncludeExternalDirs =\n    config.experimental.externalDir || !!config.transpilePackages\n  const codeCondition = {\n    test: { or: [/\\.(tsx|ts|js|cjs|mjs|jsx)$/, /__barrel_optimize__/] },\n    ...(shouldIncludeExternalDirs\n      ? // Allowing importing TS/TSX files from outside of the root dir.\n        {}\n      : { include: [dir, ...babelIncludeRegexes] }),\n    exclude: (excludePath: string) => {\n      if (babelIncludeRegexes.some((r) => r.test(excludePath))) {\n        return false\n      }\n\n      const shouldBeBundled = isResourceInPackages(\n        excludePath,\n        finalTranspilePackages\n      )\n      if (shouldBeBundled) return false\n\n      return excludePath.includes('node_modules')\n    },\n  }\n\n  const babelLoader = getBabelLoader(\n    useSWCLoader,\n    babelConfigFile,\n    isNodeOrEdgeCompilation,\n    distDir,\n    pagesDir,\n    dir,\n    (appDir || pagesDir)!,\n    dev,\n    isClient,\n    config.experimental?.reactCompiler,\n    codeCondition.exclude\n  )\n\n  const reactCompilerLoader = babelLoader\n    ? undefined\n    : getReactCompilerLoader(\n        config.experimental?.reactCompiler,\n        dir,\n        dev,\n        isNodeOrEdgeCompilation,\n        codeCondition.exclude\n      )\n\n  let swcTraceProfilingInitialized = false\n  const getSwcLoader = (extraOptions: Partial<SWCLoaderOptions>) => {\n    if (\n      config?.experimental?.swcTraceProfiling &&\n      !swcTraceProfilingInitialized\n    ) {\n      // This will init subscribers once only in a single process lifecycle,\n      // even though it can be called multiple times.\n      // Subscriber need to be initialized _before_ any actual swc's call (transform, etcs)\n      // to collect correct trace spans when they are called.\n      swcTraceProfilingInitialized = true\n      require('./swc')?.initCustomTraceSubscriber?.(\n        path.join(distDir, `swc-trace-profile-${Date.now()}.json`)\n      )\n    }\n\n    const useBuiltinSwcLoader = process.env.BUILTIN_SWC_LOADER\n    if (isRspack && useBuiltinSwcLoader) {\n      return {\n        loader: 'builtin:next-swc-loader',\n        options: {\n          isServer: isNodeOrEdgeCompilation,\n          rootDir: dir,\n          pagesDir,\n          appDir,\n          hasReactRefresh: dev && isClient,\n          transpilePackages: finalTranspilePackages,\n          supportedBrowsers,\n          swcCacheDir: path.join(\n            dir,\n            config?.distDir ?? '.next',\n            'cache',\n            'swc'\n          ),\n          serverReferenceHashSalt: encryptionKey,\n\n          // rspack specific options\n          pnp: Boolean(process.versions.pnp),\n          optimizeServerReact: Boolean(config.experimental.optimizeServerReact),\n          modularizeImports: config.modularizeImports,\n          decorators: Boolean(\n            jsConfig?.compilerOptions?.experimentalDecorators\n          ),\n          emitDecoratorMetadata: Boolean(\n            jsConfig?.compilerOptions?.emitDecoratorMetadata\n          ),\n          regeneratorRuntimePath: require.resolve(\n            'next/dist/compiled/regenerator-runtime'\n          ),\n\n          ...extraOptions,\n        },\n      }\n    }\n\n    return {\n      loader: 'next-swc-loader',\n      options: {\n        isServer: isNodeOrEdgeCompilation,\n        rootDir: dir,\n        pagesDir,\n        appDir,\n        hasReactRefresh: dev && isClient,\n        nextConfig: config,\n        jsConfig,\n        transpilePackages: finalTranspilePackages,\n        supportedBrowsers,\n        swcCacheDir: path.join(dir, config?.distDir ?? '.next', 'cache', 'swc'),\n        serverReferenceHashSalt: encryptionKey,\n        ...extraOptions,\n      } satisfies SWCLoaderOptions,\n    }\n  }\n\n  // RSC loaders, prefer ESM, set `esm` to true\n  const swcServerLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.reactServerComponents,\n    esm: true,\n  })\n  const swcSSRLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.serverSideRendering,\n    esm: true,\n  })\n  const swcBrowserLayerLoader = getSwcLoader({\n    serverComponents: true,\n    bundleLayer: WEBPACK_LAYERS.appPagesBrowser,\n    esm: true,\n  })\n  // Default swc loaders for pages doesn't prefer ESM.\n  const swcDefaultLoader = getSwcLoader({\n    serverComponents: true,\n    esm: false,\n  })\n\n  const defaultLoaders = {\n    babel: useSWCLoader ? swcDefaultLoader : babelLoader!,\n  }\n\n  const appServerLayerLoaders = hasAppDir\n    ? [\n        // When using Babel, we will have to add the SWC loader\n        // as an additional pass to handle RSC correctly.\n        // This will cause some performance overhead but\n        // acceptable as Babel will not be recommended.\n        swcServerLayerLoader,\n        babelLoader,\n        reactCompilerLoader,\n      ].filter(Boolean)\n    : []\n\n  const instrumentLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to add the SWC loader\n    // as an additional pass to handle RSC correctly.\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    swcServerLayerLoader,\n    babelLoader,\n  ].filter(Boolean)\n\n  const middlewareLayerLoaders = [\n    'next-flight-loader',\n    // When using Babel, we will have to use SWC to do the optimization\n    // for middleware to tree shake the unused default optimized imports like \"next/server\".\n    // This will cause some performance overhead but\n    // acceptable as Babel will not be recommended.\n    getSwcLoader({\n      serverComponents: true,\n      bundleLayer: WEBPACK_LAYERS.middleware,\n    }),\n    babelLoader,\n  ].filter(Boolean)\n\n  const reactRefreshLoaders =\n    dev && isClient ? [require.resolve(reactRefreshLoaderName)] : []\n\n  // client components layers: SSR or browser\n  const createClientLayerLoader = ({\n    isBrowserLayer,\n    reactRefresh,\n  }: {\n    isBrowserLayer: boolean\n    reactRefresh: boolean\n  }) => [\n    ...(reactRefresh ? reactRefreshLoaders : []),\n    {\n      // This loader handles actions and client entries\n      // in the client layer.\n      loader: 'next-flight-client-module-loader',\n    },\n    ...(hasAppDir\n      ? [\n          // When using Babel, we will have to add the SWC loader\n          // as an additional pass to handle RSC correctly.\n          // This will cause some performance overhead but\n          // acceptable as Babel will not be recommended.\n          isBrowserLayer ? swcBrowserLayerLoader : swcSSRLayerLoader,\n          babelLoader,\n          reactCompilerLoader,\n        ].filter(Boolean)\n      : []),\n  ]\n\n  const appBrowserLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: true,\n    // reactRefresh for browser layer is applied conditionally to user-land source\n    reactRefresh: false,\n  })\n  const appSSRLayerLoaders = createClientLayerLoader({\n    isBrowserLayer: false,\n    reactRefresh: true,\n  })\n\n  // Loader for API routes needs to be differently configured as it shouldn't\n  // have RSC transpiler enabled, so syntax checks such as invalid imports won't\n  // be performed.\n  const apiRoutesLayerLoaders = useSWCLoader\n    ? getSwcLoader({\n        serverComponents: false,\n        bundleLayer: WEBPACK_LAYERS.apiNode,\n      })\n    : defaultLoaders.babel\n\n  const pageExtensions = config.pageExtensions\n\n  const outputPath = isNodeOrEdgeCompilation\n    ? path.join(distDir, SERVER_DIRECTORY)\n    : distDir\n\n  const reactServerCondition = [\n    'react-server',\n    ...(isEdgeServer ? edgeConditionNames : []),\n    // inherits the default conditions\n    '...',\n  ]\n\n  const clientEntries = isClient\n    ? ({\n        // Backwards compatibility\n        'main.js': [],\n        ...(dev\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH]: require.resolve(\n                `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n              ),\n              [CLIENT_STATIC_FILES_RUNTIME_AMP]:\n                `./` +\n                path\n                  .relative(\n                    dir,\n                    path.join(NEXT_PROJECT_ROOT_DIST_CLIENT, 'dev', 'amp-dev')\n                  )\n                  .replace(/\\\\/g, '/'),\n            }\n          : {}),\n        [CLIENT_STATIC_FILES_RUNTIME_MAIN]:\n          `./` +\n          path\n            .relative(\n              dir,\n              path.join(\n                NEXT_PROJECT_ROOT_DIST_CLIENT,\n                dev ? `next-dev.js` : 'next.js'\n              )\n            )\n            .replace(/\\\\/g, '/'),\n        ...(hasAppDir\n          ? {\n              [CLIENT_STATIC_FILES_RUNTIME_MAIN_APP]: dev\n                ? [\n                    require.resolve(\n                      `next/dist/compiled/@next/react-refresh-utils/dist/runtime`\n                    ),\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next-dev.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ]\n                : [\n                    `./` +\n                      path\n                        .relative(\n                          dir,\n                          path.join(\n                            NEXT_PROJECT_ROOT_DIST_CLIENT,\n                            'app-next.js'\n                          )\n                        )\n                        .replace(/\\\\/g, '/'),\n                  ],\n            }\n          : {}),\n      } satisfies ClientEntries)\n    : undefined\n\n  const resolveConfig: webpack.Configuration['resolve'] = {\n    // Disable .mjs for node_modules bundling\n    extensions: ['.js', '.mjs', '.tsx', '.ts', '.jsx', '.json', '.wasm'],\n    extensionAlias: config.experimental.extensionAlias,\n    modules: [\n      'node_modules',\n      ...nodePathList, // Support for NODE_PATH environment variable\n    ],\n    alias: createWebpackAliases({\n      distDir,\n      isClient,\n      isEdgeServer,\n      isNodeServer,\n      dev,\n      config,\n      pagesDir,\n      appDir,\n      dir,\n      reactProductionProfiling,\n      hasRewrites,\n    }),\n    ...(isClient\n      ? {\n          fallback: {\n            process: require.resolve('./polyfills/process'),\n          },\n        }\n      : undefined),\n    // default main fields use pages dir ones, and customize app router ones in loaders.\n    mainFields: getMainField(compilerType, false),\n    ...(isEdgeServer && {\n      conditionNames: edgeConditionNames,\n    }),\n    plugins: [\n      isNodeServer ? new OptionalPeerDependencyResolverPlugin() : undefined,\n    ].filter(Boolean) as webpack.ResolvePluginInstance[],\n    ...((isRspack && jsConfigPath\n      ? {\n          tsConfig: {\n            configFile: jsConfigPath,\n          },\n        }\n      : {}) as any),\n  }\n\n  // Packages which will be split into the 'framework' chunk.\n  // Only top-level packages are included, e.g. nested copies like\n  // 'node_modules/meow/node_modules/object-assign' are not included.\n  const nextFrameworkPaths: string[] = []\n  const topLevelFrameworkPaths: string[] = []\n  const visitedFrameworkPackages = new Set<string>()\n  // Adds package-paths of dependencies recursively\n  const addPackagePath = (\n    packageName: string,\n    relativeToPath: string,\n    paths: string[]\n  ) => {\n    try {\n      if (visitedFrameworkPackages.has(packageName)) {\n        return\n      }\n      visitedFrameworkPackages.add(packageName)\n\n      const packageJsonPath = require.resolve(`${packageName}/package.json`, {\n        paths: [relativeToPath],\n      })\n\n      // Include a trailing slash so that a `.startsWith(packagePath)` check avoids false positives\n      // when one package name starts with the full name of a different package.\n      // For example:\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react\")  // true\n      //   \"node_modules/react-slider\".startsWith(\"node_modules/react/\") // false\n      const directory = path.join(packageJsonPath, '../')\n\n      // Returning from the function in case the directory has already been added and traversed\n      if (paths.includes(directory)) return\n      paths.push(directory)\n      const dependencies = require(packageJsonPath).dependencies || {}\n      for (const name of Object.keys(dependencies)) {\n        addPackagePath(name, directory, paths)\n      }\n    } catch (_) {\n      // don't error on failing to resolve framework packages\n    }\n  }\n\n  for (const packageName of [\n    'react',\n    'react-dom',\n    ...(hasAppDir\n      ? [\n          `next/dist/compiled/react${bundledReactChannel}`,\n          `next/dist/compiled/react-dom${bundledReactChannel}`,\n        ]\n      : []),\n  ]) {\n    addPackagePath(packageName, dir, topLevelFrameworkPaths)\n  }\n  addPackagePath('next', dir, nextFrameworkPaths)\n\n  const crossOrigin = config.crossOrigin\n\n  // The `serverExternalPackages` should not conflict with\n  // the `transpilePackages`.\n  if (config.serverExternalPackages && finalTranspilePackages) {\n    const externalPackageConflicts = finalTranspilePackages.filter((pkg) =>\n      config.serverExternalPackages?.includes(pkg)\n    )\n    if (externalPackageConflicts.length > 0) {\n      throw new Error(\n        `The packages specified in the 'transpilePackages' conflict with the 'serverExternalPackages': ${externalPackageConflicts.join(\n          ', '\n        )}`\n      )\n    }\n  }\n\n  // For original request, such as `package name`\n  const optOutBundlingPackages = EXTERNAL_PACKAGES.concat(\n    ...(config.serverExternalPackages || [])\n  ).filter((pkg) => !finalTranspilePackages?.includes(pkg))\n  // For resolved request, such as `absolute path/package name/foo/bar.js`\n  const optOutBundlingPackageRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${optOutBundlingPackages\n      .map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const transpilePackagesRegex = new RegExp(\n    `[/\\\\\\\\]node_modules[/\\\\\\\\](${finalTranspilePackages\n      ?.map((p) => p.replace(/\\//g, '[/\\\\\\\\]'))\n      .join('|')})[/\\\\\\\\]`\n  )\n\n  const handleExternals = makeExternalHandler({\n    config,\n    optOutBundlingPackageRegex,\n    transpiledPackages: finalTranspilePackages,\n    dir,\n  })\n\n  const pageExtensionsRegex = new RegExp(`\\\\.(${pageExtensions.join('|')})$`)\n\n  const aliasCodeConditionTest = [codeCondition.test, pageExtensionsRegex]\n\n  const builtinModules = require('module').builtinModules\n\n  const shouldEnableSlowModuleDetection =\n    !!config.experimental.slowModuleDetection && dev\n\n  const getParallelism = () => {\n    const override = Number(process.env.NEXT_WEBPACK_PARALLELISM)\n    if (shouldEnableSlowModuleDetection) {\n      if (override) {\n        console.warn(\n          'NEXT_WEBPACK_PARALLELISM is specified but will be ignored due to experimental.slowModuleDetection being enabled.'\n        )\n      }\n      return 1\n    }\n    return override || undefined\n  }\n\n  const telemetryPlugin =\n    !isRspack &&\n    !dev &&\n    isClient &&\n    new (\n      require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n    ).TelemetryPlugin(\n      new Map(\n        [\n          ['swcLoader', useSWCLoader],\n          ['swcRelay', !!config.compiler?.relay],\n          ['swcStyledComponents', !!config.compiler?.styledComponents],\n          [\n            'swcReactRemoveProperties',\n            !!config.compiler?.reactRemoveProperties,\n          ],\n          [\n            'swcExperimentalDecorators',\n            !!jsConfig?.compilerOptions?.experimentalDecorators,\n          ],\n          ['swcRemoveConsole', !!config.compiler?.removeConsole],\n          ['swcImportSource', !!jsConfig?.compilerOptions?.jsxImportSource],\n          ['swcEmotion', !!config.compiler?.emotion],\n          ['transpilePackages', !!config.transpilePackages],\n          ['skipMiddlewareUrlNormalize', !!config.skipMiddlewareUrlNormalize],\n          ['skipTrailingSlashRedirect', !!config.skipTrailingSlashRedirect],\n          ['modularizeImports', !!config.modularizeImports],\n          // If esmExternals is not same as default value, it represents customized usage\n          ['esmExternals', config.experimental.esmExternals !== true],\n          SWCBinaryTarget,\n        ].filter<[Feature, boolean]>(Boolean as any)\n      )\n    )\n\n  let webpackConfig: webpack.Configuration = {\n    parallelism: getParallelism(),\n    ...(isNodeServer ? { externalsPresets: { node: true } } : {}),\n    // @ts-ignore\n    externals:\n      isClient || isEdgeServer\n        ? // make sure importing \"next\" is handled gracefully for client\n          // bundles in case a user imported types and it wasn't removed\n          // TODO: should we warn/error for this instead?\n          [\n            'next',\n            ...(isEdgeServer\n              ? [\n                  {\n                    '@builder.io/partytown': '{}',\n                    'next/dist/compiled/etag': '{}',\n                  },\n                  getEdgePolyfilledModules(),\n                  handleWebpackExternalForEdgeRuntime,\n                ]\n              : []),\n          ]\n        : [\n            ...builtinModules,\n            ({\n              context,\n              request,\n              dependencyType,\n              contextInfo,\n              getResolve,\n            }: {\n              context: string\n              request: string\n              dependencyType: string\n              contextInfo: {\n                issuer: string\n                issuerLayer: string | null\n                compiler: string\n              }\n              getResolve: (\n                options: any\n              ) => (\n                resolveContext: string,\n                resolveRequest: string,\n                callback: (\n                  err?: Error,\n                  result?: string,\n                  resolveData?: { descriptionFileData?: { type?: any } }\n                ) => void\n              ) => void\n            }) =>\n              handleExternals(\n                context,\n                request,\n                dependencyType,\n                contextInfo.issuerLayer as WebpackLayerName,\n                (options) => {\n                  const resolveFunction = getResolve(options)\n                  return (resolveContext: string, requestToResolve: string) =>\n                    new Promise((resolve, reject) => {\n                      resolveFunction(\n                        resolveContext,\n                        requestToResolve,\n                        (err, result, resolveData) => {\n                          if (err) return reject(err)\n                          if (!result) return resolve([null, false])\n                          const isEsm = /\\.js$/i.test(result)\n                            ? resolveData?.descriptionFileData?.type ===\n                              'module'\n                            : /\\.mjs$/i.test(result)\n                          resolve([result, isEsm])\n                        }\n                      )\n                    })\n                }\n              ),\n          ],\n\n    optimization: {\n      emitOnErrors: !dev,\n      checkWasmTypes: false,\n      nodeEnv: false,\n\n      splitChunks: (():\n        | Required<webpack.Configuration>['optimization']['splitChunks']\n        | false => {\n        // server chunking\n        if (dev) {\n          if (isNodeServer) {\n            /*\n              In development, we want to split code that comes from `node_modules` into their own chunks.\n              This is because in development, we often need to reload the user bundle due to changes in the code.\n              To work around this, we put all the vendor code into separate chunks so that we don't need to reload them.\n              This is safe because the vendor code doesn't change between reloads.\n            */\n            const extractRootNodeModule = (modulePath: string) => {\n              // This regex is used to extract the root node module name to be used as the chunk group name.\n              // example: ../../node_modules/.pnpm/next@10/foo/node_modules/bar -> next@10\n              const regex =\n                /node_modules(?:\\/|\\\\)\\.?(?:pnpm(?:\\/|\\\\))?([^/\\\\]+)/\n              const match = modulePath.match(regex)\n              return match ? match[1] : null\n            }\n            return {\n              cacheGroups: {\n                // this chunk configuration gives us a separate chunk for each top level module in node_modules\n                // or a hashed chunk if we can't extract the module name.\n                vendor: {\n                  chunks: 'all',\n                  reuseExistingChunk: true,\n                  test: /[\\\\/]node_modules[\\\\/]/,\n                  minSize: 0,\n                  minChunks: 1,\n                  maxAsyncRequests: 300,\n                  maxInitialRequests: 300,\n                  name: (module: webpack.Module) => {\n                    const moduleId = module.nameForCondition()!\n                    const rootModule = extractRootNodeModule(moduleId)\n                    if (rootModule) {\n                      return `vendor-chunks/${rootModule}`\n                    } else {\n                      const hash = crypto.createHash('sha1').update(moduleId)\n                      hash.update(moduleId)\n                      return `vendor-chunks/${hash.digest('hex')}`\n                    }\n                  },\n                },\n                // disable the default chunk groups\n                default: false,\n                defaultVendors: false,\n              },\n            }\n          }\n\n          return false\n        }\n\n        if (isNodeServer || isEdgeServer) {\n          return {\n            filename: `${isEdgeServer ? `edge-chunks/` : ''}[name].js`,\n            chunks: 'all',\n            minChunks: 2,\n          }\n        }\n\n        const frameworkCacheGroup = {\n          chunks: 'all' as const,\n          name: 'framework',\n          // Ensures the framework chunk is not created for App Router.\n          layer: isWebpackDefaultLayer,\n          test(module: any) {\n            const resource = module.nameForCondition?.()\n            return resource\n              ? topLevelFrameworkPaths.some((pkgPath) =>\n                  resource.startsWith(pkgPath)\n                )\n              : false\n          },\n          priority: 40,\n          // Don't let webpack eliminate this chunk (prevents this chunk from\n          // becoming a part of the commons chunk)\n          enforce: true,\n        }\n\n        const libCacheGroup = {\n          test(module: {\n            type: string\n            size: Function\n            nameForCondition: Function\n          }): boolean {\n            return (\n              !module.type?.startsWith('css') &&\n              // rspack doesn't support module.size\n              (isRspack || module.size() > 160000) &&\n              /node_modules[/\\\\]/.test(module.nameForCondition() || '')\n            )\n          },\n          name(module: {\n            layer: string | null | undefined\n            type: string\n            libIdent?: Function\n            updateHash: (hash: crypto.Hash) => void\n          }): string {\n            const hash = crypto.createHash('sha1')\n            if (isModuleCSS(module)) {\n              module.updateHash(hash)\n            } else {\n              // rspack doesn't support this\n              if (!isRspack) {\n                if (!module.libIdent) {\n                  throw new Error(\n                    `Encountered unknown module type: ${module.type}. Please open an issue.`\n                  )\n                }\n                hash.update(module.libIdent({ context: dir }))\n              }\n            }\n\n            // Ensures the name of the chunk is not the same between two modules in different layers\n            // E.g. if you import 'button-library' in App Router and Pages Router we don't want these to be bundled in the same chunk\n            // as they're never used on the same page.\n            if (module.layer) {\n              hash.update(module.layer)\n            }\n\n            return hash.digest('hex').substring(0, 8)\n          },\n          priority: 30,\n          minChunks: 1,\n          reuseExistingChunk: true,\n        }\n\n        // client chunking\n        return {\n          // Keep main and _app chunks unsplitted in webpack 5\n          // as we don't need a separate vendor chunk from that\n          // and all other chunk depend on them so there is no\n          // duplication that need to be pulled out.\n          chunks: isRspack\n            ? // using a function here causes noticable slowdown\n              // in rspack\n              /(?!polyfills|main|pages\\/_app)/\n            : (chunk: any) =>\n                !/^(polyfills|main|pages\\/_app)$/.test(chunk.name),\n\n          // TODO: investigate these cache groups with rspack\n          cacheGroups: isRspack\n            ? {}\n            : {\n                framework: frameworkCacheGroup,\n                lib: libCacheGroup,\n              },\n          maxInitialRequests: 25,\n          minSize: 20000,\n        }\n      })(),\n      runtimeChunk: isClient\n        ? { name: CLIENT_STATIC_FILES_RUNTIME_WEBPACK }\n        : undefined,\n\n      minimize:\n        !dev &&\n        (isClient ||\n          isEdgeServer ||\n          (isNodeServer && config.experimental.serverMinification)),\n      minimizer: isRspack\n        ? [\n            // @ts-expect-error\n            new webpack.SwcJsMinimizerRspackPlugin({\n              // JS minimizer configuration\n            }),\n            // @ts-expect-error\n            new webpack.LightningCssMinimizerRspackPlugin({\n              // CSS minimizer configuration\n            }),\n          ]\n        : [\n            // Minify JavaScript\n            (compiler: webpack.Compiler) => {\n              // @ts-ignore No typings yet\n              const { MinifyPlugin } =\n                require('./webpack/plugins/minify-webpack-plugin/src/index.js') as typeof import('./webpack/plugins/minify-webpack-plugin/src')\n              new MinifyPlugin({ noMangling }).apply(compiler)\n            },\n            // Minify CSS\n            (compiler: webpack.Compiler) => {\n              const {\n                CssMinimizerPlugin,\n              } = require('./webpack/plugins/css-minimizer-plugin')\n              new CssMinimizerPlugin({\n                postcssOptions: {\n                  map: {\n                    // `inline: false` generates the source map in a separate file.\n                    // Otherwise, the CSS file is needlessly large.\n                    inline: false,\n                    // `annotation: false` skips appending the `sourceMappingURL`\n                    // to the end of the CSS file. Webpack already handles this.\n                    annotation: false,\n                  },\n                },\n              }).apply(compiler)\n            },\n          ],\n    },\n    context: dir,\n    // Kept as function to be backwards compatible\n    entry: async () => {\n      return {\n        ...(clientEntries ? clientEntries : {}),\n        ...entrypoints,\n      }\n    },\n    watchOptions: Object.freeze({\n      ...baseWatchOptions,\n      poll: config.watchOptions?.pollIntervalMs,\n    }),\n    output: {\n      // we must set publicPath to an empty value to override the default of\n      // auto which doesn't work in IE11\n      publicPath: `${\n        config.assetPrefix\n          ? config.assetPrefix.endsWith('/')\n            ? config.assetPrefix.slice(0, -1)\n            : config.assetPrefix\n          : ''\n      }/_next/`,\n      path: !dev && isNodeServer ? path.join(outputPath, 'chunks') : outputPath,\n      // On the server we don't use hashes\n      filename: isNodeOrEdgeCompilation\n        ? dev || isEdgeServer\n          ? `[name].js`\n          : `../[name].js`\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}[name]${\n            dev ? '' : appDir ? '-[chunkhash]' : '-[contenthash]'\n          }.js`,\n      library: isClient || isEdgeServer ? '_N_E' : undefined,\n      libraryTarget: isClient || isEdgeServer ? 'assign' : 'commonjs2',\n      hotUpdateChunkFilename: 'static/webpack/[id].[fullhash].hot-update.js',\n      hotUpdateMainFilename:\n        'static/webpack/[fullhash].[runtime].hot-update.json',\n      // This saves chunks with the name given via `import()`\n      chunkFilename: isNodeOrEdgeCompilation\n        ? '[name].js'\n        : `static/chunks/${isDevFallback ? 'fallback/' : ''}${\n            dev ? '[name]' : '[name].[contenthash]'\n          }.js`,\n      strictModuleExceptionHandling: true,\n      crossOriginLoading: crossOrigin,\n      // if `sources[number]` is not an absolute path, it's is resolved\n      // relative to the location of the source map file (https://tc39.es/source-map/#resolving-sources).\n      // However, Webpack's `resource-path` is relative to the app dir.\n      // TODO: Either `sourceRoot` should be populated with the root and then we can use `[resource-path]`\n      // or we need a way to resolve return `path.relative(sourceMapLocation, info.resourcePath)`\n      devtoolModuleFilenameTemplate: dev\n        ? '[absolute-resource-path]'\n        : undefined,\n      webassemblyModuleFilename: 'static/wasm/[modulehash].wasm',\n      hashFunction: 'xxhash64',\n      hashDigestLength: 16,\n    },\n    performance: false,\n    resolve: resolveConfig,\n    resolveLoader: {\n      // The loaders Next.js provides\n      alias: [\n        'error-loader',\n        'next-swc-loader',\n        'next-client-pages-loader',\n        'next-image-loader',\n        'next-metadata-image-loader',\n        'next-style-loader',\n        'next-flight-loader',\n        'next-flight-client-entry-loader',\n        'next-flight-action-entry-loader',\n        'next-flight-client-module-loader',\n        'next-flight-server-reference-proxy-loader',\n        'empty-loader',\n        'next-middleware-loader',\n        'next-edge-function-loader',\n        'next-edge-app-route-loader',\n        'next-edge-ssr-loader',\n        'next-middleware-asset-loader',\n        'next-middleware-wasm-loader',\n        'next-app-loader',\n        'next-route-loader',\n        'next-font-loader',\n        'next-invalid-import-error-loader',\n        'next-metadata-route-loader',\n        'modularize-import-loader',\n        'next-barrel-loader',\n        'next-error-browser-binary-loader',\n      ].reduce(\n        (alias, loader) => {\n          // using multiple aliases to replace `resolveLoader.modules`\n          alias[loader] = path.join(__dirname, 'webpack', 'loaders', loader)\n\n          return alias\n        },\n        {} as Record<string, string>\n      ),\n      modules: [\n        'node_modules',\n        ...nodePathList, // Support for NODE_PATH environment variable\n      ],\n      plugins: [],\n    },\n    module: {\n      rules: [\n        // Alias server-only and client-only to proper exports based on bundling layers\n        {\n          issuerLayer: {\n            or: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on client-only but allow server-only\n            alias: createServerOnlyClientOnlyAliases(true),\n          },\n        },\n        {\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          resolve: {\n            // Error on server-only but allow client-only\n            alias: createServerOnlyClientOnlyAliases(false),\n          },\n        },\n        // Detect server-only / client-only imports and error in build time\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.serverOnly,\n          },\n          options: {\n            message:\n              \"'client-only' cannot be imported from a Server Component module. It should only be used from a Client Component.\",\n          },\n        },\n        {\n          test: [\n            /^server-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]server-only[\\\\/]index/,\n          ],\n          loader: 'next-invalid-import-error-loader',\n          issuerLayer: {\n            not: [\n              ...WEBPACK_LAYERS.GROUP.serverOnly,\n              ...WEBPACK_LAYERS.GROUP.neutralTarget,\n            ],\n          },\n          options: {\n            message:\n              \"'server-only' cannot be imported from a Client Component module. It should only be used from a Server Component.\",\n          },\n        },\n        // Potential the bundle introduced into middleware and api can be poisoned by client-only\n        // but not being used, so we disabled the `client-only` erroring on these layers.\n        // `server-only` is still available.\n        {\n          test: [\n            /^client-only$/,\n            /next[\\\\/]dist[\\\\/]compiled[\\\\/]client-only[\\\\/]error/,\n          ],\n          loader: 'empty-loader',\n          issuerLayer: {\n            or: WEBPACK_LAYERS.GROUP.neutralTarget,\n          },\n        },\n        ...(isNodeServer\n          ? []\n          : [\n              {\n                test: /[\\\\/].*?\\.node$/,\n                loader: 'next-error-browser-binary-loader',\n              },\n            ]),\n        ...(hasAppDir\n          ? [\n              {\n                // Make sure that AsyncLocalStorage module instance is shared between server and client\n                // layers.\n                layer: WEBPACK_LAYERS.shared,\n                test: asyncStoragesRegex,\n              },\n              // Convert metadata routes to separate layer\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.metadataRoute\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n              {\n                // Ensure that the app page module is in the client layers, this\n                // enables React to work correctly for RSC.\n                layer: WEBPACK_LAYERS.serverSideRendering,\n                test: /next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]route-modules[\\\\/]app-page[\\\\/]module/,\n              },\n              {\n                issuerLayer: isWebpackBundledLayer,\n                resolve: {\n                  alias: createNextApiEsmAliases(),\n                },\n              },\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(true),\n                },\n              },\n              {\n                issuerLayer: isWebpackClientOnlyLayer,\n                resolve: {\n                  alias: createAppRouterApiAliases(false),\n                },\n              },\n            ]\n          : []),\n        ...(hasAppDir && !isClient\n          ? [\n              {\n                issuerLayer: isWebpackServerOnlyLayer,\n                test: {\n                  // Resolve it if it is a source code file, and it has NOT been\n                  // opted out of bundling.\n                  and: [\n                    aliasCodeConditionTest,\n                    {\n                      not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                    },\n                  ],\n                },\n                resourceQuery: {\n                  // Do not apply next-flight-loader to imports generated by the\n                  // next-metadata-image-loader, to avoid generating unnecessary\n                  // and conflicting entries in the flight client entry plugin.\n                  // These are already covered by the next-metadata-route-loader\n                  // entries.\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                  conditionNames: reactServerCondition,\n                  // If missing the alias override here, the default alias will be used which aliases\n                  // react to the direct file path, not the package name. In that case the condition\n                  // will be ignored completely.\n                  alias: createRSCAliases(bundledReactChannel, {\n                    // No server components profiling\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.reactServerComponents,\n                    isEdgeServer,\n                  }),\n                },\n                use: 'next-flight-loader',\n              },\n            ]\n          : []),\n        // TODO: FIXME: do NOT webpack 5 support with this\n        // x-ref: https://github.com/webpack/webpack/issues/11467\n        ...(!config.experimental.fullySpecified\n          ? [\n              {\n                test: /\\.m?js/,\n                resolve: {\n                  fullySpecified: false,\n                },\n              } as any,\n            ]\n          : []),\n        ...(hasAppDir && isEdgeServer\n          ? [\n              // The Edge bundle includes the server in its entrypoint, so it has to\n              // be in the SSR layer — here we convert the actual page request to\n              // the RSC layer via a webpack rule.\n              {\n                resourceQuery: new RegExp(\n                  WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                ),\n                layer: WEBPACK_LAYERS.reactServerComponents,\n              },\n            ]\n          : []),\n        ...(hasAppDir\n          ? [\n              {\n                // Alias react-dom for ReactDOM.preload usage.\n                // Alias react for switching between default set and share subset.\n                oneOf: [\n                  {\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    test: {\n                      // Resolve it if it is a source code file, and it has NOT been\n                      // opted out of bundling.\n                      and: [\n                        aliasCodeConditionTest,\n                        {\n                          not: [optOutBundlingPackageRegex, asyncStoragesRegex],\n                        },\n                      ],\n                    },\n                    resolve: {\n                      // It needs `conditionNames` here to require the proper asset,\n                      // when react is acting as dependency of compiled/react-dom.\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.reactServerComponents,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                  {\n                    test: aliasCodeConditionTest,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    resolve: {\n                      alias: createRSCAliases(bundledReactChannel, {\n                        reactProductionProfiling,\n                        layer: WEBPACK_LAYERS.serverSideRendering,\n                        isEdgeServer,\n                      }),\n                    },\n                  },\n                ],\n              },\n              {\n                test: aliasCodeConditionTest,\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                resolve: {\n                  alias: createRSCAliases(bundledReactChannel, {\n                    reactProductionProfiling,\n                    layer: WEBPACK_LAYERS.appPagesBrowser,\n                    isEdgeServer,\n                  }),\n                },\n              },\n            ]\n          : []),\n        // Do not apply react-refresh-loader to node_modules for app router browser layer\n        ...(hasAppDir && dev && isClient\n          ? [\n              {\n                test: codeCondition.test,\n                exclude: [\n                  // exclude unchanged modules from react-refresh\n                  codeCondition.exclude,\n                  transpilePackagesRegex,\n                  precompileRegex,\n                ],\n                issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                use: reactRefreshLoaders,\n                resolve: {\n                  mainFields: getMainField(compilerType, true),\n                },\n              },\n            ]\n          : []),\n        {\n          oneOf: [\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiNode,\n              use: apiRoutesLayerLoaders,\n              // In Node.js, switch back to normal URL handling.\n              // We won't bundle `new URL()` cases in Node.js bundler layer.\n              parser: {\n                url: true,\n              },\n            },\n            {\n              ...codeCondition,\n              issuerLayer: WEBPACK_LAYERS.apiEdge,\n              use: apiRoutesLayerLoaders,\n              // In Edge runtime, we leave the url handling by default.\n              // The new URL assets will be converted into edge assets through assets loader.\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.middleware,\n              use: middlewareLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.middleware,\n                  isEdgeServer,\n                }),\n              },\n            },\n            {\n              test: codeCondition.test,\n              issuerLayer: WEBPACK_LAYERS.instrument,\n              use: instrumentLayerLoaders,\n              resolve: {\n                mainFields: getMainField(compilerType, true),\n                conditionNames: reactServerCondition,\n                alias: createRSCAliases(bundledReactChannel, {\n                  reactProductionProfiling,\n                  layer: WEBPACK_LAYERS.instrument,\n                  isEdgeServer,\n                }),\n              },\n            },\n            ...(hasAppDir\n              ? [\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: isWebpackServerOnlyLayer,\n                    exclude: asyncStoragesRegex,\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    resourceQuery: new RegExp(\n                      WEBPACK_RESOURCE_QUERIES.edgeSSREntry\n                    ),\n                    use: appServerLayerLoaders,\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.appPagesBrowser,\n                    // Exclude the transpilation of the app layer due to compilation issues\n                    exclude: browserNonTranspileModules,\n                    use: appBrowserLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                  {\n                    test: codeCondition.test,\n                    issuerLayer: WEBPACK_LAYERS.serverSideRendering,\n                    exclude: asyncStoragesRegex,\n                    use: appSSRLayerLoaders,\n                    resolve: {\n                      mainFields: getMainField(compilerType, true),\n                    },\n                  },\n                ]\n              : []),\n            {\n              ...codeCondition,\n              use: [\n                ...reactRefreshLoaders,\n                defaultLoaders.babel,\n                reactCompilerLoader,\n              ].filter(Boolean),\n            },\n          ],\n        },\n\n        ...(!config.images.disableStaticImages\n          ? [\n              {\n                test: nextImageLoaderRegex,\n                loader: 'next-image-loader',\n                issuer: { not: regexLikeCss },\n                dependency: { not: ['url'] },\n                resourceQuery: {\n                  not: [\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadata),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataRoute),\n                    new RegExp(WEBPACK_RESOURCE_QUERIES.metadataImageMeta),\n                  ],\n                },\n                options: {\n                  isDev: dev,\n                  compilerType,\n                  basePath: config.basePath,\n                  assetPrefix: config.assetPrefix,\n                },\n              },\n            ]\n          : []),\n        ...(isEdgeServer\n          ? [\n              {\n                resolve: {\n                  fallback: {\n                    process: require.resolve('./polyfills/process'),\n                  },\n                },\n              },\n            ]\n          : isClient\n            ? [\n                {\n                  resolve: {\n                    fallback:\n                      config.experimental.fallbackNodePolyfills === false\n                        ? {\n                            assert: false,\n                            buffer: false,\n                            constants: false,\n                            crypto: false,\n                            domain: false,\n                            http: false,\n                            https: false,\n                            os: false,\n                            path: false,\n                            punycode: false,\n                            process: false,\n                            querystring: false,\n                            stream: false,\n                            string_decoder: false,\n                            sys: false,\n                            timers: false,\n                            tty: false,\n                            util: false,\n                            vm: false,\n                            zlib: false,\n                            events: false,\n                            setImmediate: false,\n                          }\n                        : {\n                            assert: require.resolve(\n                              'next/dist/compiled/assert'\n                            ),\n                            buffer: require.resolve(\n                              'next/dist/compiled/buffer'\n                            ),\n                            constants: require.resolve(\n                              'next/dist/compiled/constants-browserify'\n                            ),\n                            crypto: require.resolve(\n                              'next/dist/compiled/crypto-browserify'\n                            ),\n                            domain: require.resolve(\n                              'next/dist/compiled/domain-browser'\n                            ),\n                            http: require.resolve(\n                              'next/dist/compiled/stream-http'\n                            ),\n                            https: require.resolve(\n                              'next/dist/compiled/https-browserify'\n                            ),\n                            os: require.resolve(\n                              'next/dist/compiled/os-browserify'\n                            ),\n                            path: require.resolve(\n                              'next/dist/compiled/path-browserify'\n                            ),\n                            punycode: require.resolve(\n                              'next/dist/compiled/punycode'\n                            ),\n                            process: require.resolve('./polyfills/process'),\n                            // Handled in separate alias\n                            querystring: require.resolve(\n                              'next/dist/compiled/querystring-es3'\n                            ),\n                            stream: require.resolve(\n                              'next/dist/compiled/stream-browserify'\n                            ),\n                            string_decoder: require.resolve(\n                              'next/dist/compiled/string_decoder'\n                            ),\n                            sys: require.resolve('next/dist/compiled/util'),\n                            timers: require.resolve(\n                              'next/dist/compiled/timers-browserify'\n                            ),\n                            tty: require.resolve(\n                              'next/dist/compiled/tty-browserify'\n                            ),\n                            // Handled in separate alias\n                            // url: require.resolve('url'),\n                            util: require.resolve('next/dist/compiled/util'),\n                            vm: require.resolve(\n                              'next/dist/compiled/vm-browserify'\n                            ),\n                            zlib: require.resolve(\n                              'next/dist/compiled/browserify-zlib'\n                            ),\n                            events: require.resolve(\n                              'next/dist/compiled/events'\n                            ),\n                            setImmediate: require.resolve(\n                              'next/dist/compiled/setimmediate'\n                            ),\n                          },\n                  },\n                },\n              ]\n            : []),\n        {\n          // Mark `image-response.js` as side-effects free to make sure we can\n          // tree-shake it if not used.\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?server[\\\\/]og[\\\\/]image-response\\.js/,\n          sideEffects: false,\n        },\n        // Mark the action-client-wrapper module as side-effects free to make sure\n        // the individual transformed module of client action can be tree-shaken.\n        // This will make modules processed by `next-flight-server-reference-proxy-loader` become side-effects free,\n        // then on client side the module ids will become tree-shakable.\n        // e.g. the output of client action module will look like:\n        // `export { a } from 'next-flight-server-reference-proxy-loader?id=idOfA&name=a!\n        // `export { b } from 'next-flight-server-reference-proxy-loader?id=idOfB&name=b!\n        {\n          test: /[\\\\/]next[\\\\/]dist[\\\\/](esm[\\\\/])?build[\\\\/]webpack[\\\\/]loaders[\\\\/]next-flight-loader[\\\\/]action-client-wrapper\\.js/,\n          sideEffects: false,\n        },\n        {\n          // This loader rule should be before other rules, as it can output code\n          // that still contains `\"use client\"` or `\"use server\"` statements that\n          // needs to be re-transformed by the RSC compilers.\n          // This loader rule works like a bridge between user's import and\n          // the target module behind a package's barrel file. It reads SWC's\n          // analysis result from the previous loader, and directly returns the\n          // code that only exports values that are asked by the user.\n          test: /__barrel_optimize__/,\n          use: ({ resourceQuery }: { resourceQuery: string }) => {\n            const names = (\n              resourceQuery.match(/\\?names=([^&]+)/)?.[1] || ''\n            ).split(',')\n\n            return [\n              {\n                loader: 'next-barrel-loader',\n                options: {\n                  names,\n                  swcCacheDir: path.join(\n                    dir,\n                    config?.distDir ?? '.next',\n                    'cache',\n                    'swc'\n                  ),\n                },\n                // This is part of the request value to serve as the module key.\n                // The barrel loader are no-op re-exported modules keyed by\n                // export names.\n                ident: 'next-barrel-loader:' + resourceQuery,\n              },\n            ]\n          },\n        },\n        {\n          resolve: {\n            alias: {\n              next: NEXT_PROJECT_ROOT,\n            },\n          },\n        },\n      ],\n    },\n    plugins: [\n      isNodeServer &&\n        new webpack.NormalModuleReplacementPlugin(\n          /\\.\\/(.+)\\.shared-runtime$/,\n          function (resource) {\n            const moduleName = path.basename(\n              resource.request,\n              '.shared-runtime'\n            )\n            const layer = resource.contextInfo.issuerLayer\n            let runtime\n\n            switch (layer) {\n              case WEBPACK_LAYERS.serverSideRendering:\n              case WEBPACK_LAYERS.reactServerComponents:\n              case WEBPACK_LAYERS.appPagesBrowser:\n              case WEBPACK_LAYERS.actionBrowser:\n                runtime = 'app-page'\n                break\n              default:\n                runtime = 'pages'\n            }\n            resource.request = `next/dist/server/route-modules/${runtime}/vendored/contexts/${moduleName}`\n          }\n        ),\n      dev && new MemoryWithGcCachePlugin({ maxGenerations: 5 }),\n      dev &&\n        isClient &&\n        (isRspack\n          ? // eslint-disable-next-line\n            new (getRspackReactRefresh() as any)()\n          : new ReactRefreshWebpackPlugin(webpack)),\n      // Makes sure `Buffer` and `process` are polyfilled in client and flight bundles (same behavior as webpack 4)\n      (isClient || isEdgeServer) &&\n        new webpack.ProvidePlugin({\n          // Buffer is used by getInlineScriptSource\n          Buffer: [require.resolve('buffer'), 'Buffer'],\n          // Avoid process being overridden when in web run time\n          ...(isClient && { process: [require.resolve('process')] }),\n        }),\n      getDefineEnvPlugin({\n        isTurbopack: false,\n        config,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix,\n        hasRewrites,\n        isClient,\n        isEdgeServer,\n        isNodeOrEdgeCompilation,\n        isNodeServer,\n        middlewareMatchers,\n      }),\n      isClient &&\n        new ReactLoadablePlugin({\n          filename: REACT_LOADABLE_MANIFEST,\n          pagesDir,\n          appDir,\n          runtimeAsset: `server/${MIDDLEWARE_REACT_LOADABLE_MANIFEST}.js`,\n          dev,\n        }),\n      // rspack doesn't support the parser hooks used here\n      !isRspack && (isClient || isEdgeServer) && new DropClientPage(),\n      isNodeServer &&\n        !dev &&\n        new (require('./webpack/plugins/next-trace-entrypoints-plugin')\n          .TraceEntryPointsPlugin as typeof import('./webpack/plugins/next-trace-entrypoints-plugin').TraceEntryPointsPlugin)(\n          {\n            rootDir: dir,\n            appDir: appDir,\n            pagesDir: pagesDir,\n            esmExternals: config.experimental.esmExternals,\n            outputFileTracingRoot: config.outputFileTracingRoot,\n            appDirEnabled: hasAppDir,\n            traceIgnores: [],\n            compilerType,\n          }\n        ),\n      // Moment.js is an extremely popular library that bundles large locale files\n      // by default due to how Webpack interprets its code. This is a practical\n      // solution that requires the user to opt into importing specific locales.\n      // https://github.com/jmblog/how-to-optimize-momentjs-with-webpack\n      config.excludeDefaultMomentLocales &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /^\\.\\/locale$/,\n          contextRegExp: /moment$/,\n        }),\n      ...(dev\n        ? (() => {\n            // Even though require.cache is server only we have to clear assets from both compilations\n            // This is because the client compilation generates the build manifest that's used on the server side\n            const { NextJsRequireCacheHotReloader } =\n              require('./webpack/plugins/nextjs-require-cache-hot-reloader') as typeof import('./webpack/plugins/nextjs-require-cache-hot-reloader')\n            const devPlugins: any[] = [\n              new NextJsRequireCacheHotReloader({\n                serverComponents: hasAppDir,\n              }),\n            ]\n\n            if (isClient || isEdgeServer) {\n              devPlugins.push(new webpack.HotModuleReplacementPlugin())\n            }\n\n            return devPlugins\n          })()\n        : []),\n      !dev &&\n        new webpack.IgnorePlugin({\n          resourceRegExp: /react-is/,\n          contextRegExp: /next[\\\\/]dist[\\\\/]/,\n        }),\n      isNodeOrEdgeCompilation &&\n        new PagesManifestPlugin({\n          dev,\n          appDirEnabled: hasAppDir,\n          isEdgeRuntime: isEdgeServer,\n          distDir: !dev ? distDir : undefined,\n        }),\n      // MiddlewarePlugin should be after DefinePlugin so NEXT_PUBLIC_*\n      // replacement is done before its process.env.* handling\n      isEdgeServer &&\n        new MiddlewarePlugin({\n          dev,\n          sriEnabled: !dev && !!config.experimental.sri?.algorithm,\n          rewrites,\n          edgeEnvironments: {\n            __NEXT_BUILD_ID: buildId,\n            NEXT_SERVER_ACTIONS_ENCRYPTION_KEY: encryptionKey,\n            ...edgePreviewProps,\n          },\n        }),\n      isClient &&\n        new BuildManifestPlugin({\n          buildId,\n          rewrites,\n          isDevFallback,\n          appDirEnabled: hasAppDir,\n          clientRouterFilters,\n        }),\n      isRspack\n        ? new RspackProfilingPlugin({ runWebpackSpan })\n        : new ProfilingPlugin({ runWebpackSpan, rootDir: dir }),\n      new WellKnownErrorsPlugin(),\n      isClient &&\n        new CopyFilePlugin({\n          // file path to build output of `@next/polyfill-nomodule`\n          filePath: require.resolve('./polyfills/polyfill-nomodule'),\n          cacheKey: process.env.__NEXT_VERSION as string,\n          name: `static/chunks/polyfills${dev ? '' : '-[hash]'}.js`,\n          minimize: false,\n          info: {\n            [CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL]: 1,\n            // This file is already minified\n            minimized: true,\n          },\n        }),\n      hasAppDir && isClient && new AppBuildManifestPlugin({ dev }),\n      hasAppDir &&\n        (isClient\n          ? new ClientReferenceManifestPlugin({\n              dev,\n              appDir,\n              experimentalInlineCss: !!config.experimental.inlineCss,\n            })\n          : new FlightClientEntryPlugin({\n              appDir,\n              dev,\n              isEdgeServer,\n              encryptionKey,\n            })),\n      hasAppDir &&\n        !isClient &&\n        new NextTypesPlugin({\n          dir,\n          distDir: config.distDir,\n          appDir,\n          dev,\n          isEdgeServer,\n          pageExtensions: config.pageExtensions,\n          typedRoutes: enableTypedRoutes,\n          cacheLifeConfig: config.experimental.cacheLife,\n          originalRewrites,\n          originalRedirects,\n        }),\n      !dev &&\n        isClient &&\n        !!config.experimental.sri?.algorithm &&\n        new SubresourceIntegrityPlugin(config.experimental.sri.algorithm),\n      isClient &&\n        new NextFontManifestPlugin({\n          appDir,\n        }),\n      !isRspack &&\n        !dev &&\n        isClient &&\n        config.experimental.cssChunking &&\n        new CssChunkingPlugin(config.experimental.cssChunking === 'strict'),\n      telemetryPlugin,\n      !isRspack &&\n        !dev &&\n        isNodeServer &&\n        new (\n          require('./webpack/plugins/telemetry-plugin/telemetry-plugin') as typeof import('./webpack/plugins/telemetry-plugin/telemetry-plugin')\n        ).TelemetryPlugin(new Map()),\n      shouldEnableSlowModuleDetection &&\n        new (\n          require('./webpack/plugins/slow-module-detection-plugin') as typeof import('./webpack/plugins/slow-module-detection-plugin')\n        ).default({\n          compilerType,\n          ...config.experimental.slowModuleDetection!,\n        }),\n    ].filter(Boolean as any as ExcludesFalse),\n  }\n\n  // Support tsconfig and jsconfig baseUrl\n  // Only add the baseUrl if it's explicitly set in tsconfig/jsconfig\n  if (resolvedBaseUrl && !resolvedBaseUrl.isImplicit) {\n    webpackConfig.resolve?.modules?.push(resolvedBaseUrl.baseUrl)\n  }\n\n  // always add JsConfigPathsPlugin to allow hot-reloading\n  // if the config is added/removed\n  webpackConfig.resolve?.plugins?.unshift(\n    new JsConfigPathsPlugin(\n      jsConfig?.compilerOptions?.paths || {},\n      resolvedBaseUrl\n    )\n  )\n\n  const webpack5Config = webpackConfig as webpack.Configuration\n\n  if (isEdgeServer) {\n    webpack5Config.module?.rules?.unshift({\n      test: /\\.wasm$/,\n      loader: 'next-middleware-wasm-loader',\n      type: 'javascript/auto',\n      resourceQuery: /module/i,\n    })\n    webpack5Config.module?.rules?.unshift({\n      dependency: 'url',\n      loader: 'next-middleware-asset-loader',\n      type: 'javascript/auto',\n      layer: WEBPACK_LAYERS.edgeAsset,\n    })\n    webpack5Config.module?.rules?.unshift({\n      issuerLayer: WEBPACK_LAYERS.edgeAsset,\n      type: 'asset/source',\n    })\n  }\n\n  webpack5Config.experiments = {\n    layers: true,\n    cacheUnaffected: true,\n    buildHttp: Array.isArray(config.experimental.urlImports)\n      ? {\n          allowedUris: config.experimental.urlImports,\n          cacheLocation: path.join(dir, 'next.lock/data'),\n          lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n        }\n      : config.experimental.urlImports\n        ? {\n            cacheLocation: path.join(dir, 'next.lock/data'),\n            lockfileLocation: path.join(dir, 'next.lock/lock.json'),\n            ...config.experimental.urlImports,\n          }\n        : undefined,\n  }\n\n  webpack5Config.module!.parser = {\n    javascript: {\n      url: 'relative',\n    },\n  }\n  webpack5Config.module!.generator = {\n    asset: {\n      filename: 'static/media/[name].[hash:8][ext]',\n    },\n  }\n\n  if (!webpack5Config.output) {\n    webpack5Config.output = {}\n  }\n  if (isClient) {\n    webpack5Config.output.trustedTypes = 'nextjs#bundler'\n  }\n\n  if (isClient || isEdgeServer) {\n    webpack5Config.output.enabledLibraryTypes = ['assign']\n  }\n\n  // This enables managedPaths for all node_modules\n  // and also for the unplugged folder when using yarn pnp\n  // It also add the yarn cache to the immutable paths\n  webpack5Config.snapshot = {}\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.managedPaths = [\n      /^(.+?(?:[\\\\/]\\.yarn[\\\\/]unplugged[\\\\/][^\\\\/]+)?[\\\\/]node_modules[\\\\/])/,\n    ]\n  } else {\n    webpack5Config.snapshot.managedPaths = [/^(.+?[\\\\/]node_modules[\\\\/])/]\n  }\n  if (process.versions.pnp === '3') {\n    webpack5Config.snapshot.immutablePaths = [\n      /^(.+?[\\\\/]cache[\\\\/][^\\\\/]+\\.zip[\\\\/]node_modules[\\\\/])/,\n    ]\n  }\n\n  if (dev) {\n    if (!webpack5Config.optimization) {\n      webpack5Config.optimization = {}\n    }\n\n    // For Server Components, it's necessary to have provided exports collected\n    // to generate the correct flight manifest.\n    if (!hasAppDir) {\n      webpack5Config.optimization.providedExports = false\n    }\n    webpack5Config.optimization.usedExports = false\n  }\n\n  const configVars = JSON.stringify({\n    optimizePackageImports: config?.experimental?.optimizePackageImports,\n    crossOrigin: config.crossOrigin,\n    pageExtensions: pageExtensions,\n    trailingSlash: config.trailingSlash,\n    buildActivityPosition:\n      config.devIndicators === false\n        ? undefined\n        : config.devIndicators.position,\n    productionBrowserSourceMaps: !!config.productionBrowserSourceMaps,\n    reactStrictMode: config.reactStrictMode,\n    optimizeCss: config.experimental.optimizeCss,\n    nextScriptWorkers: config.experimental.nextScriptWorkers,\n    scrollRestoration: config.experimental.scrollRestoration,\n    typedRoutes: config.experimental.typedRoutes,\n    basePath: config.basePath,\n    excludeDefaultMomentLocales: config.excludeDefaultMomentLocales,\n    assetPrefix: config.assetPrefix,\n    disableOptimizedLoading,\n    isEdgeRuntime: isEdgeServer,\n    reactProductionProfiling,\n    webpack: !!config.webpack,\n    hasRewrites,\n    swcLoader: useSWCLoader,\n    removeConsole: config.compiler?.removeConsole,\n    reactRemoveProperties: config.compiler?.reactRemoveProperties,\n    styledComponents: config.compiler?.styledComponents,\n    relay: config.compiler?.relay,\n    emotion: config.compiler?.emotion,\n    modularizeImports: config.modularizeImports,\n    imageLoaderFile: config.images.loaderFile,\n    clientTraceMetadata: config.experimental.clientTraceMetadata,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n    serverReferenceHashSalt: encryptionKey,\n  })\n\n  const cache: any = {\n    type: 'filesystem',\n    // Disable memory cache in development in favor of our own MemoryWithGcCachePlugin.\n    maxMemoryGenerations: dev ? 0 : Infinity, // Infinity is default value for production in webpack currently.\n    // Includes:\n    //  - Next.js location on disk (some loaders use absolute paths and some resolve options depend on absolute paths)\n    //  - Next.js version\n    //  - next.config.js keys that affect compilation\n    version: `${__dirname}|${process.env.__NEXT_VERSION}|${configVars}`,\n    cacheDirectory: path.join(distDir, 'cache', 'webpack'),\n    // For production builds, it's more efficient to compress all cache files together instead of compression each one individually.\n    // So we disable compression here and allow the build runner to take care of compressing the cache as a whole.\n    // For local development, we still want to compress the cache files individually to avoid I/O bottlenecks\n    // as we are seeing 1~10 seconds of fs I/O time from user reports.\n    compression: dev ? 'gzip' : false,\n  }\n\n  // Adds `next.config.js` as a buildDependency when custom webpack config is provided\n  if (config.webpack && config.configFile) {\n    cache.buildDependencies = {\n      config: [config.configFile],\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  } else {\n    cache.buildDependencies = {\n      // We don't want to use the webpack default buildDependencies as we already include the next.js version\n      defaultWebpack: [],\n    }\n  }\n  webpack5Config.plugins?.push((compiler) => {\n    compiler.hooks.done.tap('next-build-dependencies', (stats) => {\n      const buildDependencies = stats.compilation.buildDependencies\n      const nextPackage = path.dirname(require.resolve('next/package.json'))\n      // Remove all next.js build dependencies, they are already covered by the cacheVersion\n      // and next.js also imports the output files which leads to broken caching.\n      for (const dep of buildDependencies) {\n        if (dep.startsWith(nextPackage)) {\n          buildDependencies.delete(dep)\n        }\n      }\n    })\n  })\n\n  webpack5Config.cache = cache\n\n  if (process.env.NEXT_WEBPACK_LOGGING) {\n    const infra = process.env.NEXT_WEBPACK_LOGGING.includes('infrastructure')\n    const profileClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-client')\n    const profileServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('profile-server')\n    const summaryClient =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-client')\n    const summaryServer =\n      process.env.NEXT_WEBPACK_LOGGING.includes('summary-server')\n\n    const profile =\n      (profileClient && isClient) || (profileServer && isNodeOrEdgeCompilation)\n    const summary =\n      (summaryClient && isClient) || (summaryServer && isNodeOrEdgeCompilation)\n\n    const logDefault = !infra && !profile && !summary\n\n    if (logDefault || infra) {\n      webpack5Config.infrastructureLogging = {\n        level: 'verbose',\n        debug: /FileSystemInfo/,\n      }\n    }\n\n    if (logDefault || profile) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              colors: true,\n              logging: logDefault ? 'log' : 'verbose',\n            })\n          )\n        })\n      })\n    } else if (summary) {\n      webpack5Config.plugins!.push((compiler: webpack.Compiler) => {\n        compiler.hooks.done.tap('next-webpack-logging', (stats) => {\n          console.log(\n            stats.toString({\n              preset: 'summary',\n              colors: true,\n              timings: true,\n            })\n          )\n        })\n      })\n    }\n\n    if (profile) {\n      const ProgressPlugin =\n        webpack.ProgressPlugin as unknown as typeof webpack.ProgressPlugin\n      webpack5Config.plugins!.push(\n        new ProgressPlugin({\n          profile: true,\n        })\n      )\n      webpack5Config.profile = true\n    }\n  }\n\n  webpackConfig = await buildConfiguration(webpackConfig, {\n    supportedBrowsers,\n    rootDirectory: dir,\n    customAppFile: pagesDir\n      ? new RegExp(escapeStringRegexp(path.join(pagesDir, `_app`)))\n      : undefined,\n    hasAppDir,\n    isDevelopment: dev,\n    isServer: isNodeOrEdgeCompilation,\n    isEdgeRuntime: isEdgeServer,\n    targetWeb: isClient || isEdgeServer,\n    assetPrefix: config.assetPrefix || '',\n    sassOptions: config.sassOptions,\n    productionBrowserSourceMaps: config.productionBrowserSourceMaps,\n    future: config.future,\n    experimental: config.experimental,\n    disableStaticImages: config.images.disableStaticImages,\n    transpilePackages: config.transpilePackages,\n    serverSourceMaps: config.experimental.serverSourceMaps,\n  })\n\n  // @ts-ignore Cache exists\n  webpackConfig.cache.name = `${webpackConfig.name}-${webpackConfig.mode}${\n    isDevFallback ? '-fallback' : ''\n  }`\n\n  if (dev) {\n    if (webpackConfig.module) {\n      webpackConfig.module.unsafeCache = (module: any) =>\n        !UNSAFE_CACHE_REGEX.test(module.resource)\n    } else {\n      webpackConfig.module = {\n        unsafeCache: (module: any) => !UNSAFE_CACHE_REGEX.test(module.resource),\n      }\n    }\n  }\n\n  let originalDevtool = webpackConfig.devtool\n  if (typeof config.webpack === 'function') {\n    const pluginCountBefore = webpackConfig.plugins?.length\n\n    webpackConfig = config.webpack(webpackConfig, {\n      dir,\n      dev,\n      isServer: isNodeOrEdgeCompilation,\n      buildId,\n      config,\n      defaultLoaders,\n      totalPages: Object.keys(entrypoints).length,\n      webpack,\n      ...(isNodeOrEdgeCompilation\n        ? {\n            nextRuntime: isEdgeServer ? 'edge' : 'nodejs',\n          }\n        : {}),\n    })\n\n    if (telemetryPlugin && pluginCountBefore) {\n      const pluginCountAfter = webpackConfig.plugins?.length\n      if (pluginCountAfter) {\n        const pluginsChanged = pluginCountAfter !== pluginCountBefore\n        telemetryPlugin.addUsage('webpackPlugins', pluginsChanged ? 1 : 0)\n      }\n    }\n\n    if (!webpackConfig) {\n      throw new Error(\n        `Webpack config is undefined. You may have forgot to return properly from within the \"webpack\" method of your ${config.configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/undefined-webpack-config'\n      )\n    }\n\n    if (dev && originalDevtool !== webpackConfig.devtool) {\n      webpackConfig.devtool = originalDevtool\n      devtoolRevertWarning(originalDevtool)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-shadow\n    const webpack5Config = webpackConfig as webpack.Configuration\n\n    // disable lazy compilation of entries as next.js has it's own method here\n    if (webpack5Config.experiments?.lazyCompilation === true) {\n      webpack5Config.experiments.lazyCompilation = {\n        entries: false,\n      }\n    } else if (\n      typeof webpack5Config.experiments?.lazyCompilation === 'object' &&\n      webpack5Config.experiments.lazyCompilation.entries !== false\n    ) {\n      webpack5Config.experiments.lazyCompilation.entries = false\n    }\n\n    if (typeof (webpackConfig as any).then === 'function') {\n      console.warn(\n        '> Promise returned in next config. https://nextjs.org/docs/messages/promise-in-next-config'\n      )\n    }\n  }\n  const rules = webpackConfig.module?.rules || []\n\n  const customSvgRule = rules.find(\n    (rule): rule is webpack.RuleSetRule =>\n      (rule &&\n        typeof rule === 'object' &&\n        rule.loader !== 'next-image-loader' &&\n        'test' in rule &&\n        rule.test instanceof RegExp &&\n        rule.test.test('.svg')) ||\n      false\n  )\n\n  if (customSvgRule && hasAppDir) {\n    // Create React aliases for SVG components that were transformed using a\n    // custom webpack config with e.g. the `@svgr/webpack` loader, or the\n    // `babel-plugin-inline-react-svg` plugin.\n    rules.push({\n      test: customSvgRule.test,\n      oneOf: [\n        WEBPACK_LAYERS.reactServerComponents,\n        WEBPACK_LAYERS.serverSideRendering,\n        WEBPACK_LAYERS.appPagesBrowser,\n      ].map((layer) => ({\n        issuerLayer: layer,\n        resolve: {\n          alias: createRSCAliases(bundledReactChannel, {\n            reactProductionProfiling,\n            layer,\n            isEdgeServer,\n          }),\n        },\n      })),\n    })\n  }\n\n  if (!config.images.disableStaticImages) {\n    const nextImageRule = rules.find(\n      (rule) =>\n        rule && typeof rule === 'object' && rule.loader === 'next-image-loader'\n    )\n    if (customSvgRule && nextImageRule && typeof nextImageRule === 'object') {\n      // Exclude svg if the user already defined it in custom\n      // webpack config such as the `@svgr/webpack` loader, or\n      // the `babel-plugin-inline-react-svg` plugin.\n      nextImageRule.test = /\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp)$/i\n    }\n  }\n\n  if (\n    config.experimental.craCompat &&\n    webpackConfig.module?.rules &&\n    webpackConfig.plugins\n  ) {\n    // CRA allows importing non-webpack handled files with file-loader\n    // these need to be the last rule to prevent catching other items\n    // https://github.com/facebook/create-react-app/blob/fddce8a9e21bf68f37054586deb0c8636a45f50b/packages/react-scripts/config/webpack.config.js#L594\n    const fileLoaderExclude = [/\\.(js|mjs|jsx|ts|tsx|json)$/]\n    const fileLoader = {\n      exclude: fileLoaderExclude,\n      issuer: fileLoaderExclude,\n      type: 'asset/resource',\n    }\n\n    const topRules = []\n    const innerRules = []\n\n    for (const rule of webpackConfig.module.rules) {\n      if (!rule || typeof rule !== 'object') continue\n      if (rule.resolve) {\n        topRules.push(rule)\n      } else {\n        if (\n          rule.oneOf &&\n          !(rule.test || rule.exclude || rule.resource || rule.issuer)\n        ) {\n          rule.oneOf.forEach((r) => innerRules.push(r))\n        } else {\n          innerRules.push(rule)\n        }\n      }\n    }\n\n    webpackConfig.module.rules = [\n      ...(topRules as any),\n      {\n        oneOf: [...innerRules, fileLoader],\n      },\n    ]\n  }\n\n  // Backwards compat with webpack-dev-middleware options object\n  if (typeof config.webpackDevMiddleware === 'function') {\n    const options = config.webpackDevMiddleware({\n      watchOptions: webpackConfig.watchOptions,\n    })\n    if (options.watchOptions) {\n      webpackConfig.watchOptions = options.watchOptions\n    }\n  }\n\n  function canMatchCss(rule: webpack.RuleSetCondition | undefined): boolean {\n    if (!rule) {\n      return false\n    }\n\n    const fileNames = [\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.css',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.scss',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.sass',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.less',\n      '/tmp/NEXTJS_CSS_DETECTION_FILE.styl',\n    ]\n\n    if (rule instanceof RegExp && fileNames.some((input) => rule.test(input))) {\n      return true\n    }\n\n    if (typeof rule === 'function') {\n      if (\n        fileNames.some((input) => {\n          try {\n            if (rule(input)) {\n              return true\n            }\n          } catch {}\n          return false\n        })\n      ) {\n        return true\n      }\n    }\n\n    if (Array.isArray(rule) && rule.some(canMatchCss)) {\n      return true\n    }\n\n    return false\n  }\n\n  const hasUserCssConfig =\n    webpackConfig.module?.rules?.some(\n      (rule: any) => canMatchCss(rule.test) || canMatchCss(rule.include)\n    ) ?? false\n\n  if (hasUserCssConfig) {\n    // only show warning for one build\n    if (isNodeOrEdgeCompilation) {\n      console.warn(\n        yellow(bold('Warning: ')) +\n          bold(\n            'Built-in CSS support is being disabled due to custom CSS configuration being detected.\\n'\n          ) +\n          'See here for more info: https://nextjs.org/docs/messages/built-in-css-disabled\\n'\n      )\n    }\n\n    if (webpackConfig.module?.rules?.length) {\n      // Remove default CSS Loaders\n      webpackConfig.module.rules.forEach((r) => {\n        if (!r || typeof r !== 'object') return\n        if (Array.isArray(r.oneOf)) {\n          r.oneOf = r.oneOf.filter(\n            (o) => (o as any)[Symbol.for('__next_css_remove')] !== true\n          )\n        }\n      })\n    }\n    if (webpackConfig.plugins?.length) {\n      // Disable CSS Extraction Plugin\n      webpackConfig.plugins = webpackConfig.plugins.filter(\n        (p) => (p as any).__next_css_remove !== true\n      )\n    }\n    if (webpackConfig.optimization?.minimizer?.length) {\n      // Disable CSS Minifier\n      webpackConfig.optimization.minimizer =\n        webpackConfig.optimization.minimizer.filter(\n          (e) => (e as any).__next_css_remove !== true\n        )\n    }\n  }\n\n  // Inject missing React Refresh loaders so that development mode is fast:\n  if (dev && isClient) {\n    attachReactRefresh(webpackConfig, defaultLoaders.babel)\n  }\n\n  // Backwards compat for `main.js` entry key\n  // and setup of dependencies between entries\n  // we can't do that in the initial entry for\n  // backward-compat reasons\n  const originalEntry: any = webpackConfig.entry\n  if (typeof originalEntry !== 'undefined') {\n    const updatedEntry = async () => {\n      const entry: webpack.EntryObject =\n        typeof originalEntry === 'function'\n          ? await originalEntry()\n          : originalEntry\n      // Server compilation doesn't have main.js\n      if (\n        clientEntries &&\n        Array.isArray(entry['main.js']) &&\n        entry['main.js'].length > 0\n      ) {\n        const originalFile = clientEntries[\n          CLIENT_STATIC_FILES_RUNTIME_MAIN\n        ] as string\n        entry[CLIENT_STATIC_FILES_RUNTIME_MAIN] = [\n          ...entry['main.js'],\n          originalFile,\n        ]\n      }\n      delete entry['main.js']\n\n      for (const name of Object.keys(entry)) {\n        entry[name] = finalizeEntrypoint({\n          value: entry[name],\n          compilerType,\n          name,\n          hasAppDir,\n        })\n      }\n\n      return entry\n    }\n    // @ts-ignore webpack 5 typings needed\n    webpackConfig.entry = updatedEntry\n  }\n\n  if (!dev && typeof webpackConfig.entry === 'function') {\n    // entry is always a function\n    webpackConfig.entry = await webpackConfig.entry()\n  }\n\n  return webpackConfig\n}\n"], "names": ["NODE_BASE_ESM_RESOLVE_OPTIONS", "NODE_BASE_RESOLVE_OPTIONS", "NODE_ESM_RESOLVE_OPTIONS", "NODE_RESOLVE_OPTIONS", "attachReactRefresh", "babelIncludeRegexes", "getBaseWebpackConfig", "hasExternalOtelApiPackage", "loadProjectInfo", "nextImageLoaderRegex", "EXTERNAL_PACKAGES", "require", "DEFAULT_TRANSPILED_PACKAGES", "parseInt", "React", "version", "Error", "browserNonTranspileModules", "precompileRegex", "asyncStoragesRegex", "nodePathList", "process", "env", "NODE_PATH", "split", "platform", "filter", "p", "baseWatchOptions", "Object", "freeze", "aggregateTimeout", "ignored", "isModuleCSS", "module", "type", "devtoolRevertWarning", "execOnce", "devtool", "console", "warn", "yellow", "bold", "loggedSwcDisabled", "loggedIgnoredCompilerOptions", "reactRefreshLoaderName", "webpackConfig", "target<PERSON><PERSON><PERSON>", "reactRefreshLoader", "resolve", "rules", "for<PERSON>ach", "rule", "curr", "use", "Array", "isArray", "some", "r", "idx", "findIndex", "splice", "dependencyType", "modules", "fallback", "exportsFields", "importsFields", "conditionNames", "descriptionFiles", "extensions", "enforceExtensions", "symlinks", "mainFields", "mainFiles", "roots", "fullySpecified", "preferRelative", "preferAbsolute", "restrictions", "alias", "dir", "config", "dev", "jsConfig", "jsConfigPath", "resolvedBaseUrl", "loadJsConfig", "supportedBrowsers", "getSupportedBrowsers", "UNSAFE_CACHE_REGEX", "buildId", "<PERSON><PERSON><PERSON>", "compilerType", "entrypoints", "isDev<PERSON><PERSON><PERSON>", "pagesDir", "reactProductionProfiling", "rewrites", "originalRewrites", "originalRedirects", "runWebpackSpan", "appDir", "middlewareMatchers", "noMangling", "clientRouterFilters", "fetchCacheKeyPrefix", "edgePreviewProps", "webpack5Config", "isClient", "COMPILER_NAMES", "client", "isEdgeServer", "edgeServer", "isNodeServer", "server", "isRspack", "Boolean", "NEXT_RSPACK", "FlightClientEntryPlugin", "BUILTIN_FLIGHT_CLIENT_ENTRY_PLUGIN", "RspackFlightClientEntryPlugin", "NextFlightClientEntryPlugin", "isNodeOrEdgeCompilation", "hasRewrites", "beforeFiles", "length", "afterFiles", "hasAppDir", "disableOptimizedLoading", "enableTypedRoutes", "experimental", "typedRoutes", "bundledReactChannel", "needsExperimentalReact", "babelConfigFile", "getBabelConfigFile", "hasCustomExportOutput", "distDir", "path", "join", "useSWCLoader", "forceSwcTransforms", "SWCBinaryTarget", "undefined", "binaryTarget", "getBinaryMetadata", "target", "Log", "info", "relative", "loadBindings", "useWasmBinary", "finalTranspilePackages", "transpilePackages", "concat", "pkg", "optimizePackageImports", "includes", "push", "compiler", "shouldIncludeExternalDirs", "externalDir", "codeCondition", "test", "or", "include", "exclude", "excludePath", "shouldBeBundled", "isResourceInPackages", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactCompiler", "reactCompilerLoader", "getReactCompilerLoader", "swcTraceProfilingInitialized", "getSwcLoader", "extraOptions", "swcTraceProfiling", "initCustomTraceSubscriber", "Date", "now", "useBuiltinSwcLoader", "BUILTIN_SWC_LOADER", "loader", "options", "isServer", "rootDir", "hasReactRefresh", "swcCacheDir", "serverReferenceHashSalt", "pnp", "versions", "optimizeServerReact", "modularizeImports", "decorators", "compilerOptions", "experimentalDecorators", "emitDecoratorMetadata", "regeneratorRuntimePath", "nextConfig", "swcServerLayerLoader", "serverComponents", "bundleLayer", "WEBPACK_LAYERS", "reactServerComponents", "esm", "swcSS<PERSON>ayer<PERSON><PERSON>der", "serverSideRendering", "swcBrowser<PERSON><PERSON><PERSON><PERSON><PERSON>der", "appPagesBrowser", "swcDefaultLoader", "defaultLoaders", "babel", "appServerLayerLoaders", "instrumentLayerLoaders", "middlewareLayerLoaders", "middleware", "reactRefreshLoaders", "createClientLayerLoader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactRefresh", "appBrowserLayerLoaders", "appSSRLayerLoaders", "apiRoutesLayerLoaders", "apiNode", "pageExtensions", "outputPath", "SERVER_DIRECTORY", "reactServerCondition", "edgeConditionNames", "clientEntries", "CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH", "CLIENT_STATIC_FILES_RUNTIME_AMP", "NEXT_PROJECT_ROOT_DIST_CLIENT", "replace", "CLIENT_STATIC_FILES_RUNTIME_MAIN", "CLIENT_STATIC_FILES_RUNTIME_MAIN_APP", "resolveConfig", "extensionAlias", "createWebpackAliases", "getMainField", "plugins", "OptionalPeerDependencyResolverPlugin", "tsConfig", "configFile", "<PERSON><PERSON><PERSON>eworkP<PERSON><PERSON>", "topLevelFrameworkPaths", "visitedFrameworkPackages", "Set", "addPackagePath", "packageName", "relativeToPath", "paths", "has", "add", "packageJsonPath", "directory", "dependencies", "name", "keys", "_", "crossOrigin", "serverExternalPackages", "externalPackageConflicts", "optOutBundlingPackages", "optOutBundlingPackageRegex", "RegExp", "map", "transpilePackagesRegex", "handleExternals", "makeExternalHandler", "transpiledPackages", "pageExtensionsRegex", "aliasCodeConditionTest", "builtinModules", "shouldEnableSlowModuleDetection", "slowModuleDetection", "getParallelism", "override", "Number", "NEXT_WEBPACK_PARALLELISM", "telemetryPlugin", "TelemetryPlugin", "Map", "relay", "styledComponents", "reactRemoveProperties", "removeConsole", "jsxImportSource", "emotion", "skipMiddlewareUrlNormalize", "skipTrailingSlashRedirect", "esmExternals", "parallelism", "externalsPresets", "node", "externals", "getEdgePolyfilledModules", "handleWebpackExternalForEdgeRuntime", "context", "request", "contextInfo", "getResolve", "issuer<PERSON><PERSON>er", "resolveFunction", "resolveContext", "requestToResolve", "Promise", "reject", "err", "result", "resolveData", "isEsm", "descriptionFileData", "optimization", "emitOnErrors", "checkWasmTypes", "nodeEnv", "splitChunks", "extractRootNodeModule", "modulePath", "regex", "match", "cacheGroups", "vendor", "chunks", "reuseExistingChunk", "minSize", "minChunks", "maxAsyncRequests", "maxInitialRequests", "moduleId", "nameForCondition", "rootModule", "hash", "crypto", "createHash", "update", "digest", "default", "defaultVendors", "filename", "frameworkCacheGroup", "layer", "isWebpackDefaultLayer", "resource", "pkgPath", "startsWith", "priority", "enforce", "libCacheGroup", "size", "updateHash", "libIdent", "substring", "chunk", "framework", "lib", "runtimeChunk", "CLIENT_STATIC_FILES_RUNTIME_WEBPACK", "minimize", "serverMinification", "minimizer", "webpack", "SwcJsMinimizerRspackPlugin", "LightningCssMinimizerRspackPlugin", "MinifyPlugin", "apply", "CssMinimizerPlugin", "postcssOptions", "inline", "annotation", "entry", "watchOptions", "poll", "pollIntervalMs", "output", "publicPath", "assetPrefix", "endsWith", "slice", "library", "libraryTarget", "hotUpdateChunkFilename", "hotUpdateMainFilename", "chunkFilename", "strictModuleExceptionHandling", "crossOriginLoading", "devtoolModuleFilenameTemplate", "webassemblyModuleFilename", "hashFunction", "hashDigestLength", "performance", "<PERSON><PERSON><PERSON><PERSON>", "reduce", "__dirname", "GROUP", "serverOnly", "neutralTarget", "createServerOnlyClientOnlyAliases", "not", "message", "shared", "resourceQuery", "WEBPACK_RESOURCE_QUERIES", "metadataRoute", "isWebpackBundledLayer", "createNextApiEsmAliases", "isWebpackServerOnlyLayer", "createAppRouterApiAliases", "isWebpackClientOnlyLayer", "and", "metadata", "metadataImageMeta", "createRSCAliases", "edgeSSREntry", "oneOf", "parser", "url", "apiEdge", "instrument", "images", "disableStaticImages", "issuer", "regexLikeCss", "dependency", "isDev", "basePath", "fallbackNodePolyfills", "assert", "buffer", "constants", "domain", "http", "https", "os", "punycode", "querystring", "stream", "string_decoder", "sys", "timers", "tty", "util", "vm", "zlib", "events", "setImmediate", "sideEffects", "names", "ident", "next", "NEXT_PROJECT_ROOT", "NormalModuleReplacementPlugin", "moduleName", "basename", "runtime", "<PERSON><PERSON><PERSON><PERSON>", "MemoryWithGcCachePlugin", "maxGenerations", "getRspackReactRefresh", "ReactRefreshWebpackPlugin", "ProvidePlugin", "<PERSON><PERSON><PERSON>", "getDefineEnvPlugin", "isTurbopack", "ReactLoadablePlugin", "REACT_LOADABLE_MANIFEST", "runtimeAsset", "MIDDLEWARE_REACT_LOADABLE_MANIFEST", "DropClientPage", "TraceEntryPointsPlugin", "outputFileTracingRoot", "appDirEnabled", "traceIgnores", "excludeDefaultMomentLocales", "IgnorePlugin", "resourceRegExp", "contextRegExp", "NextJsRequireCacheHotReloader", "devP<PERSON><PERSON>", "HotModuleReplacementPlugin", "PagesManifestPlugin", "isEdgeRuntime", "MiddlewarePlugin", "sriEnabled", "sri", "algorithm", "edgeEnvironments", "__NEXT_BUILD_ID", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY", "BuildManifestPlugin", "RspackProfilingPlugin", "Profiling<PERSON><PERSON><PERSON>", "WellKnownErrorsPlugin", "CopyFilePlugin", "filePath", "cache<PERSON>ey", "__NEXT_VERSION", "CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL", "minimized", "AppBuildManifestPlugin", "ClientReferenceManifestPlugin", "experimentalInlineCss", "inlineCss", "NextTypesPlugin", "cacheLifeConfig", "cacheLife", "SubresourceIntegrityPlugin", "NextFontManifestPlugin", "cssChunking", "CssChunkingPlugin", "isImplicit", "baseUrl", "unshift", "JsConfigPathsPlugin", "edgeAsset", "experiments", "layers", "cacheUnaffected", "buildHttp", "urlImports", "<PERSON><PERSON><PERSON>", "cacheLocation", "lockfileLocation", "javascript", "generator", "asset", "trustedTypes", "enabledLibraryTypes", "snapshot", "managedPaths", "immutablePaths", "providedExports", "usedExports", "configVars", "JSON", "stringify", "trailingSlash", "buildActivityPosition", "devIndicators", "position", "productionBrowserSourceMaps", "reactStrictMode", "optimizeCss", "nextScriptWorkers", "scrollRestoration", "sw<PERSON><PERSON><PERSON><PERSON>", "imageLoaderFile", "loaderFile", "clientTraceMetadata", "serverSourceMaps", "cache", "maxMemoryGenerations", "Infinity", "cacheDirectory", "compression", "buildDependencies", "defaultWebpack", "hooks", "done", "tap", "stats", "compilation", "nextPackage", "dirname", "dep", "delete", "NEXT_WEBPACK_LOGGING", "infra", "profileClient", "profileServer", "summaryClient", "summaryServer", "profile", "summary", "logDefault", "infrastructureLogging", "level", "debug", "log", "toString", "colors", "logging", "preset", "timings", "ProgressPlugin", "buildConfiguration", "rootDirectory", "customAppFile", "escapeStringRegexp", "isDevelopment", "targetWeb", "sassOptions", "future", "mode", "unsafeCache", "originalDevtool", "pluginCountBefore", "totalPages", "nextRuntime", "pluginCountAfter", "pluginsChanged", "addUsage", "configFileName", "lazyCompilation", "entries", "then", "customSvgRule", "find", "nextImageRule", "craCompat", "fileLoaderExclude", "fileLoader", "topRules", "innerRules", "webpackDevMiddleware", "canMatchCss", "fileNames", "input", "hasUserCssConfig", "o", "Symbol", "for", "__next_css_remove", "e", "originalEntry", "updatedEntry", "originalFile", "finalizeEntrypoint", "value"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;IA4OaA,6BAA6B;eAA7BA;;IAbAC,yBAAyB;eAAzBA;;IAKAC,wBAAwB;eAAxBA;;IAzBAC,oBAAoB;eAApBA;;IA9BGC,kBAAkB;eAAlBA;;IA5DHC,mBAAmB;eAAnBA;;IAyKb,OAu0EC;eAv0E6BC;;IAXdC,yBAAyB;eAAzBA;;IA3BMC,eAAe;eAAfA;;IAHTC,oBAAoB;eAApBA;;;8DAjPK;kFACoB;4BACT;+DACV;yBACK;6DACP;8BAEkB;2BACsB;uBAOlD;4BAaA;wBAEkB;yBAEU;6DACd;wBACc;0EAI5B;4EACyB;qCACI;0CACL;4EACC;iCACA;qCACI;uCACE;qBACT;gCACE;sCACe;yCACyB;+CACzB;iCACd;qEAUzB;qBACsB;wCACU;4CACI;wCACJ;yCAEC;oCACL;wCACI;iCACJ;iCAEuB;yBAInD;qDAC8C;uCAO9C;wBAC+B;mCACJ;sCAI3B;8BAIA;2BAC+B;uCACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC,MAAMC,oBACJC,QAAQ;AAEV,MAAMC,8BACJD,QAAQ;AAEV,IAAIE,SAASC,cAAK,CAACC,OAAO,IAAI,IAAI;IAChC,MAAM,qBAA8D,CAA9D,IAAIC,MAAM,sDAAV,qBAAA;eAAA;oBAAA;sBAAA;IAA6D;AACrE;AAEO,MAAMX,sBAAgC;IAC3C;IACA;IACA;IACA;CACD;AAED,MAAMY,6BAA6B;IACjC,+FAA+F;IAC/F,2HAA2H;IAC3H,2DAA2D;IAC3D;IACA,oGAAoG;IACpG,8GAA8G;IAC9G;CACD;AACD,MAAMC,kBAAkB;AAExB,MAAMC,qBACJ;AAEF,wBAAwB;AACxB,MAAMC,eAAe,AAACC,CAAAA,QAAQC,GAAG,CAACC,SAAS,IAAI,EAAC,EAC7CC,KAAK,CAACH,QAAQI,QAAQ,KAAK,UAAU,MAAM,KAC3CC,MAAM,CAAC,CAACC,IAAM,CAAC,CAACA;AAEnB,MAAMC,mBAA0DC,OAAOC,MAAM,CAAC;IAC5EC,kBAAkB;IAClBC,SACE,yDAAyD;IACzD;AACJ;AAEA,SAASC,YAAYC,OAAwB;IAC3C,OACE,0BAA0B;IAC1BA,QAAOC,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAClC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,kBAAkB,CAAC,IACpC,0CAA0C;IAC1CD,QAAOC,IAAI,KAAK,CAAC,sBAAsB,CAAC;AAE5C;AAEA,MAAMC,uBAAuBC,IAAAA,gBAAQ,EACnC,CAACC;IACCC,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EAAC,CAAC,8BAA8B,EAAEJ,QAAQ,IAAI,CAAC,IACnD,kGACA;AAEN;AAGF,IAAIK,oBAAoB;AACxB,IAAIC,+BAA+B;AACnC,MAAMC,yBACJ;AAEK,SAASzC,mBACd0C,aAAoC,EACpCC,YAAoC;QAGpCD,6BAAAA;IADA,MAAME,qBAAqBrC,QAAQsC,OAAO,CAACJ;KAC3CC,wBAAAA,cAAcZ,MAAM,sBAApBY,8BAAAA,sBAAsBI,KAAK,qBAA3BJ,4BAA6BK,OAAO,CAAC,CAACC;QACpC,IAAIA,QAAQ,OAAOA,SAAS,YAAY,SAASA,MAAM;YACrD,MAAMC,OAAOD,KAAKE,GAAG;YACrB,wEAAwE;YACxE,IAAID,SAASN,cAAc;gBACzBK,KAAKE,GAAG,GAAG;oBAACN;oBAAoBK;iBAA+B;YACjE,OAAO,IACLE,MAAMC,OAAO,CAACH,SACdA,KAAKI,IAAI,CAAC,CAACC,IAAMA,MAAMX,iBACvB,kCAAkC;YAClC,CAACM,KAAKI,IAAI,CACR,CAACC,IAAMA,MAAMV,sBAAsBU,MAAMb,yBAE3C;gBACA,MAAMc,MAAMN,KAAKO,SAAS,CAAC,CAACF,IAAMA,MAAMX;gBACxC,iCAAiC;gBACjCK,KAAKE,GAAG,GAAG;uBAAID;iBAAK;gBAEpB,kEAAkE;gBAClED,KAAKE,GAAG,CAACO,MAAM,CAACF,KAAK,GAAGX;YAC1B;QACF;IACF;AACF;AAEO,MAAM7C,uBAAuB;IAClC2D,gBAAgB;IAChBC,SAAS;QAAC;KAAe;IACzBC,UAAU;IACVC,eAAe;QAAC;KAAU;IAC1BC,eAAe;QAAC;KAAU;IAC1BC,gBAAgB;QAAC;QAAQ;KAAU;IACnCC,kBAAkB;QAAC;KAAe;IAClCC,YAAY;QAAC;QAAO;QAAS;KAAQ;IACrCC,mBAAmB;IACnBC,UAAU;IACVC,YAAY;QAAC;KAAO;IACpBC,WAAW;QAAC;KAAQ;IACpBC,OAAO,EAAE;IACTC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,cAAc,EAAE;AAClB;AAEO,MAAM7E,4BAA4B;IACvC,GAAGE,oBAAoB;IACvB4E,OAAO;AACT;AAEO,MAAM7E,2BAA2B;IACtC,GAAGC,oBAAoB;IACvB4E,OAAO;IACPjB,gBAAgB;IAChBK,gBAAgB;QAAC;QAAQ;KAAS;IAClCQ,gBAAgB;AAClB;AAEO,MAAM3E,gCAAgC;IAC3C,GAAGE,wBAAwB;IAC3B6E,OAAO;AACT;AAEO,MAAMtE,uBACX;AAEK,eAAeD,gBAAgB,EACpCwE,GAAG,EACHC,MAAM,EACNC,GAAG,EAKJ;IAMC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,eAAe,EAAE,GAAG,MAAMC,IAAAA,qBAAY,EACpEN,KACAC;IAEF,MAAMM,oBAAoB,MAAMC,IAAAA,2BAAoB,EAACR,KAAKE;IAC1D,OAAO;QACLC;QACAC;QACAC;QACAE;IACF;AACF;AAEO,SAAShF;IACd,IAAI;QACFI,QAAQ;QACR,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEA,MAAM8E,qBAAqB;AAEZ,eAAenF,qBAC5B0E,GAAW,EACX,EACEU,OAAO,EACPC,aAAa,EACbV,MAAM,EACNW,YAAY,EACZV,MAAM,KAAK,EACXW,WAAW,EACXC,gBAAgB,KAAK,EACrBC,QAAQ,EACRC,2BAA2B,KAAK,EAChCC,QAAQ,EACRC,gBAAgB,EAChBC,iBAAiB,EACjBC,cAAc,EACdC,MAAM,EACNC,kBAAkB,EAClBC,UAAU,EACVpB,QAAQ,EACRC,YAAY,EACZC,eAAe,EACfE,iBAAiB,EACjBiB,mBAAmB,EACnBC,mBAAmB,EACnBC,gBAAgB,EAgCjB;QAiHCzB,sBAOIA,uBA2biBA,kBACWA,mBAGtBA,mBAIAE,2BAEmBF,mBACDE,4BACLF,mBAkTfA,sBA0vBoBA,0BA+DtBA,2BAsCJE,4BAJJ,wDAAwD;IACxD,iCAAiC;IACjCrC,gCAAAA,wBAmG0BmC,uBAuBTA,mBACQA,mBACLA,mBACXA,mBACEA,mBAsCX0B,yBAgLc7D,uBAmDZA,wBA0FAA,6BAAAA;IA7qEF,MAAM8D,WAAWhB,iBAAiBiB,0BAAc,CAACC,MAAM;IACvD,MAAMC,eAAenB,iBAAiBiB,0BAAc,CAACG,UAAU;IAC/D,MAAMC,eAAerB,iBAAiBiB,0BAAc,CAACK,MAAM;IAE3D,MAAMC,WAAWC,QAAQ/F,QAAQC,GAAG,CAAC+F,WAAW;IAEhD,MAAMC,0BACJH,YAAY9F,QAAQC,GAAG,CAACiG,kCAAkC,GACtDC,4DAA6B,GAC7BC,gDAA2B;IAEjC,uFAAuF;IACvF,MAAMC,0BAA0BT,gBAAgBF;IAEhD,MAAMY,cACJ1B,SAAS2B,WAAW,CAACC,MAAM,GAAG,KAC9B5B,SAAS6B,UAAU,CAACD,MAAM,GAAG,KAC7B5B,SAASjC,QAAQ,CAAC6D,MAAM,GAAG;IAE7B,MAAME,YAAY,CAAC,CAAC1B;IACpB,MAAM2B,0BAA0B;IAChC,MAAMC,oBAAoB,CAAC,CAAChD,OAAOiD,YAAY,CAACC,WAAW,IAAIJ;IAC/D,MAAMK,sBAAsBC,IAAAA,8CAAsB,EAACpD,UAC/C,kBACA;IAEJ,MAAMqD,kBAAkBC,IAAAA,sCAAkB,EAACvD;IAE3C,IAAI,CAACE,OAAOsD,IAAAA,6BAAqB,EAACvD,SAAS;QACzCA,OAAOwD,OAAO,GAAG;IACnB;IACA,MAAMA,UAAUC,aAAI,CAACC,IAAI,CAAC3D,KAAKC,OAAOwD,OAAO;IAE7C,IAAIG,eAAe,CAACN,mBAAmBrD,OAAOiD,YAAY,CAACW,kBAAkB;IAC7E,IAAIC,kBAAkDC;IACtD,IAAIH,cAAc;YAEKjI,4BAAAA,6BAAAA;QADrB,0CAA0C;QAC1C,MAAMqI,gBAAerI,WAAAA,QAAQ,8BAARA,8BAAAA,SAAkBsI,iBAAiB,sBAAnCtI,6BAAAA,iCAAAA,8BAAAA,2BACjBuI,MAAM;QACVJ,kBAAkBE,eACd;YAAC,CAAC,WAAW,EAAEA,cAAc;YAAW;SAAK,GAC7CD;IACN;IAEA,IAAI,CAACpG,qBAAqB,CAACiG,gBAAgBN,iBAAiB;QAC1Da,KAAIC,IAAI,CACN,CAAC,6EAA6E,EAAEV,aAAI,CAACW,QAAQ,CAC3FrE,KACAsD,iBACA,+CAA+C,CAAC;QAEpD3F,oBAAoB;IACtB;IAEA,mEAAmE;IACnE,IAAI,CAAC2F,mBAAmB1B,UAAU;QAChC,MAAM0C,IAAAA,iBAAY,EAACrE,OAAOiD,YAAY,CAACqB,aAAa;IACtD;IAEA,4DAA4D;IAC5D,2DAA2D;IAC3D,MAAMC,yBAAmC,AACvCvE,CAAAA,OAAOwE,iBAAiB,IAAI,EAAE,AAAD,EAC7BC,MAAM,CAAC9I;IAET,KAAK,MAAM+I,OAAO1E,OAAOiD,YAAY,CAAC0B,sBAAsB,IAAI,EAAE,CAAE;QAClE,IAAI,CAACJ,uBAAuBK,QAAQ,CAACF,MAAM;YACzCH,uBAAuBM,IAAI,CAACH;QAC9B;IACF;IAEA,IAAI,CAAC/G,gCAAgC,CAACgG,gBAAgB3D,OAAO8E,QAAQ,EAAE;QACrEZ,KAAIC,IAAI,CACN;QAEFxG,+BAA+B;IACjC;IAEA,MAAMoH,4BACJ/E,OAAOiD,YAAY,CAAC+B,WAAW,IAAI,CAAC,CAAChF,OAAOwE,iBAAiB;IAC/D,MAAMS,gBAAgB;QACpBC,MAAM;YAAEC,IAAI;gBAAC;gBAA8B;aAAsB;QAAC;QAClE,GAAIJ,4BAEA,CAAC,IACD;YAAEK,SAAS;gBAACrF;mBAAQ3E;aAAoB;QAAC,CAAC;QAC9CiK,SAAS,CAACC;YACR,IAAIlK,oBAAoBoD,IAAI,CAAC,CAACC,IAAMA,EAAEyG,IAAI,CAACI,eAAe;gBACxD,OAAO;YACT;YAEA,MAAMC,kBAAkBC,IAAAA,qCAAoB,EAC1CF,aACAf;YAEF,IAAIgB,iBAAiB,OAAO;YAE5B,OAAOD,YAAYV,QAAQ,CAAC;QAC9B;IACF;IAEA,MAAMa,cAAcC,IAAAA,oCAAc,EAChC/B,cACAN,iBACAZ,yBACAe,SACA1C,UACAf,KACCqB,UAAUN,UACXb,KACA0B,WACA3B,uBAAAA,OAAOiD,YAAY,qBAAnBjD,qBAAqB2F,aAAa,EAClCV,cAAcI,OAAO;IAGvB,MAAMO,sBAAsBH,cACxB3B,YACA+B,IAAAA,4CAAsB,GACpB7F,wBAAAA,OAAOiD,YAAY,qBAAnBjD,sBAAqB2F,aAAa,EAClC5F,KACAE,KACAwC,yBACAwC,cAAcI,OAAO;IAG3B,IAAIS,+BAA+B;IACnC,MAAMC,eAAe,CAACC;YAElBhG;QADF,IACEA,CAAAA,2BAAAA,uBAAAA,OAAQiD,YAAY,qBAApBjD,qBAAsBiG,iBAAiB,KACvC,CAACH,8BACD;gBAMApK,oCAAAA;YALA,sEAAsE;YACtE,+CAA+C;YAC/C,qFAAqF;YACrF,uDAAuD;YACvDoK,+BAA+B;aAC/BpK,WAAAA,QAAQ,8BAARA,qCAAAA,SAAkBwK,yBAAyB,qBAA3CxK,wCAAAA,UACE+H,aAAI,CAACC,IAAI,CAACF,SAAS,CAAC,kBAAkB,EAAE2C,KAAKC,GAAG,GAAG,KAAK,CAAC;QAE7D;QAEA,MAAMC,sBAAsBjK,QAAQC,GAAG,CAACiK,kBAAkB;QAC1D,IAAIpE,YAAYmE,qBAAqB;gBAwB7BnG,2BAGAA;YA1BN,OAAO;gBACLqG,QAAQ;gBACRC,SAAS;oBACPC,UAAUhE;oBACViE,SAAS3G;oBACTe;oBACAM;oBACAuF,iBAAiB1G,OAAO0B;oBACxB6C,mBAAmBD;oBACnBjE;oBACAsG,aAAanD,aAAI,CAACC,IAAI,CACpB3D,KACAC,CAAAA,0BAAAA,OAAQwD,OAAO,KAAI,SACnB,SACA;oBAEFqD,yBAAyBnG;oBAEzB,0BAA0B;oBAC1BoG,KAAK3E,QAAQ/F,QAAQ2K,QAAQ,CAACD,GAAG;oBACjCE,qBAAqB7E,QAAQnC,OAAOiD,YAAY,CAAC+D,mBAAmB;oBACpEC,mBAAmBjH,OAAOiH,iBAAiB;oBAC3CC,YAAY/E,QACVjC,6BAAAA,4BAAAA,SAAUiH,eAAe,qBAAzBjH,0BAA2BkH,sBAAsB;oBAEnDC,uBAAuBlF,QACrBjC,6BAAAA,6BAAAA,SAAUiH,eAAe,qBAAzBjH,2BAA2BmH,qBAAqB;oBAElDC,wBAAwB5L,QAAQsC,OAAO,CACrC;oBAGF,GAAGgI,YAAY;gBACjB;YACF;QACF;QAEA,OAAO;YACLO,QAAQ;YACRC,SAAS;gBACPC,UAAUhE;gBACViE,SAAS3G;gBACTe;gBACAM;gBACAuF,iBAAiB1G,OAAO0B;gBACxB4F,YAAYvH;gBACZE;gBACAsE,mBAAmBD;gBACnBjE;gBACAsG,aAAanD,aAAI,CAACC,IAAI,CAAC3D,KAAKC,CAAAA,0BAAAA,OAAQwD,OAAO,KAAI,SAAS,SAAS;gBACjEqD,yBAAyBnG;gBACzB,GAAGsF,YAAY;YACjB;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAMwB,uBAAuBzB,aAAa;QACxC0B,kBAAkB;QAClBC,aAAaC,yBAAc,CAACC,qBAAqB;QACjDC,KAAK;IACP;IACA,MAAMC,oBAAoB/B,aAAa;QACrC0B,kBAAkB;QAClBC,aAAaC,yBAAc,CAACI,mBAAmB;QAC/CF,KAAK;IACP;IACA,MAAMG,wBAAwBjC,aAAa;QACzC0B,kBAAkB;QAClBC,aAAaC,yBAAc,CAACM,eAAe;QAC3CJ,KAAK;IACP;IACA,oDAAoD;IACpD,MAAMK,mBAAmBnC,aAAa;QACpC0B,kBAAkB;QAClBI,KAAK;IACP;IAEA,MAAMM,iBAAiB;QACrBC,OAAOzE,eAAeuE,mBAAmBzC;IAC3C;IAEA,MAAM4C,wBAAwBvF,YAC1B;QACE,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/C0E;QACA/B;QACAG;KACD,CAACnJ,MAAM,CAAC0F,WACT,EAAE;IAEN,MAAMmG,yBAAyB;QAC7B;QACA,uDAAuD;QACvD,iDAAiD;QACjD,gDAAgD;QAChD,+CAA+C;QAC/Cd;QACA/B;KACD,CAAChJ,MAAM,CAAC0F;IAET,MAAMoG,yBAAyB;QAC7B;QACA,mEAAmE;QACnE,wFAAwF;QACxF,gDAAgD;QAChD,+CAA+C;QAC/CxC,aAAa;YACX0B,kBAAkB;YAClBC,aAAaC,yBAAc,CAACa,UAAU;QACxC;QACA/C;KACD,CAAChJ,MAAM,CAAC0F;IAET,MAAMsG,sBACJxI,OAAO0B,WAAW;QAACjG,QAAQsC,OAAO,CAACJ;KAAwB,GAAG,EAAE;IAElE,2CAA2C;IAC3C,MAAM8K,0BAA0B,CAAC,EAC/BC,cAAc,EACdC,YAAY,EAIb,GAAK;eACAA,eAAeH,sBAAsB,EAAE;YAC3C;gBACE,iDAAiD;gBACjD,uBAAuB;gBACvBlC,QAAQ;YACV;eACIzD,YACA;gBACE,uDAAuD;gBACvD,iDAAiD;gBACjD,gDAAgD;gBAChD,+CAA+C;gBAC/C6F,iBAAiBX,wBAAwBF;gBACzCrC;gBACAG;aACD,CAACnJ,MAAM,CAAC0F,WACT,EAAE;SACP;IAED,MAAM0G,yBAAyBH,wBAAwB;QACrDC,gBAAgB;QAChB,8EAA8E;QAC9EC,cAAc;IAChB;IACA,MAAME,qBAAqBJ,wBAAwB;QACjDC,gBAAgB;QAChBC,cAAc;IAChB;IAEA,2EAA2E;IAC3E,8EAA8E;IAC9E,gBAAgB;IAChB,MAAMG,wBAAwBpF,eAC1BoC,aAAa;QACX0B,kBAAkB;QAClBC,aAAaC,yBAAc,CAACqB,OAAO;IACrC,KACAb,eAAeC,KAAK;IAExB,MAAMa,iBAAiBjJ,OAAOiJ,cAAc;IAE5C,MAAMC,aAAazG,0BACfgB,aAAI,CAACC,IAAI,CAACF,SAAS2F,4BAAgB,IACnC3F;IAEJ,MAAM4F,uBAAuB;QAC3B;WACItH,eAAeuH,2BAAkB,GAAG,EAAE;QAC1C,kCAAkC;QAClC;KACD;IAED,MAAMC,gBAAgB3H,WACjB;QACC,0BAA0B;QAC1B,WAAW,EAAE;QACb,GAAI1B,MACA;YACE,CAACsJ,qDAAyC,CAAC,EAAE7N,QAAQsC,OAAO,CAC1D,CAAC,yDAAyD,CAAC;YAE7D,CAACwL,2CAA+B,CAAC,EAC/B,CAAC,EAAE,CAAC,GACJ/F,aAAI,CACDW,QAAQ,CACPrE,KACA0D,aAAI,CAACC,IAAI,CAAC+F,2CAA6B,EAAE,OAAO,YAEjDC,OAAO,CAAC,OAAO;QACtB,IACA,CAAC,CAAC;QACN,CAACC,4CAAgC,CAAC,EAChC,CAAC,EAAE,CAAC,GACJlG,aAAI,CACDW,QAAQ,CACPrE,KACA0D,aAAI,CAACC,IAAI,CACP+F,2CAA6B,EAC7BxJ,MAAM,CAAC,WAAW,CAAC,GAAG,YAGzByJ,OAAO,CAAC,OAAO;QACpB,GAAI5G,YACA;YACE,CAAC8G,gDAAoC,CAAC,EAAE3J,MACpC;gBACEvE,QAAQsC,OAAO,CACb,CAAC,yDAAyD,CAAC;gBAE7D,CAAC,EAAE,CAAC,GACFyF,aAAI,CACDW,QAAQ,CACPrE,KACA0D,aAAI,CAACC,IAAI,CACP+F,2CAA6B,EAC7B,oBAGHC,OAAO,CAAC,OAAO;aACrB,GACD;gBACE,CAAC,EAAE,CAAC,GACFjG,aAAI,CACDW,QAAQ,CACPrE,KACA0D,aAAI,CAACC,IAAI,CACP+F,2CAA6B,EAC7B,gBAGHC,OAAO,CAAC,OAAO;aACrB;QACP,IACA,CAAC,CAAC;IACR,IACA5F;IAEJ,MAAM+F,gBAAkD;QACtD,yCAAyC;QACzCzK,YAAY;YAAC;YAAO;YAAQ;YAAQ;YAAO;YAAQ;YAAS;SAAQ;QACpE0K,gBAAgB9J,OAAOiD,YAAY,CAAC6G,cAAc;QAClDhL,SAAS;YACP;eACG3C;SACJ;QACD2D,OAAOiK,IAAAA,2CAAoB,EAAC;YAC1BvG;YACA7B;YACAG;YACAE;YACA/B;YACAD;YACAc;YACAM;YACArB;YACAgB;YACA2B;QACF;QACA,GAAIf,WACA;YACE5C,UAAU;gBACR3C,SAASV,QAAQsC,OAAO,CAAC;YAC3B;QACF,IACA8F,SAAS;QACb,oFAAoF;QACpFvE,YAAYyK,IAAAA,qBAAY,EAACrJ,cAAc;QACvC,GAAImB,gBAAgB;YAClB5C,gBAAgBmK,2BAAkB;QACpC,CAAC;QACDY,SAAS;YACPjI,eAAe,IAAIkI,yEAAoC,KAAKpG;SAC7D,CAACrH,MAAM,CAAC0F;QACT,GAAKD,YAAY/B,eACb;YACEgK,UAAU;gBACRC,YAAYjK;YACd;QACF,IACA,CAAC,CAAC;IACR;IAEA,2DAA2D;IAC3D,gEAAgE;IAChE,mEAAmE;IACnE,MAAMkK,qBAA+B,EAAE;IACvC,MAAMC,yBAAmC,EAAE;IAC3C,MAAMC,2BAA2B,IAAIC;IACrC,iDAAiD;IACjD,MAAMC,iBAAiB,CACrBC,aACAC,gBACAC;QAEA,IAAI;YACF,IAAIL,yBAAyBM,GAAG,CAACH,cAAc;gBAC7C;YACF;YACAH,yBAAyBO,GAAG,CAACJ;YAE7B,MAAMK,kBAAkBrP,QAAQsC,OAAO,CAAC,GAAG0M,YAAY,aAAa,CAAC,EAAE;gBACrEE,OAAO;oBAACD;iBAAe;YACzB;YAEA,6FAA6F;YAC7F,0EAA0E;YAC1E,eAAe;YACf,0EAA0E;YAC1E,2EAA2E;YAC3E,MAAMK,YAAYvH,aAAI,CAACC,IAAI,CAACqH,iBAAiB;YAE7C,yFAAyF;YACzF,IAAIH,MAAMhG,QAAQ,CAACoG,YAAY;YAC/BJ,MAAM/F,IAAI,CAACmG;YACX,MAAMC,eAAevP,QAAQqP,iBAAiBE,YAAY,IAAI,CAAC;YAC/D,KAAK,MAAMC,QAAQtO,OAAOuO,IAAI,CAACF,cAAe;gBAC5CR,eAAeS,MAAMF,WAAWJ;YAClC;QACF,EAAE,OAAOQ,GAAG;QACV,uDAAuD;QACzD;IACF;IAEA,KAAK,MAAMV,eAAe;QACxB;QACA;WACI5H,YACA;YACE,CAAC,wBAAwB,EAAEK,qBAAqB;YAChD,CAAC,4BAA4B,EAAEA,qBAAqB;SACrD,GACD,EAAE;KACP,CAAE;QACDsH,eAAeC,aAAa3K,KAAKuK;IACnC;IACAG,eAAe,QAAQ1K,KAAKsK;IAE5B,MAAMgB,cAAcrL,OAAOqL,WAAW;IAEtC,wDAAwD;IACxD,2BAA2B;IAC3B,IAAIrL,OAAOsL,sBAAsB,IAAI/G,wBAAwB;QAC3D,MAAMgH,2BAA2BhH,uBAAuB9H,MAAM,CAAC,CAACiI;gBAC9D1E;oBAAAA,iCAAAA,OAAOsL,sBAAsB,qBAA7BtL,+BAA+B4E,QAAQ,CAACF;;QAE1C,IAAI6G,yBAAyB3I,MAAM,GAAG,GAAG;YACvC,MAAM,qBAIL,CAJK,IAAI7G,MACR,CAAC,8FAA8F,EAAEwP,yBAAyB7H,IAAI,CAC5H,OACC,GAHC,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,+CAA+C;IAC/C,MAAM8H,yBAAyB/P,kBAAkBgJ,MAAM,IACjDzE,OAAOsL,sBAAsB,IAAI,EAAE,EACvC7O,MAAM,CAAC,CAACiI,MAAQ,EAACH,0CAAAA,uBAAwBK,QAAQ,CAACF;IACpD,wEAAwE;IACxE,MAAM+G,6BAA6B,IAAIC,OACrC,CAAC,2BAA2B,EAAEF,uBAC3BG,GAAG,CAAC,CAACjP,IAAMA,EAAEgN,OAAO,CAAC,OAAO,YAC5BhG,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMkI,yBAAyB,IAAIF,OACjC,CAAC,2BAA2B,EAAEnH,0CAAAA,uBAC1BoH,GAAG,CAAC,CAACjP,IAAMA,EAAEgN,OAAO,CAAC,OAAO,YAC7BhG,IAAI,CAAC,KAAK,QAAQ,CAAC;IAGxB,MAAMmI,kBAAkBC,IAAAA,oCAAmB,EAAC;QAC1C9L;QACAyL;QACAM,oBAAoBxH;QACpBxE;IACF;IAEA,MAAMiM,sBAAsB,IAAIN,OAAO,CAAC,IAAI,EAAEzC,eAAevF,IAAI,CAAC,KAAK,EAAE,CAAC;IAE1E,MAAMuI,yBAAyB;QAAChH,cAAcC,IAAI;QAAE8G;KAAoB;IAExE,MAAME,iBAAiBxQ,QAAQ,UAAUwQ,cAAc;IAEvD,MAAMC,kCACJ,CAAC,CAACnM,OAAOiD,YAAY,CAACmJ,mBAAmB,IAAInM;IAE/C,MAAMoM,iBAAiB;QACrB,MAAMC,WAAWC,OAAOnQ,QAAQC,GAAG,CAACmQ,wBAAwB;QAC5D,IAAIL,iCAAiC;YACnC,IAAIG,UAAU;gBACZhP,QAAQC,IAAI,CACV;YAEJ;YACA,OAAO;QACT;QACA,OAAO+O,YAAYxI;IACrB;IAEA,MAAM2I,kBACJ,CAACvK,YACD,CAACjC,OACD0B,YACA,IAAI,AACFjG,CAAAA,QAAQ,sDAAqD,EAC7DgR,eAAe,CACf,IAAIC,IACF;QACE;YAAC;YAAahJ;SAAa;QAC3B;YAAC;YAAY,CAAC,GAAC3D,mBAAAA,OAAO8E,QAAQ,qBAAf9E,iBAAiB4M,KAAK;SAAC;QACtC;YAAC;YAAuB,CAAC,GAAC5M,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiB6M,gBAAgB;SAAC;QAC5D;YACE;YACA,CAAC,GAAC7M,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiB8M,qBAAqB;SACzC;QACD;YACE;YACA,CAAC,EAAC5M,6BAAAA,4BAAAA,SAAUiH,eAAe,qBAAzBjH,0BAA2BkH,sBAAsB;SACpD;QACD;YAAC;YAAoB,CAAC,GAACpH,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiB+M,aAAa;SAAC;QACtD;YAAC;YAAmB,CAAC,EAAC7M,6BAAAA,6BAAAA,SAAUiH,eAAe,qBAAzBjH,2BAA2B8M,eAAe;SAAC;QACjE;YAAC;YAAc,CAAC,GAAChN,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiBiN,OAAO;SAAC;QAC1C;YAAC;YAAqB,CAAC,CAACjN,OAAOwE,iBAAiB;SAAC;QACjD;YAAC;YAA8B,CAAC,CAACxE,OAAOkN,0BAA0B;SAAC;QACnE;YAAC;YAA6B,CAAC,CAAClN,OAAOmN,yBAAyB;SAAC;QACjE;YAAC;YAAqB,CAAC,CAACnN,OAAOiH,iBAAiB;SAAC;QACjD,+EAA+E;QAC/E;YAAC;YAAgBjH,OAAOiD,YAAY,CAACmK,YAAY,KAAK;SAAK;QAC3DvJ;KACD,CAACpH,MAAM,CAAqB0F;IAInC,IAAItE,gBAAuC;QACzCwP,aAAahB;QACb,GAAIrK,eAAe;YAAEsL,kBAAkB;gBAAEC,MAAM;YAAK;QAAE,IAAI,CAAC,CAAC;QAC5D,aAAa;QACbC,WACE7L,YAAYG,eAER,8DAA8D;QAC9D,+CAA+C;QAC/C;YACE;eACIA,eACA;gBACE;oBACE,yBAAyB;oBACzB,2BAA2B;gBAC7B;gBACA2L,IAAAA,0CAAwB;gBACxBC,qDAAmC;aACpC,GACD,EAAE;SACP,GACD;eACKxB;YACH,CAAC,EACCyB,OAAO,EACPC,OAAO,EACP/O,cAAc,EACdgP,WAAW,EACXC,UAAU,EAqBX,GACCjC,gBACE8B,SACAC,SACA/O,gBACAgP,YAAYE,WAAW,EACvB,CAACvH;oBACC,MAAMwH,kBAAkBF,WAAWtH;oBACnC,OAAO,CAACyH,gBAAwBC,mBAC9B,IAAIC,QAAQ,CAACnQ,SAASoQ;4BACpBJ,gBACEC,gBACAC,kBACA,CAACG,KAAKC,QAAQC;oCAIRA;gCAHJ,IAAIF,KAAK,OAAOD,OAAOC;gCACvB,IAAI,CAACC,QAAQ,OAAOtQ,QAAQ;oCAAC;oCAAM;iCAAM;gCACzC,MAAMwQ,QAAQ,SAAStJ,IAAI,CAACoJ,UACxBC,CAAAA,gCAAAA,mCAAAA,YAAaE,mBAAmB,qBAAhCF,iCAAkCrR,IAAI,MACtC,WACA,UAAUgI,IAAI,CAACoJ;gCACnBtQ,QAAQ;oCAACsQ;oCAAQE;iCAAM;4BACzB;wBAEJ;gBACJ;SAEL;QAEPE,cAAc;YACZC,cAAc,CAAC1O;YACf2O,gBAAgB;YAChBC,SAAS;YAETC,aAAa,AAAC,CAAA;gBAGZ,kBAAkB;gBAClB,IAAI7O,KAAK;oBACP,IAAI+B,cAAc;wBAChB;;;;;YAKA,GACA,MAAM+M,wBAAwB,CAACC;4BAC7B,8FAA8F;4BAC9F,4EAA4E;4BAC5E,MAAMC,QACJ;4BACF,MAAMC,QAAQF,WAAWE,KAAK,CAACD;4BAC/B,OAAOC,QAAQA,KAAK,CAAC,EAAE,GAAG;wBAC5B;wBACA,OAAO;4BACLC,aAAa;gCACX,+FAA+F;gCAC/F,yDAAyD;gCACzDC,QAAQ;oCACNC,QAAQ;oCACRC,oBAAoB;oCACpBpK,MAAM;oCACNqK,SAAS;oCACTC,WAAW;oCACXC,kBAAkB;oCAClBC,oBAAoB;oCACpBxE,MAAM,CAACjO;wCACL,MAAM0S,WAAW1S,QAAO2S,gBAAgB;wCACxC,MAAMC,aAAad,sBAAsBY;wCACzC,IAAIE,YAAY;4CACd,OAAO,CAAC,cAAc,EAAEA,YAAY;wCACtC,OAAO;4CACL,MAAMC,OAAOC,eAAM,CAACC,UAAU,CAAC,QAAQC,MAAM,CAACN;4CAC9CG,KAAKG,MAAM,CAACN;4CACZ,OAAO,CAAC,cAAc,EAAEG,KAAKI,MAAM,CAAC,QAAQ;wCAC9C;oCACF;gCACF;gCACA,mCAAmC;gCACnCC,SAAS;gCACTC,gBAAgB;4BAClB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,IAAIpO,gBAAgBF,cAAc;oBAChC,OAAO;wBACLuO,UAAU,GAAGvO,eAAe,CAAC,YAAY,CAAC,GAAG,GAAG,SAAS,CAAC;wBAC1DuN,QAAQ;wBACRG,WAAW;oBACb;gBACF;gBAEA,MAAMc,sBAAsB;oBAC1BjB,QAAQ;oBACRnE,MAAM;oBACN,6DAA6D;oBAC7DqF,OAAOC,4BAAqB;oBAC5BtL,MAAKjI,OAAW;wBACd,MAAMwT,WAAWxT,QAAO2S,gBAAgB,oBAAvB3S,QAAO2S,gBAAgB,MAAvB3S;wBACjB,OAAOwT,WACHnG,uBAAuB9L,IAAI,CAAC,CAACkS,UAC3BD,SAASE,UAAU,CAACD,YAEtB;oBACN;oBACAE,UAAU;oBACV,mEAAmE;oBACnE,wCAAwC;oBACxCC,SAAS;gBACX;gBAEA,MAAMC,gBAAgB;oBACpB5L,MAAKjI,OAIJ;4BAEIA;wBADH,OACE,GAACA,eAAAA,QAAOC,IAAI,qBAAXD,aAAa0T,UAAU,CAAC,WACzB,qCAAqC;wBACpCzO,CAAAA,YAAYjF,QAAO8T,IAAI,KAAK,MAAK,KAClC,oBAAoB7L,IAAI,CAACjI,QAAO2S,gBAAgB,MAAM;oBAE1D;oBACA1E,MAAKjO,OAKJ;wBACC,MAAM6S,OAAOC,eAAM,CAACC,UAAU,CAAC;wBAC/B,IAAIhT,YAAYC,UAAS;4BACvBA,QAAO+T,UAAU,CAAClB;wBACpB,OAAO;4BACL,8BAA8B;4BAC9B,IAAI,CAAC5N,UAAU;gCACb,IAAI,CAACjF,QAAOgU,QAAQ,EAAE;oCACpB,MAAM,qBAEL,CAFK,IAAIlV,MACR,CAAC,iCAAiC,EAAEkB,QAAOC,IAAI,CAAC,uBAAuB,CAAC,GADpE,qBAAA;+CAAA;oDAAA;sDAAA;oCAEN;gCACF;gCACA4S,KAAKG,MAAM,CAAChT,QAAOgU,QAAQ,CAAC;oCAAEtD,SAAS5N;gCAAI;4BAC7C;wBACF;wBAEA,wFAAwF;wBACxF,yHAAyH;wBACzH,0CAA0C;wBAC1C,IAAI9C,QAAOsT,KAAK,EAAE;4BAChBT,KAAKG,MAAM,CAAChT,QAAOsT,KAAK;wBAC1B;wBAEA,OAAOT,KAAKI,MAAM,CAAC,OAAOgB,SAAS,CAAC,GAAG;oBACzC;oBACAN,UAAU;oBACVpB,WAAW;oBACXF,oBAAoB;gBACtB;gBAEA,kBAAkB;gBAClB,OAAO;oBACL,oDAAoD;oBACpD,qDAAqD;oBACrD,oDAAoD;oBACpD,0CAA0C;oBAC1CD,QAAQnN,WAEJ,YAAY;oBACZ,mCACA,CAACiP,QACC,CAAC,iCAAiCjM,IAAI,CAACiM,MAAMjG,IAAI;oBAEvD,mDAAmD;oBACnDiE,aAAajN,WACT,CAAC,IACD;wBACEkP,WAAWd;wBACXe,KAAKP;oBACP;oBACJpB,oBAAoB;oBACpBH,SAAS;gBACX;YACF,CAAA;YACA+B,cAAc3P,WACV;gBAAEuJ,MAAMqG,+CAAmC;YAAC,IAC5CzN;YAEJ0N,UACE,CAACvR,OACA0B,CAAAA,YACCG,gBACCE,gBAAgBhC,OAAOiD,YAAY,CAACwO,kBAAkB;YAC3DC,WAAWxP,WACP;gBACE,mBAAmB;gBACnB,IAAIyP,gBAAO,CAACC,0BAA0B,CAAC;gBAEvC;gBACA,mBAAmB;gBACnB,IAAID,gBAAO,CAACE,iCAAiC,CAAC;gBAE9C;aACD,GACD;gBACE,oBAAoB;gBACpB,CAAC/M;oBACC,4BAA4B;oBAC5B,MAAM,EAAEgN,YAAY,EAAE,GACpBpW,QAAQ;oBACV,IAAIoW,aAAa;wBAAExQ;oBAAW,GAAGyQ,KAAK,CAACjN;gBACzC;gBACA,aAAa;gBACb,CAACA;oBACC,MAAM,EACJkN,kBAAkB,EACnB,GAAGtW,QAAQ;oBACZ,IAAIsW,mBAAmB;wBACrBC,gBAAgB;4BACdtG,KAAK;gCACH,+DAA+D;gCAC/D,+CAA+C;gCAC/CuG,QAAQ;gCACR,6DAA6D;gCAC7D,4DAA4D;gCAC5DC,YAAY;4BACd;wBACF;oBACF,GAAGJ,KAAK,CAACjN;gBACX;aACD;QACP;QACA6I,SAAS5N;QACT,8CAA8C;QAC9CqS,OAAO;YACL,OAAO;gBACL,GAAI9I,gBAAgBA,gBAAgB,CAAC,CAAC;gBACtC,GAAG1I,WAAW;YAChB;QACF;QACAyR,cAAczV,OAAOC,MAAM,CAAC;YAC1B,GAAGF,gBAAgB;YACnB2V,IAAI,GAAEtS,uBAAAA,OAAOqS,YAAY,qBAAnBrS,qBAAqBuS,cAAc;QAC3C;QACAC,QAAQ;YACN,sEAAsE;YACtE,kCAAkC;YAClCC,YAAY,GACVzS,OAAO0S,WAAW,GACd1S,OAAO0S,WAAW,CAACC,QAAQ,CAAC,OAC1B3S,OAAO0S,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,KAC7B5S,OAAO0S,WAAW,GACpB,GACL,OAAO,CAAC;YACTjP,MAAM,CAACxD,OAAO+B,eAAeyB,aAAI,CAACC,IAAI,CAACwF,YAAY,YAAYA;YAC/D,oCAAoC;YACpCmH,UAAU5N,0BACNxC,OAAO6B,eACL,CAAC,SAAS,CAAC,GACX,CAAC,YAAY,CAAC,GAChB,CAAC,cAAc,EAAEjB,gBAAgB,cAAc,GAAG,MAAM,EACtDZ,MAAM,KAAKmB,SAAS,iBAAiB,iBACtC,GAAG,CAAC;YACTyR,SAASlR,YAAYG,eAAe,SAASgC;YAC7CgP,eAAenR,YAAYG,eAAe,WAAW;YACrDiR,wBAAwB;YACxBC,uBACE;YACF,uDAAuD;YACvDC,eAAexQ,0BACX,cACA,CAAC,cAAc,EAAE5B,gBAAgB,cAAc,KAC7CZ,MAAM,WAAW,uBAClB,GAAG,CAAC;YACTiT,+BAA+B;YAC/BC,oBAAoB9H;YACpB,iEAAiE;YACjE,mGAAmG;YACnG,iEAAiE;YACjE,oGAAoG;YACpG,2FAA2F;YAC3F+H,+BAA+BnT,MAC3B,6BACA6D;YACJuP,2BAA2B;YAC3BC,cAAc;YACdC,kBAAkB;QACpB;QACAC,aAAa;QACbxV,SAAS6L;QACT4J,eAAe;YACb,+BAA+B;YAC/B3T,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD,CAAC4T,MAAM,CACN,CAAC5T,OAAOyG;gBACN,4DAA4D;gBAC5DzG,KAAK,CAACyG,OAAO,GAAG9C,aAAI,CAACC,IAAI,CAACiQ,WAAW,WAAW,WAAWpN;gBAE3D,OAAOzG;YACT,GACA,CAAC;YAEHhB,SAAS;gBACP;mBACG3C;aACJ;YACD8N,SAAS,EAAE;QACb;QACAhN,QAAQ;YACNgB,OAAO;gBACL,+EAA+E;gBAC/E;oBACE8P,aAAa;wBACX5I,IAAI;+BACCwC,yBAAc,CAACiM,KAAK,CAACC,UAAU;+BAC/BlM,yBAAc,CAACiM,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA9V,SAAS;wBACP,6CAA6C;wBAC7C8B,OAAOiU,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA;oBACEhG,aAAa;wBACXiG,KAAK;+BACArM,yBAAc,CAACiM,KAAK,CAACC,UAAU;+BAC/BlM,yBAAc,CAACiM,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACA9V,SAAS;wBACP,6CAA6C;wBAC7C8B,OAAOiU,IAAAA,wDAAiC,EAAC;oBAC3C;gBACF;gBACA,mEAAmE;gBACnE;oBACE7O,MAAM;wBACJ;wBACA;qBACD;oBACDqB,QAAQ;oBACRwH,aAAa;wBACX5I,IAAIwC,yBAAc,CAACiM,KAAK,CAACC,UAAU;oBACrC;oBACArN,SAAS;wBACPyN,SACE;oBACJ;gBACF;gBACA;oBACE/O,MAAM;wBACJ;wBACA;qBACD;oBACDqB,QAAQ;oBACRwH,aAAa;wBACXiG,KAAK;+BACArM,yBAAc,CAACiM,KAAK,CAACC,UAAU;+BAC/BlM,yBAAc,CAACiM,KAAK,CAACE,aAAa;yBACtC;oBACH;oBACAtN,SAAS;wBACPyN,SACE;oBACJ;gBACF;gBACA,yFAAyF;gBACzF,iFAAiF;gBACjF,oCAAoC;gBACpC;oBACE/O,MAAM;wBACJ;wBACA;qBACD;oBACDqB,QAAQ;oBACRwH,aAAa;wBACX5I,IAAIwC,yBAAc,CAACiM,KAAK,CAACE,aAAa;oBACxC;gBACF;mBACI9R,eACA,EAAE,GACF;oBACE;wBACEkD,MAAM;wBACNqB,QAAQ;oBACV;iBACD;mBACDzD,YACA;oBACE;wBACE,uFAAuF;wBACvF,UAAU;wBACVyN,OAAO5I,yBAAc,CAACuM,MAAM;wBAC5BhP,MAAMhJ;oBACR;oBACA,4CAA4C;oBAC5C;wBACEiY,eAAe,IAAIzI,OACjB0I,mCAAwB,CAACC,aAAa;wBAExC9D,OAAO5I,yBAAc,CAACC,qBAAqB;oBAC7C;oBACA;wBACE,gEAAgE;wBAChE,2CAA2C;wBAC3C2I,OAAO5I,yBAAc,CAACI,mBAAmB;wBACzC7C,MAAM;oBACR;oBACA;wBACE6I,aAAauG,4BAAqB;wBAClCtW,SAAS;4BACP8B,OAAOyU,IAAAA,8CAAuB;wBAChC;oBACF;oBACA;wBACExG,aAAayG,+BAAwB;wBACrCxW,SAAS;4BACP8B,OAAO2U,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;oBACA;wBACE1G,aAAa2G,+BAAwB;wBACrC1W,SAAS;4BACP8B,OAAO2U,IAAAA,gDAAyB,EAAC;wBACnC;oBACF;iBACD,GACD,EAAE;mBACF3R,aAAa,CAACnB,WACd;oBACE;wBACEoM,aAAayG,+BAAwB;wBACrCtP,MAAM;4BACJ,8DAA8D;4BAC9D,yBAAyB;4BACzByP,KAAK;gCACH1I;gCACA;oCACE+H,KAAK;wCAACvI;wCAA4BvP;qCAAmB;gCACvD;6BACD;wBACH;wBACAiY,eAAe;4BACb,8DAA8D;4BAC9D,8DAA8D;4BAC9D,6DAA6D;4BAC7D,8DAA8D;4BAC9D,WAAW;4BACXH,KAAK;gCACH,IAAItI,OAAO0I,mCAAwB,CAACQ,QAAQ;gCAC5C,IAAIlJ,OAAO0I,mCAAwB,CAACS,iBAAiB;6BACtD;wBACH;wBACA7W,SAAS;4BACPuB,YAAYyK,IAAAA,qBAAY,EAACrJ,cAAc;4BACvCzB,gBAAgBkK;4BAChB,mFAAmF;4BACnF,kFAAkF;4BAClF,8BAA8B;4BAC9BtJ,OAAOgV,IAAAA,uCAAgB,EAAC3R,qBAAqB;gCAC3C,iCAAiC;gCACjCpC;gCACAwP,OAAO5I,yBAAc,CAACC,qBAAqB;gCAC3C9F;4BACF;wBACF;wBACAzD,KAAK;oBACP;iBACD,GACD,EAAE;gBACN,kDAAkD;gBAClD,yDAAyD;mBACrD,CAAC2B,OAAOiD,YAAY,CAACvD,cAAc,GACnC;oBACE;wBACEwF,MAAM;wBACNlH,SAAS;4BACP0B,gBAAgB;wBAClB;oBACF;iBACD,GACD,EAAE;mBACFoD,aAAahB,eACb;oBACE,sEAAsE;oBACtE,mEAAmE;oBACnE,oCAAoC;oBACpC;wBACEqS,eAAe,IAAIzI,OACjB0I,mCAAwB,CAACW,YAAY;wBAEvCxE,OAAO5I,yBAAc,CAACC,qBAAqB;oBAC7C;iBACD,GACD,EAAE;mBACF9E,YACA;oBACE;wBACE,8CAA8C;wBAC9C,kEAAkE;wBAClEkS,OAAO;4BACL;gCACEjH,aAAayG,+BAAwB;gCACrCtP,MAAM;oCACJ,8DAA8D;oCAC9D,yBAAyB;oCACzByP,KAAK;wCACH1I;wCACA;4CACE+H,KAAK;gDAACvI;gDAA4BvP;6CAAmB;wCACvD;qCACD;gCACH;gCACA8B,SAAS;oCACP,8DAA8D;oCAC9D,4DAA4D;oCAC5D8B,OAAOgV,IAAAA,uCAAgB,EAAC3R,qBAAqB;wCAC3CpC;wCACAwP,OAAO5I,yBAAc,CAACC,qBAAqB;wCAC3C9F;oCACF;gCACF;4BACF;4BACA;gCACEoD,MAAM+G;gCACN8B,aAAapG,yBAAc,CAACI,mBAAmB;gCAC/C/J,SAAS;oCACP8B,OAAOgV,IAAAA,uCAAgB,EAAC3R,qBAAqB;wCAC3CpC;wCACAwP,OAAO5I,yBAAc,CAACI,mBAAmB;wCACzCjG;oCACF;gCACF;4BACF;yBACD;oBACH;oBACA;wBACEoD,MAAM+G;wBACN8B,aAAapG,yBAAc,CAACM,eAAe;wBAC3CjK,SAAS;4BACP8B,OAAOgV,IAAAA,uCAAgB,EAAC3R,qBAAqB;gCAC3CpC;gCACAwP,OAAO5I,yBAAc,CAACM,eAAe;gCACrCnG;4BACF;wBACF;oBACF;iBACD,GACD,EAAE;gBACN,iFAAiF;mBAC7EgB,aAAa7C,OAAO0B,WACpB;oBACE;wBACEuD,MAAMD,cAAcC,IAAI;wBACxBG,SAAS;4BACP,+CAA+C;4BAC/CJ,cAAcI,OAAO;4BACrBuG;4BACA3P;yBACD;wBACD8R,aAAapG,yBAAc,CAACM,eAAe;wBAC3C5J,KAAKoK;wBACLzK,SAAS;4BACPuB,YAAYyK,IAAAA,qBAAY,EAACrJ,cAAc;wBACzC;oBACF;iBACD,GACD,EAAE;gBACN;oBACEqU,OAAO;wBACL;4BACE,GAAG/P,aAAa;4BAChB8I,aAAapG,yBAAc,CAACqB,OAAO;4BACnC3K,KAAK0K;4BACL,kDAAkD;4BAClD,8DAA8D;4BAC9DkM,QAAQ;gCACNC,KAAK;4BACP;wBACF;wBACA;4BACE,GAAGjQ,aAAa;4BAChB8I,aAAapG,yBAAc,CAACwN,OAAO;4BACnC9W,KAAK0K;wBAGP;wBACA;4BACE7D,MAAMD,cAAcC,IAAI;4BACxB6I,aAAapG,yBAAc,CAACa,UAAU;4BACtCnK,KAAKkK;4BACLvK,SAAS;gCACPuB,YAAYyK,IAAAA,qBAAY,EAACrJ,cAAc;gCACvCzB,gBAAgBkK;gCAChBtJ,OAAOgV,IAAAA,uCAAgB,EAAC3R,qBAAqB;oCAC3CpC;oCACAwP,OAAO5I,yBAAc,CAACa,UAAU;oCAChC1G;gCACF;4BACF;wBACF;wBACA;4BACEoD,MAAMD,cAAcC,IAAI;4BACxB6I,aAAapG,yBAAc,CAACyN,UAAU;4BACtC/W,KAAKiK;4BACLtK,SAAS;gCACPuB,YAAYyK,IAAAA,qBAAY,EAACrJ,cAAc;gCACvCzB,gBAAgBkK;gCAChBtJ,OAAOgV,IAAAA,uCAAgB,EAAC3R,qBAAqB;oCAC3CpC;oCACAwP,OAAO5I,yBAAc,CAACyN,UAAU;oCAChCtT;gCACF;4BACF;wBACF;2BACIgB,YACA;4BACE;gCACEoC,MAAMD,cAAcC,IAAI;gCACxB6I,aAAayG,+BAAwB;gCACrCnP,SAASnJ;gCACTmC,KAAKgK;4BACP;4BACA;gCACEnD,MAAMD,cAAcC,IAAI;gCACxBiP,eAAe,IAAIzI,OACjB0I,mCAAwB,CAACW,YAAY;gCAEvC1W,KAAKgK;4BACP;4BACA;gCACEnD,MAAMD,cAAcC,IAAI;gCACxB6I,aAAapG,yBAAc,CAACM,eAAe;gCAC3C,uEAAuE;gCACvE5C,SAASrJ;gCACTqC,KAAKwK;gCACL7K,SAAS;oCACPuB,YAAYyK,IAAAA,qBAAY,EAACrJ,cAAc;gCACzC;4BACF;4BACA;gCACEuE,MAAMD,cAAcC,IAAI;gCACxB6I,aAAapG,yBAAc,CAACI,mBAAmB;gCAC/C1C,SAASnJ;gCACTmC,KAAKyK;gCACL9K,SAAS;oCACPuB,YAAYyK,IAAAA,qBAAY,EAACrJ,cAAc;gCACzC;4BACF;yBACD,GACD,EAAE;wBACN;4BACE,GAAGsE,aAAa;4BAChB5G,KAAK;mCACAoK;gCACHN,eAAeC,KAAK;gCACpBxC;6BACD,CAACnJ,MAAM,CAAC0F;wBACX;qBACD;gBACH;mBAEI,CAACnC,OAAOqV,MAAM,CAACC,mBAAmB,GAClC;oBACE;wBACEpQ,MAAM1J;wBACN+K,QAAQ;wBACRgP,QAAQ;4BAAEvB,KAAKwB,iBAAY;wBAAC;wBAC5BC,YAAY;4BAAEzB,KAAK;gCAAC;6BAAM;wBAAC;wBAC3BG,eAAe;4BACbH,KAAK;gCACH,IAAItI,OAAO0I,mCAAwB,CAACQ,QAAQ;gCAC5C,IAAIlJ,OAAO0I,mCAAwB,CAACC,aAAa;gCACjD,IAAI3I,OAAO0I,mCAAwB,CAACS,iBAAiB;6BACtD;wBACH;wBACArO,SAAS;4BACPkP,OAAOzV;4BACPU;4BACAgV,UAAU3V,OAAO2V,QAAQ;4BACzBjD,aAAa1S,OAAO0S,WAAW;wBACjC;oBACF;iBACD,GACD,EAAE;mBACF5Q,eACA;oBACE;wBACE9D,SAAS;4BACPe,UAAU;gCACR3C,SAASV,QAAQsC,OAAO,CAAC;4BAC3B;wBACF;oBACF;iBACD,GACD2D,WACE;oBACE;wBACE3D,SAAS;4BACPe,UACEiB,OAAOiD,YAAY,CAAC2S,qBAAqB,KAAK,QAC1C;gCACEC,QAAQ;gCACRC,QAAQ;gCACRC,WAAW;gCACXhG,QAAQ;gCACRiG,QAAQ;gCACRC,MAAM;gCACNC,OAAO;gCACPC,IAAI;gCACJ1S,MAAM;gCACN2S,UAAU;gCACVha,SAAS;gCACTia,aAAa;gCACbC,QAAQ;gCACRC,gBAAgB;gCAChBC,KAAK;gCACLC,QAAQ;gCACRC,KAAK;gCACLC,MAAM;gCACNC,IAAI;gCACJC,MAAM;gCACNC,QAAQ;gCACRC,cAAc;4BAChB,IACA;gCACElB,QAAQna,QAAQsC,OAAO,CACrB;gCAEF8X,QAAQpa,QAAQsC,OAAO,CACrB;gCAEF+X,WAAWra,QAAQsC,OAAO,CACxB;gCAEF+R,QAAQrU,QAAQsC,OAAO,CACrB;gCAEFgY,QAAQta,QAAQsC,OAAO,CACrB;gCAEFiY,MAAMva,QAAQsC,OAAO,CACnB;gCAEFkY,OAAOxa,QAAQsC,OAAO,CACpB;gCAEFmY,IAAIza,QAAQsC,OAAO,CACjB;gCAEFyF,MAAM/H,QAAQsC,OAAO,CACnB;gCAEFoY,UAAU1a,QAAQsC,OAAO,CACvB;gCAEF5B,SAASV,QAAQsC,OAAO,CAAC;gCACzB,4BAA4B;gCAC5BqY,aAAa3a,QAAQsC,OAAO,CAC1B;gCAEFsY,QAAQ5a,QAAQsC,OAAO,CACrB;gCAEFuY,gBAAgB7a,QAAQsC,OAAO,CAC7B;gCAEFwY,KAAK9a,QAAQsC,OAAO,CAAC;gCACrByY,QAAQ/a,QAAQsC,OAAO,CACrB;gCAEF0Y,KAAKhb,QAAQsC,OAAO,CAClB;gCAEF,4BAA4B;gCAC5B,+BAA+B;gCAC/B2Y,MAAMjb,QAAQsC,OAAO,CAAC;gCACtB4Y,IAAIlb,QAAQsC,OAAO,CACjB;gCAEF6Y,MAAMnb,QAAQsC,OAAO,CACnB;gCAEF8Y,QAAQpb,QAAQsC,OAAO,CACrB;gCAEF+Y,cAAcrb,QAAQsC,OAAO,CAC3B;4BAEJ;wBACR;oBACF;iBACD,GACD,EAAE;gBACR;oBACE,oEAAoE;oBACpE,6BAA6B;oBAC7BkH,MAAM;oBACN8R,aAAa;gBACf;gBACA,0EAA0E;gBAC1E,yEAAyE;gBACzE,4GAA4G;gBAC5G,gEAAgE;gBAChE,0DAA0D;gBAC1D,iFAAiF;gBACjF,iFAAiF;gBACjF;oBACE9R,MAAM;oBACN8R,aAAa;gBACf;gBACA;oBACE,uEAAuE;oBACvE,uEAAuE;oBACvE,mDAAmD;oBACnD,iEAAiE;oBACjE,mEAAmE;oBACnE,qEAAqE;oBACrE,4DAA4D;oBAC5D9R,MAAM;oBACN7G,KAAK,CAAC,EAAE8V,aAAa,EAA6B;4BAE9CA;wBADF,MAAM8C,QAAQ,AACZ9C,CAAAA,EAAAA,uBAAAA,cAAcjF,KAAK,CAAC,uCAApBiF,oBAAwC,CAAC,EAAE,KAAI,EAAC,EAChD5X,KAAK,CAAC;wBAER,OAAO;4BACL;gCACEgK,QAAQ;gCACRC,SAAS;oCACPyQ;oCACArQ,aAAanD,aAAI,CAACC,IAAI,CACpB3D,KACAC,CAAAA,0BAAAA,OAAQwD,OAAO,KAAI,SACnB,SACA;gCAEJ;gCACA,gEAAgE;gCAChE,2DAA2D;gCAC3D,gBAAgB;gCAChB0T,OAAO,wBAAwB/C;4BACjC;yBACD;oBACH;gBACF;gBACA;oBACEnW,SAAS;wBACP8B,OAAO;4BACLqX,MAAMC,+BAAiB;wBACzB;oBACF;gBACF;aACD;QACH;QACAnN,SAAS;YACPjI,gBACE,IAAI2P,gBAAO,CAAC0F,6BAA6B,CACvC,6BACA,SAAU5G,QAAQ;gBAChB,MAAM6G,aAAa7T,aAAI,CAAC8T,QAAQ,CAC9B9G,SAAS7C,OAAO,EAChB;gBAEF,MAAM2C,QAAQE,SAAS5C,WAAW,CAACE,WAAW;gBAC9C,IAAIyJ;gBAEJ,OAAQjH;oBACN,KAAK5I,yBAAc,CAACI,mBAAmB;oBACvC,KAAKJ,yBAAc,CAACC,qBAAqB;oBACzC,KAAKD,yBAAc,CAACM,eAAe;oBACnC,KAAKN,yBAAc,CAAC8P,aAAa;wBAC/BD,UAAU;wBACV;oBACF;wBACEA,UAAU;gBACd;gBACA/G,SAAS7C,OAAO,GAAG,CAAC,+BAA+B,EAAE4J,QAAQ,mBAAmB,EAAEF,YAAY;YAChG;YAEJrX,OAAO,IAAIyX,gDAAuB,CAAC;gBAAEC,gBAAgB;YAAE;YACvD1X,OACE0B,YACCO,CAAAA,WAEG,IAAK0V,CAAAA,IAAAA,gCAAqB,GAAC,MAC3B,IAAIC,kCAAyB,CAAClG,gBAAO,CAAA;YAC3C,6GAA6G;YAC5GhQ,CAAAA,YAAYG,YAAW,KACtB,IAAI6P,gBAAO,CAACmG,aAAa,CAAC;gBACxB,0CAA0C;gBAC1CC,QAAQ;oBAACrc,QAAQsC,OAAO,CAAC;oBAAW;iBAAS;gBAC7C,sDAAsD;gBACtD,GAAI2D,YAAY;oBAAEvF,SAAS;wBAACV,QAAQsC,OAAO,CAAC;qBAAW;gBAAC,CAAC;YAC3D;YACFga,IAAAA,mCAAkB,EAAC;gBACjBC,aAAa;gBACbjY;gBACAC;gBACAuD;gBACAhC;gBACAkB;gBACAf;gBACAG;gBACAW;gBACAT;gBACAX;YACF;YACAM,YACE,IAAIuW,wCAAmB,CAAC;gBACtB7H,UAAU8H,mCAAuB;gBACjCrX;gBACAM;gBACAgX,cAAc,CAAC,OAAO,EAAEC,8CAAkC,CAAC,GAAG,CAAC;gBAC/DpY;YACF;YACF,oDAAoD;YACpD,CAACiC,YAAaP,CAAAA,YAAYG,YAAW,KAAM,IAAIwW,wCAAc;YAC7DtW,gBACE,CAAC/B,OACD,IAAKvE,CAAAA,QAAQ,kDAAiD,EAC3D6c,sBAAsB,CACvB;gBACE7R,SAAS3G;gBACTqB,QAAQA;gBACRN,UAAUA;gBACVsM,cAAcpN,OAAOiD,YAAY,CAACmK,YAAY;gBAC9CoL,uBAAuBxY,OAAOwY,qBAAqB;gBACnDC,eAAe3V;gBACf4V,cAAc,EAAE;gBAChB/X;YACF;YAEJ,4EAA4E;YAC5E,yEAAyE;YACzE,0EAA0E;YAC1E,kEAAkE;YAClEX,OAAO2Y,2BAA2B,IAChC,IAAIhH,gBAAO,CAACiH,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;eACE7Y,MACA,AAAC,CAAA;gBACC,0FAA0F;gBAC1F,qGAAqG;gBACrG,MAAM,EAAE8Y,6BAA6B,EAAE,GACrCrd,QAAQ;gBACV,MAAMsd,aAAoB;oBACxB,IAAID,8BAA8B;wBAChCtR,kBAAkB3E;oBACpB;iBACD;gBAED,IAAInB,YAAYG,cAAc;oBAC5BkX,WAAWnU,IAAI,CAAC,IAAI8M,gBAAO,CAACsH,0BAA0B;gBACxD;gBAEA,OAAOD;YACT,CAAA,MACA,EAAE;YACN,CAAC/Y,OACC,IAAI0R,gBAAO,CAACiH,YAAY,CAAC;gBACvBC,gBAAgB;gBAChBC,eAAe;YACjB;YACFrW,2BACE,IAAIyW,4BAAmB,CAAC;gBACtBjZ;gBACAwY,eAAe3V;gBACfqW,eAAerX;gBACf0B,SAAS,CAACvD,MAAMuD,UAAUM;YAC5B;YACF,iEAAiE;YACjE,wDAAwD;YACxDhC,gBACE,IAAIsX,yBAAgB,CAAC;gBACnBnZ;gBACAoZ,YAAY,CAACpZ,OAAO,CAAC,GAACD,2BAAAA,OAAOiD,YAAY,CAACqW,GAAG,qBAAvBtZ,yBAAyBuZ,SAAS;gBACxDvY;gBACAwY,kBAAkB;oBAChBC,iBAAiBhZ;oBACjBiZ,oCAAoChZ;oBACpC,GAAGe,gBAAgB;gBACrB;YACF;YACFE,YACE,IAAIgY,4BAAmB,CAAC;gBACtBlZ;gBACAO;gBACAH;gBACA4X,eAAe3V;gBACfvB;YACF;YACFW,WACI,IAAI0X,4CAAqB,CAAC;gBAAEzY;YAAe,KAC3C,IAAI0Y,gCAAe,CAAC;gBAAE1Y;gBAAgBuF,SAAS3G;YAAI;YACvD,IAAI+Z,4CAAqB;YACzBnY,YACE,IAAIoY,8BAAc,CAAC;gBACjB,yDAAyD;gBACzDC,UAAUte,QAAQsC,OAAO,CAAC;gBAC1Bic,UAAU7d,QAAQC,GAAG,CAAC6d,cAAc;gBACpChP,MAAM,CAAC,uBAAuB,EAAEjL,MAAM,KAAK,UAAU,GAAG,CAAC;gBACzDuR,UAAU;gBACVrN,MAAM;oBACJ,CAACgW,wDAA4C,CAAC,EAAE;oBAChD,gCAAgC;oBAChCC,WAAW;gBACb;YACF;YACFtX,aAAanB,YAAY,IAAI0Y,8CAAsB,CAAC;gBAAEpa;YAAI;YAC1D6C,aACGnB,CAAAA,WACG,IAAI2Y,mDAA6B,CAAC;gBAChCra;gBACAmB;gBACAmZ,uBAAuB,CAAC,CAACva,OAAOiD,YAAY,CAACuX,SAAS;YACxD,KACA,IAAInY,wBAAwB;gBAC1BjB;gBACAnB;gBACA6B;gBACApB;YACF,EAAC;YACPoC,aACE,CAACnB,YACD,IAAI8Y,gCAAe,CAAC;gBAClB1a;gBACAyD,SAASxD,OAAOwD,OAAO;gBACvBpC;gBACAnB;gBACA6B;gBACAmH,gBAAgBjJ,OAAOiJ,cAAc;gBACrC/F,aAAaF;gBACb0X,iBAAiB1a,OAAOiD,YAAY,CAAC0X,SAAS;gBAC9C1Z;gBACAC;YACF;YACF,CAACjB,OACC0B,YACA,CAAC,GAAC3B,4BAAAA,OAAOiD,YAAY,CAACqW,GAAG,qBAAvBtZ,0BAAyBuZ,SAAS,KACpC,IAAIqB,sDAA0B,CAAC5a,OAAOiD,YAAY,CAACqW,GAAG,CAACC,SAAS;YAClE5X,YACE,IAAIkZ,8CAAsB,CAAC;gBACzBzZ;YACF;YACF,CAACc,YACC,CAACjC,OACD0B,YACA3B,OAAOiD,YAAY,CAAC6X,WAAW,IAC/B,IAAIC,oCAAiB,CAAC/a,OAAOiD,YAAY,CAAC6X,WAAW,KAAK;YAC5DrO;YACA,CAACvK,YACC,CAACjC,OACD+B,gBACA,IAAI,AACFtG,CAAAA,QAAQ,sDAAqD,EAC7DgR,eAAe,CAAC,IAAIC;YACxBR,mCACE,IAAI,AACFzQ,CAAAA,QAAQ,iDAAgD,EACxDyU,OAAO,CAAC;gBACRxP;gBACA,GAAGX,OAAOiD,YAAY,CAACmJ,mBAAmB;YAC5C;SACH,CAAC3P,MAAM,CAAC0F;IACX;IAEA,wCAAwC;IACxC,mEAAmE;IACnE,IAAI/B,mBAAmB,CAACA,gBAAgB4a,UAAU,EAAE;YAClDnd,gCAAAA;SAAAA,0BAAAA,cAAcG,OAAO,sBAArBH,iCAAAA,wBAAuBiB,OAAO,qBAA9BjB,+BAAgCgH,IAAI,CAACzE,gBAAgB6a,OAAO;IAC9D;KAIApd,yBAAAA,cAAcG,OAAO,sBAArBH,iCAAAA,uBAAuBoM,OAAO,qBAA9BpM,+BAAgCqd,OAAO,CACrC,IAAIC,wCAAmB,CACrBjb,CAAAA,6BAAAA,6BAAAA,SAAUiH,eAAe,qBAAzBjH,2BAA2B0K,KAAK,KAAI,CAAC,GACrCxK;IAIJ,MAAMsB,iBAAiB7D;IAEvB,IAAIiE,cAAc;YAChBJ,8BAAAA,wBAMAA,+BAAAA,yBAMAA,+BAAAA;SAZAA,yBAAAA,eAAezE,MAAM,sBAArByE,+BAAAA,uBAAuBzD,KAAK,qBAA5ByD,6BAA8BwZ,OAAO,CAAC;YACpChW,MAAM;YACNqB,QAAQ;YACRrJ,MAAM;YACNiX,eAAe;QACjB;SACAzS,0BAAAA,eAAezE,MAAM,sBAArByE,gCAAAA,wBAAuBzD,KAAK,qBAA5ByD,8BAA8BwZ,OAAO,CAAC;YACpCzF,YAAY;YACZlP,QAAQ;YACRrJ,MAAM;YACNqT,OAAO5I,yBAAc,CAACyT,SAAS;QACjC;SACA1Z,0BAAAA,eAAezE,MAAM,sBAArByE,gCAAAA,wBAAuBzD,KAAK,qBAA5ByD,8BAA8BwZ,OAAO,CAAC;YACpCnN,aAAapG,yBAAc,CAACyT,SAAS;YACrCle,MAAM;QACR;IACF;IAEAwE,eAAe2Z,WAAW,GAAG;QAC3BC,QAAQ;QACRC,iBAAiB;QACjBC,WAAWld,MAAMC,OAAO,CAACyB,OAAOiD,YAAY,CAACwY,UAAU,IACnD;YACEC,aAAa1b,OAAOiD,YAAY,CAACwY,UAAU;YAC3CE,eAAelY,aAAI,CAACC,IAAI,CAAC3D,KAAK;YAC9B6b,kBAAkBnY,aAAI,CAACC,IAAI,CAAC3D,KAAK;QACnC,IACAC,OAAOiD,YAAY,CAACwY,UAAU,GAC5B;YACEE,eAAelY,aAAI,CAACC,IAAI,CAAC3D,KAAK;YAC9B6b,kBAAkBnY,aAAI,CAACC,IAAI,CAAC3D,KAAK;YACjC,GAAGC,OAAOiD,YAAY,CAACwY,UAAU;QACnC,IACA3X;IACR;IAEApC,eAAezE,MAAM,CAAEgY,MAAM,GAAG;QAC9B4G,YAAY;YACV3G,KAAK;QACP;IACF;IACAxT,eAAezE,MAAM,CAAE6e,SAAS,GAAG;QACjCC,OAAO;YACL1L,UAAU;QACZ;IACF;IAEA,IAAI,CAAC3O,eAAe8Q,MAAM,EAAE;QAC1B9Q,eAAe8Q,MAAM,GAAG,CAAC;IAC3B;IACA,IAAI7Q,UAAU;QACZD,eAAe8Q,MAAM,CAACwJ,YAAY,GAAG;IACvC;IAEA,IAAIra,YAAYG,cAAc;QAC5BJ,eAAe8Q,MAAM,CAACyJ,mBAAmB,GAAG;YAAC;SAAS;IACxD;IAEA,iDAAiD;IACjD,wDAAwD;IACxD,oDAAoD;IACpDva,eAAewa,QAAQ,GAAG,CAAC;IAC3B,IAAI9f,QAAQ2K,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCpF,eAAewa,QAAQ,CAACC,YAAY,GAAG;YACrC;SACD;IACH,OAAO;QACLza,eAAewa,QAAQ,CAACC,YAAY,GAAG;YAAC;SAA+B;IACzE;IACA,IAAI/f,QAAQ2K,QAAQ,CAACD,GAAG,KAAK,KAAK;QAChCpF,eAAewa,QAAQ,CAACE,cAAc,GAAG;YACvC;SACD;IACH;IAEA,IAAInc,KAAK;QACP,IAAI,CAACyB,eAAegN,YAAY,EAAE;YAChChN,eAAegN,YAAY,GAAG,CAAC;QACjC;QAEA,2EAA2E;QAC3E,2CAA2C;QAC3C,IAAI,CAAC5L,WAAW;YACdpB,eAAegN,YAAY,CAAC2N,eAAe,GAAG;QAChD;QACA3a,eAAegN,YAAY,CAAC4N,WAAW,GAAG;IAC5C;IAEA,MAAMC,aAAaC,KAAKC,SAAS,CAAC;QAChC9X,sBAAsB,EAAE3E,2BAAAA,wBAAAA,OAAQiD,YAAY,qBAApBjD,sBAAsB2E,sBAAsB;QACpE0G,aAAarL,OAAOqL,WAAW;QAC/BpC,gBAAgBA;QAChByT,eAAe1c,OAAO0c,aAAa;QACnCC,uBACE3c,OAAO4c,aAAa,KAAK,QACrB9Y,YACA9D,OAAO4c,aAAa,CAACC,QAAQ;QACnCC,6BAA6B,CAAC,CAAC9c,OAAO8c,2BAA2B;QACjEC,iBAAiB/c,OAAO+c,eAAe;QACvCC,aAAahd,OAAOiD,YAAY,CAAC+Z,WAAW;QAC5CC,mBAAmBjd,OAAOiD,YAAY,CAACga,iBAAiB;QACxDC,mBAAmBld,OAAOiD,YAAY,CAACia,iBAAiB;QACxDha,aAAalD,OAAOiD,YAAY,CAACC,WAAW;QAC5CyS,UAAU3V,OAAO2V,QAAQ;QACzBgD,6BAA6B3Y,OAAO2Y,2BAA2B;QAC/DjG,aAAa1S,OAAO0S,WAAW;QAC/B3P;QACAoW,eAAerX;QACff;QACA4Q,SAAS,CAAC,CAAC3R,OAAO2R,OAAO;QACzBjP;QACAya,WAAWxZ;QACXoJ,aAAa,GAAE/M,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiB+M,aAAa;QAC7CD,qBAAqB,GAAE9M,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiB8M,qBAAqB;QAC7DD,gBAAgB,GAAE7M,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiB6M,gBAAgB;QACnDD,KAAK,GAAE5M,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiB4M,KAAK;QAC7BK,OAAO,GAAEjN,oBAAAA,OAAO8E,QAAQ,qBAAf9E,kBAAiBiN,OAAO;QACjChG,mBAAmBjH,OAAOiH,iBAAiB;QAC3CmW,iBAAiBpd,OAAOqV,MAAM,CAACgI,UAAU;QACzCC,qBAAqBtd,OAAOiD,YAAY,CAACqa,mBAAmB;QAC5DC,kBAAkBvd,OAAOiD,YAAY,CAACsa,gBAAgB;QACtD1W,yBAAyBnG;IAC3B;IAEA,MAAM8c,QAAa;QACjBtgB,MAAM;QACN,mFAAmF;QACnFugB,sBAAsBxd,MAAM,IAAIyd;QAChC,YAAY;QACZ,kHAAkH;QAClH,qBAAqB;QACrB,iDAAiD;QACjD5hB,SAAS,GAAG6X,UAAU,CAAC,EAAEvX,QAAQC,GAAG,CAAC6d,cAAc,CAAC,CAAC,EAAEqC,YAAY;QACnEoB,gBAAgBla,aAAI,CAACC,IAAI,CAACF,SAAS,SAAS;QAC5C,gIAAgI;QAChI,8GAA8G;QAC9G,yGAAyG;QACzG,kEAAkE;QAClEoa,aAAa3d,MAAM,SAAS;IAC9B;IAEA,oFAAoF;IACpF,IAAID,OAAO2R,OAAO,IAAI3R,OAAOoK,UAAU,EAAE;QACvCoT,MAAMK,iBAAiB,GAAG;YACxB7d,QAAQ;gBAACA,OAAOoK,UAAU;aAAC;YAC3B,uGAAuG;YACvG0T,gBAAgB,EAAE;QACpB;IACF,OAAO;QACLN,MAAMK,iBAAiB,GAAG;YACxB,uGAAuG;YACvGC,gBAAgB,EAAE;QACpB;IACF;KACApc,0BAAAA,eAAeuI,OAAO,qBAAtBvI,wBAAwBmD,IAAI,CAAC,CAACC;QAC5BA,SAASiZ,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,2BAA2B,CAACC;YAClD,MAAML,oBAAoBK,MAAMC,WAAW,CAACN,iBAAiB;YAC7D,MAAMO,cAAc3a,aAAI,CAAC4a,OAAO,CAAC3iB,QAAQsC,OAAO,CAAC;YACjD,sFAAsF;YACtF,2EAA2E;YAC3E,KAAK,MAAMsgB,OAAOT,kBAAmB;gBACnC,IAAIS,IAAI3N,UAAU,CAACyN,cAAc;oBAC/BP,kBAAkBU,MAAM,CAACD;gBAC3B;YACF;QACF;IACF;IAEA5c,eAAe8b,KAAK,GAAGA;IAEvB,IAAIphB,QAAQC,GAAG,CAACmiB,oBAAoB,EAAE;QACpC,MAAMC,QAAQriB,QAAQC,GAAG,CAACmiB,oBAAoB,CAAC5Z,QAAQ,CAAC;QACxD,MAAM8Z,gBACJtiB,QAAQC,GAAG,CAACmiB,oBAAoB,CAAC5Z,QAAQ,CAAC;QAC5C,MAAM+Z,gBACJviB,QAAQC,GAAG,CAACmiB,oBAAoB,CAAC5Z,QAAQ,CAAC;QAC5C,MAAMga,gBACJxiB,QAAQC,GAAG,CAACmiB,oBAAoB,CAAC5Z,QAAQ,CAAC;QAC5C,MAAMia,gBACJziB,QAAQC,GAAG,CAACmiB,oBAAoB,CAAC5Z,QAAQ,CAAC;QAE5C,MAAMka,UACJ,AAACJ,iBAAiB/c,YAAcgd,iBAAiBlc;QACnD,MAAMsc,UACJ,AAACH,iBAAiBjd,YAAckd,iBAAiBpc;QAEnD,MAAMuc,aAAa,CAACP,SAAS,CAACK,WAAW,CAACC;QAE1C,IAAIC,cAAcP,OAAO;YACvB/c,eAAeud,qBAAqB,GAAG;gBACrCC,OAAO;gBACPC,OAAO;YACT;QACF;QAEA,IAAIH,cAAcF,SAAS;YACzBpd,eAAeuI,OAAO,CAAEpF,IAAI,CAAC,CAACC;gBAC5BA,SAASiZ,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/C5gB,QAAQ8hB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbC,QAAQ;wBACRC,SAASP,aAAa,QAAQ;oBAChC;gBAEJ;YACF;QACF,OAAO,IAAID,SAAS;YAClBrd,eAAeuI,OAAO,CAAEpF,IAAI,CAAC,CAACC;gBAC5BA,SAASiZ,KAAK,CAACC,IAAI,CAACC,GAAG,CAAC,wBAAwB,CAACC;oBAC/C5gB,QAAQ8hB,GAAG,CACTlB,MAAMmB,QAAQ,CAAC;wBACbG,QAAQ;wBACRF,QAAQ;wBACRG,SAAS;oBACX;gBAEJ;YACF;QACF;QAEA,IAAIX,SAAS;YACX,MAAMY,iBACJ/N,gBAAO,CAAC+N,cAAc;YACxBhe,eAAeuI,OAAO,CAAEpF,IAAI,CAC1B,IAAI6a,eAAe;gBACjBZ,SAAS;YACX;YAEFpd,eAAeod,OAAO,GAAG;QAC3B;IACF;IAEAjhB,gBAAgB,MAAM8hB,IAAAA,0BAAkB,EAAC9hB,eAAe;QACtDyC;QACAsf,eAAe7f;QACf8f,eAAe/e,WACX,IAAI4K,OAAOoU,IAAAA,gCAAkB,EAACrc,aAAI,CAACC,IAAI,CAAC5C,UAAU,CAAC,IAAI,CAAC,MACxDgD;QACJhB;QACAid,eAAe9f;QACfwG,UAAUhE;QACV0W,eAAerX;QACfke,WAAWre,YAAYG;QACvB4Q,aAAa1S,OAAO0S,WAAW,IAAI;QACnCuN,aAAajgB,OAAOigB,WAAW;QAC/BnD,6BAA6B9c,OAAO8c,2BAA2B;QAC/DoD,QAAQlgB,OAAOkgB,MAAM;QACrBjd,cAAcjD,OAAOiD,YAAY;QACjCqS,qBAAqBtV,OAAOqV,MAAM,CAACC,mBAAmB;QACtD9Q,mBAAmBxE,OAAOwE,iBAAiB;QAC3C+Y,kBAAkBvd,OAAOiD,YAAY,CAACsa,gBAAgB;IACxD;IAEA,0BAA0B;IAC1B1f,cAAc2f,KAAK,CAACtS,IAAI,GAAG,GAAGrN,cAAcqN,IAAI,CAAC,CAAC,EAAErN,cAAcsiB,IAAI,GACpEtf,gBAAgB,cAAc,IAC9B;IAEF,IAAIZ,KAAK;QACP,IAAIpC,cAAcZ,MAAM,EAAE;YACxBY,cAAcZ,MAAM,CAACmjB,WAAW,GAAG,CAACnjB,UAClC,CAACuD,mBAAmB0E,IAAI,CAACjI,QAAOwT,QAAQ;QAC5C,OAAO;YACL5S,cAAcZ,MAAM,GAAG;gBACrBmjB,aAAa,CAACnjB,UAAgB,CAACuD,mBAAmB0E,IAAI,CAACjI,QAAOwT,QAAQ;YACxE;QACF;IACF;IAEA,IAAI4P,kBAAkBxiB,cAAcR,OAAO;IAC3C,IAAI,OAAO2C,OAAO2R,OAAO,KAAK,YAAY;YACd9T,wBA0CtB6D,6BAKKA;QA/CT,MAAM4e,qBAAoBziB,yBAAAA,cAAcoM,OAAO,qBAArBpM,uBAAuB+E,MAAM;QAEvD/E,gBAAgBmC,OAAO2R,OAAO,CAAC9T,eAAe;YAC5CkC;YACAE;YACAwG,UAAUhE;YACVhC;YACAT;YACAmI;YACAoY,YAAY3jB,OAAOuO,IAAI,CAACvK,aAAagC,MAAM;YAC3C+O,SAAAA,gBAAO;YACP,GAAIlP,0BACA;gBACE+d,aAAa1e,eAAe,SAAS;YACvC,IACA,CAAC,CAAC;QACR;QAEA,IAAI2K,mBAAmB6T,mBAAmB;gBACfziB;YAAzB,MAAM4iB,oBAAmB5iB,0BAAAA,cAAcoM,OAAO,qBAArBpM,wBAAuB+E,MAAM;YACtD,IAAI6d,kBAAkB;gBACpB,MAAMC,iBAAiBD,qBAAqBH;gBAC5C7T,gBAAgBkU,QAAQ,CAAC,kBAAkBD,iBAAiB,IAAI;YAClE;QACF;QAEA,IAAI,CAAC7iB,eAAe;YAClB,MAAM,qBAGL,CAHK,IAAI9B,MACR,CAAC,6GAA6G,EAAEiE,OAAO4gB,cAAc,CAAC,GAAG,CAAC,GACxI,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,IAAI3gB,OAAOogB,oBAAoBxiB,cAAcR,OAAO,EAAE;YACpDQ,cAAcR,OAAO,GAAGgjB;YACxBljB,qBAAqBkjB;QACvB;QAEA,wDAAwD;QACxD,MAAM3e,iBAAiB7D;QAEvB,0EAA0E;QAC1E,IAAI6D,EAAAA,8BAAAA,eAAe2Z,WAAW,qBAA1B3Z,4BAA4Bmf,eAAe,MAAK,MAAM;YACxDnf,eAAe2Z,WAAW,CAACwF,eAAe,GAAG;gBAC3CC,SAAS;YACX;QACF,OAAO,IACL,SAAOpf,+BAAAA,eAAe2Z,WAAW,qBAA1B3Z,6BAA4Bmf,eAAe,MAAK,YACvDnf,eAAe2Z,WAAW,CAACwF,eAAe,CAACC,OAAO,KAAK,OACvD;YACApf,eAAe2Z,WAAW,CAACwF,eAAe,CAACC,OAAO,GAAG;QACvD;QAEA,IAAI,OAAO,AAACjjB,cAAsBkjB,IAAI,KAAK,YAAY;YACrDzjB,QAAQC,IAAI,CACV;QAEJ;IACF;IACA,MAAMU,QAAQJ,EAAAA,wBAAAA,cAAcZ,MAAM,qBAApBY,sBAAsBI,KAAK,KAAI,EAAE;IAE/C,MAAM+iB,gBAAgB/iB,MAAMgjB,IAAI,CAC9B,CAAC9iB,OACC,AAACA,QACC,OAAOA,SAAS,YAChBA,KAAKoI,MAAM,KAAK,uBAChB,UAAUpI,QACVA,KAAK+G,IAAI,YAAYwG,UACrBvN,KAAK+G,IAAI,CAACA,IAAI,CAAC,WACjB;IAGJ,IAAI8b,iBAAiBle,WAAW;QAC9B,wEAAwE;QACxE,qEAAqE;QACrE,0CAA0C;QAC1C7E,MAAM4G,IAAI,CAAC;YACTK,MAAM8b,cAAc9b,IAAI;YACxB8P,OAAO;gBACLrN,yBAAc,CAACC,qBAAqB;gBACpCD,yBAAc,CAACI,mBAAmB;gBAClCJ,yBAAc,CAACM,eAAe;aAC/B,CAAC0D,GAAG,CAAC,CAAC4E,QAAW,CAAA;oBAChBxC,aAAawC;oBACbvS,SAAS;wBACP8B,OAAOgV,IAAAA,uCAAgB,EAAC3R,qBAAqB;4BAC3CpC;4BACAwP;4BACAzO;wBACF;oBACF;gBACF,CAAA;QACF;IACF;IAEA,IAAI,CAAC9B,OAAOqV,MAAM,CAACC,mBAAmB,EAAE;QACtC,MAAM4L,gBAAgBjjB,MAAMgjB,IAAI,CAC9B,CAAC9iB,OACCA,QAAQ,OAAOA,SAAS,YAAYA,KAAKoI,MAAM,KAAK;QAExD,IAAIya,iBAAiBE,iBAAiB,OAAOA,kBAAkB,UAAU;YACvE,uDAAuD;YACvD,wDAAwD;YACxD,8CAA8C;YAC9CA,cAAchc,IAAI,GAAG;QACvB;IACF;IAEA,IACElF,OAAOiD,YAAY,CAACke,SAAS,MAC7BtjB,yBAAAA,cAAcZ,MAAM,qBAApBY,uBAAsBI,KAAK,KAC3BJ,cAAcoM,OAAO,EACrB;QACA,kEAAkE;QAClE,iEAAiE;QACjE,kJAAkJ;QAClJ,MAAMmX,oBAAoB;YAAC;SAA8B;QACzD,MAAMC,aAAa;YACjBhc,SAAS+b;YACT7L,QAAQ6L;YACRlkB,MAAM;QACR;QAEA,MAAMokB,WAAW,EAAE;QACnB,MAAMC,aAAa,EAAE;QAErB,KAAK,MAAMpjB,QAAQN,cAAcZ,MAAM,CAACgB,KAAK,CAAE;YAC7C,IAAI,CAACE,QAAQ,OAAOA,SAAS,UAAU;YACvC,IAAIA,KAAKH,OAAO,EAAE;gBAChBsjB,SAASzc,IAAI,CAAC1G;YAChB,OAAO;gBACL,IACEA,KAAK6W,KAAK,IACV,CAAE7W,CAAAA,KAAK+G,IAAI,IAAI/G,KAAKkH,OAAO,IAAIlH,KAAKsS,QAAQ,IAAItS,KAAKoX,MAAM,AAAD,GAC1D;oBACApX,KAAK6W,KAAK,CAAC9W,OAAO,CAAC,CAACO,IAAM8iB,WAAW1c,IAAI,CAACpG;gBAC5C,OAAO;oBACL8iB,WAAW1c,IAAI,CAAC1G;gBAClB;YACF;QACF;QAEAN,cAAcZ,MAAM,CAACgB,KAAK,GAAG;eACvBqjB;YACJ;gBACEtM,OAAO;uBAAIuM;oBAAYF;iBAAW;YACpC;SACD;IACH;IAEA,8DAA8D;IAC9D,IAAI,OAAOrhB,OAAOwhB,oBAAoB,KAAK,YAAY;QACrD,MAAMhb,UAAUxG,OAAOwhB,oBAAoB,CAAC;YAC1CnP,cAAcxU,cAAcwU,YAAY;QAC1C;QACA,IAAI7L,QAAQ6L,YAAY,EAAE;YACxBxU,cAAcwU,YAAY,GAAG7L,QAAQ6L,YAAY;QACnD;IACF;IAEA,SAASoP,YAAYtjB,IAA0C;QAC7D,IAAI,CAACA,MAAM;YACT,OAAO;QACT;QAEA,MAAMujB,YAAY;YAChB;YACA;YACA;YACA;YACA;SACD;QAED,IAAIvjB,gBAAgBuN,UAAUgW,UAAUljB,IAAI,CAAC,CAACmjB,QAAUxjB,KAAK+G,IAAI,CAACyc,SAAS;YACzE,OAAO;QACT;QAEA,IAAI,OAAOxjB,SAAS,YAAY;YAC9B,IACEujB,UAAUljB,IAAI,CAAC,CAACmjB;gBACd,IAAI;oBACF,IAAIxjB,KAAKwjB,QAAQ;wBACf,OAAO;oBACT;gBACF,EAAE,OAAM,CAAC;gBACT,OAAO;YACT,IACA;gBACA,OAAO;YACT;QACF;QAEA,IAAIrjB,MAAMC,OAAO,CAACJ,SAASA,KAAKK,IAAI,CAACijB,cAAc;YACjD,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAMG,mBACJ/jB,EAAAA,yBAAAA,cAAcZ,MAAM,sBAApBY,8BAAAA,uBAAsBI,KAAK,qBAA3BJ,4BAA6BW,IAAI,CAC/B,CAACL,OAAcsjB,YAAYtjB,KAAK+G,IAAI,KAAKuc,YAAYtjB,KAAKiH,OAAO,OAC9D;IAEP,IAAIwc,kBAAkB;YAYhB/jB,8BAAAA,wBAWAA,yBAMAA,uCAAAA;QA5BJ,kCAAkC;QAClC,IAAI4E,yBAAyB;YAC3BnF,QAAQC,IAAI,CACVC,IAAAA,kBAAM,EAACC,IAAAA,gBAAI,EAAC,gBACVA,IAAAA,gBAAI,EACF,8FAEF;QAEN;QAEA,KAAII,yBAAAA,cAAcZ,MAAM,sBAApBY,+BAAAA,uBAAsBI,KAAK,qBAA3BJ,6BAA6B+E,MAAM,EAAE;YACvC,6BAA6B;YAC7B/E,cAAcZ,MAAM,CAACgB,KAAK,CAACC,OAAO,CAAC,CAACO;gBAClC,IAAI,CAACA,KAAK,OAAOA,MAAM,UAAU;gBACjC,IAAIH,MAAMC,OAAO,CAACE,EAAEuW,KAAK,GAAG;oBAC1BvW,EAAEuW,KAAK,GAAGvW,EAAEuW,KAAK,CAACvY,MAAM,CACtB,CAAColB,IAAM,AAACA,CAAS,CAACC,OAAOC,GAAG,CAAC,qBAAqB,KAAK;gBAE3D;YACF;QACF;QACA,KAAIlkB,0BAAAA,cAAcoM,OAAO,qBAArBpM,wBAAuB+E,MAAM,EAAE;YACjC,gCAAgC;YAChC/E,cAAcoM,OAAO,GAAGpM,cAAcoM,OAAO,CAACxN,MAAM,CAClD,CAACC,IAAM,AAACA,EAAUslB,iBAAiB,KAAK;QAE5C;QACA,KAAInkB,8BAAAA,cAAc6Q,YAAY,sBAA1B7Q,wCAAAA,4BAA4B6T,SAAS,qBAArC7T,sCAAuC+E,MAAM,EAAE;YACjD,uBAAuB;YACvB/E,cAAc6Q,YAAY,CAACgD,SAAS,GAClC7T,cAAc6Q,YAAY,CAACgD,SAAS,CAACjV,MAAM,CACzC,CAACwlB,IAAM,AAACA,EAAUD,iBAAiB,KAAK;QAE9C;IACF;IAEA,yEAAyE;IACzE,IAAI/hB,OAAO0B,UAAU;QACnBxG,mBAAmB0C,eAAesK,eAAeC,KAAK;IACxD;IAEA,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0BAA0B;IAC1B,MAAM8Z,gBAAqBrkB,cAAcuU,KAAK;IAC9C,IAAI,OAAO8P,kBAAkB,aAAa;QACxC,MAAMC,eAAe;YACnB,MAAM/P,QACJ,OAAO8P,kBAAkB,aACrB,MAAMA,kBACNA;YACN,0CAA0C;YAC1C,IACE5Y,iBACAhL,MAAMC,OAAO,CAAC6T,KAAK,CAAC,UAAU,KAC9BA,KAAK,CAAC,UAAU,CAACxP,MAAM,GAAG,GAC1B;gBACA,MAAMwf,eAAe9Y,aAAa,CAChCK,4CAAgC,CACjC;gBACDyI,KAAK,CAACzI,4CAAgC,CAAC,GAAG;uBACrCyI,KAAK,CAAC,UAAU;oBACnBgQ;iBACD;YACH;YACA,OAAOhQ,KAAK,CAAC,UAAU;YAEvB,KAAK,MAAMlH,QAAQtO,OAAOuO,IAAI,CAACiH,OAAQ;gBACrCA,KAAK,CAAClH,KAAK,GAAGmX,IAAAA,2BAAkB,EAAC;oBAC/BC,OAAOlQ,KAAK,CAAClH,KAAK;oBAClBvK;oBACAuK;oBACApI;gBACF;YACF;YAEA,OAAOsP;QACT;QACA,sCAAsC;QACtCvU,cAAcuU,KAAK,GAAG+P;IACxB;IAEA,IAAI,CAACliB,OAAO,OAAOpC,cAAcuU,KAAK,KAAK,YAAY;QACrD,6BAA6B;QAC7BvU,cAAcuU,KAAK,GAAG,MAAMvU,cAAcuU,KAAK;IACjD;IAEA,OAAOvU;AACT"}