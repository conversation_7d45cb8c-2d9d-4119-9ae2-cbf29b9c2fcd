"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Users, TrendingUp, MapPin, Star, Award, Target,
  PlayCircle, Heart, MessageCircle, Share2,
  Calendar, Clock, DollarSign, TrendingDown,
  CheckCircle, AlertCircle, BarChart3, PieChart,
  Video, Briefcase, Eye, ThumbsUp, ArrowLeft,
  Loader2, AlertTriangle
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { dataService, type InfluencerData, type ApiResponse } from "@/lib/data-service"
import { DataQuality } from "@/components/data-status"
import {
  formatToWan,
  formatPercentage,
  formatTimestamp,
  formatDuration,
  parseJsonSafely,
  getIndustryName,
  calculateAverage,
  formatCPM,
  formatRankPercentage,
  getStatusColor,
  getStatusText,
  formatImageUrl,
  calculateThemePercentage,
  formatPrice,
  calculateScore,
  validateData
} from "@/lib/data-utils"

interface LoadingState {
  isLoading: boolean
  error: string | null
  data: InfluencerData | null
}

export default function TalentProfilePage() {
  const params = useParams()
  const router = useRouter()
  const talentId = params.talentId as string

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: true,
    error: null,
    data: null
  })
  
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedVideo, setSelectedVideo] = useState<any>(null)

  // Load influencer data on component mount
  useEffect(() => {
    const loadInfluencerData = async () => {
      if (!talentId) {
        setLoadingState({
          isLoading: false,
          error: '无效的达人ID',
          data: null
        })
        return
      }

      try {
        setLoadingState(prev => ({ ...prev, isLoading: true, error: null }))
        
        const response: ApiResponse = await dataService.getInfluencerById(talentId)
        
        if (response.success && response.data) {
          setLoadingState({
            isLoading: false,
            error: null,
            data: response.data
          })
        } else {
          setLoadingState({
            isLoading: false,
            error: response.message || '数据加载失败',
            data: null
          })
        }
      } catch (error) {
        console.error('Error loading influencer data:', error)
        setLoadingState({
          isLoading: false,
          error: '网络错误，请稍后重试',
          data: null
        })
      }
    }

    loadInfluencerData()
  }, [talentId])

  // Loading state
  if (loadingState.isLoading) {
    return (
      <div className="min-h-screen bg-[#FDFCFA] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-[#CC5500] mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-[#1A1A1A] mb-2">加载中...</h2>
          <p className="text-[#6B6967]">正在获取达人 {talentId} 的数据</p>
        </div>
      </div>
    )
  }

  // Error state
  if (loadingState.error || !loadingState.data) {
    return (
      <div className="min-h-screen bg-[#FDFCFA] flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <Alert className="border-[#DC2626] bg-[#FEF2F2]">
            <AlertTriangle className="h-4 w-4 text-[#DC2626]" />
            <AlertDescription className="text-[#DC2626]">
              {loadingState.error}
            </AlertDescription>
          </Alert>
          
          <div className="mt-6 text-center">
            <Button 
              onClick={() => router.push('/')}
              className="bg-[#CC5500] hover:bg-[#B84C00] text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </div>
          
          <div className="mt-4 text-center text-sm text-[#6B6967]">
            <p>可用的达人ID:</p>
            <div className="mt-2 flex flex-wrap gap-2 justify-center">
              {dataService.getAvailableInfluencers().map(id => (
                <Badge 
                  key={id} 
                  className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0] cursor-pointer"
                  onClick={() => router.push(`/${id}`)}
                >
                  {id}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Extract data for easier access
  const influencerData = loadingState.data

  // Parse industry distribution data
  const parseIndustryData = (distributionStr: string) => {
    try {
      return JSON.parse(distributionStr)
    } catch {
      return []
    }
  }

  // Format numbers
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return Math.round(num / 10000 * 10) / 10 + "万"
    }
    return num.toString()
  }

  // Format date
  const formatDate = (timestamp: string) => {
    return new Date(parseInt(timestamp) * 1000).toLocaleDateString('zh-CN')
  }

  // Process data for display
  const kolData = {
    // Basic info
    name: influencerData.nick_name,
    avatar: influencerData.avatar_uri,
    followers: influencerData.computed?.followerDisplay || formatNumber(influencerData.follower),
    location: influencerData.city,
    mcn: influencerData.mcn_name,
    selfIntro: influencerData.self_intro,

    // Core metrics
    metrics: {
      avgViews: Math.round(parseInt(influencerData.vv || '0') / 10000) + "万",
      avgA3IncrCnt: influencerData.avg_a3_incr_cnt,
      avgA3IncrCntRank: Math.round(parseFloat(influencerData.avg_a3_incr_cnt_rank_percent || '0') * 100) + "%",
      platformHotRate: influencerData.platform_hot_rate,
      messageReplyRate: Math.round(parseFloat(influencerData.message_reply_rate || '0') * 100) + "%",
      fansGrowthRate30d: Math.round(parseFloat(influencerData.fans_growth_rate_30d || '0') * 100 * 10) / 10 + "%",

      // CPM data
      cpm1_20: "¥" + (influencerData.cpm_1_20 || '0'),
      cpm20_60: "¥" + (influencerData.cpm_20_60 || '0'),
      cpm60: "¥" + (influencerData.cpm_60 || '0'),
      cpm20_60_rank: Math.round((1 - parseFloat(influencerData.cpm_20_60_rank_percent || '0')) * 100) + "%",

      // CPE data
      cpe1_20: influencerData.cpe_1_20 || '0',
      cpe20_60: influencerData.cpe_20_60 || '0',
      cpe60: influencerData.cpe_60 || '0',

      // Order data
      orderCnt: influencerData.order_cnt || '0',
      processOrderCnt: influencerData.process_order_cnt || '0',
      hasOrder30d: influencerData.has_order_30d || false,
      hasOrder90d: influencerData.has_order_90d || false,

      // Calculate average metrics
      avgLikes: influencerData.items ? 
        Math.round(influencerData.items.reduce((sum: number, item: any) => sum + parseInt(item.like_cnt || '0'), 0) / influencerData.items.length / 10000) + "万" : "0",
      avgComments: influencerData.items ? 
        Math.round(influencerData.items.reduce((sum: number, item: any) => sum + parseInt(item.comment_cnt || '0'), 0) / influencerData.items.length) : 0,
      interactionRate: influencerData.items ? 
        Math.round(influencerData.items.reduce((sum: number, item: any) => sum + (item.interact_rate || 0), 0) / influencerData.items.length * 100 * 100) / 100 + "%" : "0%",
    },

    // Comprehensive score
    score: influencerData.computed?.scoreDisplay || calculateScore(influencerData),

    // Content themes
    contentThemes: influencerData.content_theme_label_map ? 
      Object.entries(influencerData.content_theme_label_map).map(([name, data]: [string, any]) => ({
        name,
        count: parseInt(data.label_item_count || '0'),
        percentage: Math.round((parseInt(data.label_item_count || '0') / parseInt(influencerData.all_content_theme_label_item_cnt || '1')) * 100)
      })).sort((a, b) => b.count - a.count) : [],

    // Industry experience
    industryExperience: {
      orderDistribution: parseIndustryData(influencerData.industry_order_distribution || '[]'),
      vvDistribution: parseIndustryData(influencerData.industry_vv_distribution || '[]'),
      experienceIndustries: influencerData.order_experience_industries || []
    },

    // Latest videos
    latestVideos: influencerData.items ? influencerData.items.slice(0, 6).map((item: any) => ({
      id: item.item_id,
      title: item.title,
      coverUri: item.cover_uri,
      watchCnt: Math.round(parseInt(item.watch_cnt || '0') / 10000) + "万",
      likeCnt: Math.round(parseInt(item.like_cnt || '0') / 10000) + "万",
      commentCnt: item.comment_cnt,
      duration: Math.round(item.duration || 0),
      interactRate: Math.round((item.interact_rate || 0) * 100 * 100) / 100 + "%",
      createTime: new Date(parseInt(item.create_time || '0') * 1000).toLocaleDateString(),
      url: item.url,
      embedUrl: `https://www.douyin.com/video/${item.item_id}`,
      contentThemeLabels: item.content_theme_labels || []
    })) : [],

    // Pricing
    pricing: influencerData.price_info ? influencerData.price_info.filter((price: any) => price.enable).map((price: any) => ({
      type: price.desc,
      price: "¥" + Math.round((price.price || 0) / 10000) + "万",
      originalPrice: price.origin_price,
      settlementType: price.settlement_type,
      settlementDesc: price.settlement_desc,
      videoType: price.video_type,
      isOpen: price.is_open,
      doFold: price.do_fold,
      priceExtraInfo: price.price_extra_info
    })) : [],

    // Hot list ranks
    hotListRanks: influencerData.hot_list_ranks || [],

    // Industry tags
    industryTags: influencerData.industry_tags || [],

    // Tags
    tags: influencerData.tags ? JSON.parse(influencerData.tags) : [],
    tagsLevelTwo: influencerData.tags_level_two ? JSON.parse(influencerData.tags_level_two) : [],
  }

  return (
    <div className="min-h-screen bg-[#FDFCFA]">
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {/* Header with back button */}
        <div className="mb-6">
          <Button 
            onClick={() => router.push('/')}
            variant="outline"
            className="border-[#F0EFEB] hover:bg-[#F8F7F4]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回首页
          </Button>
        </div>

        {/* Profile header */}
        <div className="bg-white rounded-2xl shadow-sm border border-[#F0EFEB] p-8 mb-8">
          <div className="flex items-start gap-8">
            <div className="relative">
              <img
                src={kolData.avatar || "/placeholder.svg"}
                alt={kolData.name}
                className="w-32 h-32 rounded-2xl object-cover border-4 border-white shadow-md"
              />
              <div className="absolute -bottom-2 -right-2 bg-[#059669] text-white text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                活跃
              </div>
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-4 mb-3">
                <h1 className="text-3xl font-normal text-[#1A1A1A]">{kolData.name}</h1>
                <Badge className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0]">
                  ID: {talentId}
                </Badge>
                {kolData.tags.length > 0 && (
                  <Badge className="bg-[#CC5500] text-white hover:bg-[#B84C00]">{kolData.tags[0]}</Badge>
                )}
                <Badge className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0]">MCN: {kolData.mcn}</Badge>
              </div>

              <div className="flex items-center gap-6 text-[#6B6967] mb-4">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>{kolData.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>{kolData.followers} 粉丝</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-[#D97706]" />
                  <span>综合评分 {kolData.score}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-[#059669]" />
                  <span>回复率 {kolData.metrics.messageReplyRate}</span>
                </div>
              </div>

              {/* Self introduction */}
              <div className="bg-[#F8F7F4] rounded-lg p-4 mb-6">
                <p className="text-sm text-[#6B6967] leading-relaxed">{kolData.selfIntro}</p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">平均播放量</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.avgViews}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">平均点赞数</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.avgLikes}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">互动率</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.interactionRate}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">CPM (20-60s)</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.cpm20_60}</div>
                  <div className="text-xs text-[#059669] mt-1">超越 {kolData.metrics.cpm20_60_rank} 达人</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">30天粉丝增长</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.fansGrowthRate30d}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Rest of the component will be added in the next part due to length limits */}
        <div className="text-center py-8">
          <p className="text-[#6B6967]">详细内容正在加载中...</p>
        </div>
      </div>
    </div>
  )
}
