"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Users, TrendingUp, MapPin, Star, Award, Target,
  PlayCircle, Heart, MessageCircle, Share2,
  Calendar, Clock, DollarSign, TrendingDown,
  CheckCircle, AlertCircle, BarChart3, PieChart,
  Video, Briefcase, Eye, ThumbsUp, ArrowLeft,
  Loader2, AlertTriangle
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { dataService, type InfluencerData, type ApiResponse } from "@/lib/data-service"
import { DataQuality } from "@/components/data-status"
import {
  formatToWan,
  formatPercentage,
  formatTimestamp,
  formatDuration,
  parseJsonSafely,
  getIndustryName,
  calculateAverage,
  formatCPM,
  formatRankPercentage,
  getStatusColor,
  getStatusText,
  formatImageUrl,
  calculateThemePercentage,
  formatPrice,
  calculateScore,
  validateData
} from "@/lib/data-utils"

interface LoadingState {
  isLoading: boolean
  error: string | null
  data: InfluencerData | null
}

export default function TalentProfilePage() {
  const params = useParams()
  const router = useRouter()
  const talentId = params.talentId as string

  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: true,
    error: null,
    data: null
  })
  
  const [activeTab, setActiveTab] = useState("overview")
  const [selectedVideo, setSelectedVideo] = useState<any>(null)

  // Load influencer data on component mount
  useEffect(() => {
    const loadInfluencerData = async () => {
      if (!talentId) {
        setLoadingState({
          isLoading: false,
          error: '无效的达人ID',
          data: null
        })
        return
      }

      try {
        setLoadingState(prev => ({ ...prev, isLoading: true, error: null }))
        
        const response: ApiResponse = await dataService.getInfluencerById(talentId)
        
        if (response.success && response.data) {
          setLoadingState({
            isLoading: false,
            error: null,
            data: response.data
          })
        } else {
          setLoadingState({
            isLoading: false,
            error: response.message || '数据加载失败',
            data: null
          })
        }
      } catch (error) {
        console.error('Error loading influencer data:', error)
        setLoadingState({
          isLoading: false,
          error: '网络错误，请稍后重试',
          data: null
        })
      }
    }

    loadInfluencerData()
  }, [talentId])

  // Loading state
  if (loadingState.isLoading) {
    return (
      <div className="min-h-screen bg-[#FDFCFA] flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-[#CC5500] mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-[#1A1A1A] mb-2">加载中...</h2>
          <p className="text-[#6B6967]">正在获取达人 {talentId} 的数据</p>
        </div>
      </div>
    )
  }

  // Error state
  if (loadingState.error || !loadingState.data) {
    return (
      <div className="min-h-screen bg-[#FDFCFA] flex items-center justify-center">
        <div className="max-w-md w-full mx-4">
          <Alert className="border-[#DC2626] bg-[#FEF2F2]">
            <AlertTriangle className="h-4 w-4 text-[#DC2626]" />
            <AlertDescription className="text-[#DC2626]">
              {loadingState.error}
            </AlertDescription>
          </Alert>
          
          <div className="mt-6 text-center">
            <Button 
              onClick={() => router.push('/')}
              className="bg-[#CC5500] hover:bg-[#B84C00] text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              返回首页
            </Button>
          </div>
          
          <div className="mt-4 text-center text-sm text-[#6B6967]">
            <p>可用的达人ID:</p>
            <div className="mt-2 flex flex-wrap gap-2 justify-center">
              {dataService.getAvailableInfluencers().map(id => (
                <Badge 
                  key={id} 
                  className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0] cursor-pointer"
                  onClick={() => router.push(`/${id}`)}
                >
                  {id}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Extract data for easier access
  const influencerData = loadingState.data

  // Parse industry distribution data
  const parseIndustryData = (distributionStr: string) => {
    try {
      return JSON.parse(distributionStr)
    } catch {
      return []
    }
  }

  // Format numbers
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return Math.round(num / 10000 * 10) / 10 + "万"
    }
    return num.toString()
  }

  // Format date
  const formatDate = (timestamp: string) => {
    return new Date(parseInt(timestamp) * 1000).toLocaleDateString('zh-CN')
  }

  // Process data for display
  const kolData = {
    // Basic info
    name: influencerData.nick_name,
    avatar: influencerData.avatar_uri,
    followers: influencerData.computed?.followerDisplay || formatNumber(influencerData.follower),
    location: influencerData.city,
    mcn: influencerData.mcn_name,
    selfIntro: influencerData.self_intro,

    // Core metrics
    metrics: {
      avgViews: Math.round(parseInt(influencerData.vv || '0') / 10000) + "万",
      avgA3IncrCnt: influencerData.avg_a3_incr_cnt,
      avgA3IncrCntRank: Math.round(parseFloat(influencerData.avg_a3_incr_cnt_rank_percent || '0') * 100) + "%",
      platformHotRate: influencerData.platform_hot_rate,
      messageReplyRate: Math.round(parseFloat(influencerData.message_reply_rate || '0') * 100) + "%",
      fansGrowthRate30d: Math.round(parseFloat(influencerData.fans_growth_rate_30d || '0') * 100 * 10) / 10 + "%",

      // CPM data
      cpm1_20: "¥" + (influencerData.cpm_1_20 || '0'),
      cpm20_60: "¥" + (influencerData.cpm_20_60 || '0'),
      cpm60: "¥" + (influencerData.cpm_60 || '0'),
      cpm20_60_rank: Math.round((1 - parseFloat(influencerData.cpm_20_60_rank_percent || '0')) * 100) + "%",

      // CPE data
      cpe1_20: influencerData.cpe_1_20 || '0',
      cpe20_60: influencerData.cpe_20_60 || '0',
      cpe60: influencerData.cpe_60 || '0',

      // Order data
      orderCnt: influencerData.order_cnt || '0',
      processOrderCnt: influencerData.process_order_cnt || '0',
      hasOrder30d: influencerData.has_order_30d || false,
      hasOrder90d: influencerData.has_order_90d || false,

      // Calculate average metrics
      avgLikes: influencerData.items ? 
        Math.round(influencerData.items.reduce((sum: number, item: any) => sum + parseInt(item.like_cnt || '0'), 0) / influencerData.items.length / 10000) + "万" : "0",
      avgComments: influencerData.items ? 
        Math.round(influencerData.items.reduce((sum: number, item: any) => sum + parseInt(item.comment_cnt || '0'), 0) / influencerData.items.length) : 0,
      interactionRate: influencerData.items ? 
        Math.round(influencerData.items.reduce((sum: number, item: any) => sum + (item.interact_rate || 0), 0) / influencerData.items.length * 100 * 100) / 100 + "%" : "0%",
    },

    // Comprehensive score
    score: influencerData.computed?.scoreDisplay || calculateScore(influencerData),

    // Content themes
    contentThemes: influencerData.content_theme_label_map ? 
      Object.entries(influencerData.content_theme_label_map).map(([name, data]: [string, any]) => ({
        name,
        count: parseInt(data.label_item_count || '0'),
        percentage: Math.round((parseInt(data.label_item_count || '0') / parseInt(influencerData.all_content_theme_label_item_cnt || '1')) * 100)
      })).sort((a, b) => b.count - a.count) : [],

    // Industry experience
    industryExperience: {
      orderDistribution: parseIndustryData(influencerData.industry_order_distribution || '[]'),
      vvDistribution: parseIndustryData(influencerData.industry_vv_distribution || '[]'),
      experienceIndustries: influencerData.order_experience_industries || []
    },

    // Latest videos
    latestVideos: influencerData.items ? influencerData.items.slice(0, 6).map((item: any) => ({
      id: item.item_id,
      title: item.title,
      coverUri: item.cover_uri,
      watchCnt: Math.round(parseInt(item.watch_cnt || '0') / 10000) + "万",
      likeCnt: Math.round(parseInt(item.like_cnt || '0') / 10000) + "万",
      commentCnt: item.comment_cnt,
      duration: Math.round(item.duration || 0),
      interactRate: Math.round((item.interact_rate || 0) * 100 * 100) / 100 + "%",
      createTime: new Date(parseInt(item.create_time || '0') * 1000).toLocaleDateString(),
      url: item.url,
      embedUrl: `https://www.douyin.com/video/${item.item_id}`,
      contentThemeLabels: item.content_theme_labels || []
    })) : [],

    // Pricing
    pricing: influencerData.price_info ? influencerData.price_info.filter((price: any) => price.enable).map((price: any) => ({
      type: price.desc,
      price: "¥" + Math.round((price.price || 0) / 10000) + "万",
      originalPrice: price.origin_price,
      settlementType: price.settlement_type,
      settlementDesc: price.settlement_desc,
      videoType: price.video_type,
      isOpen: price.is_open,
      doFold: price.do_fold,
      priceExtraInfo: price.price_extra_info
    })) : [],

    // Hot list ranks
    hotListRanks: influencerData.hot_list_ranks || [],

    // Industry tags
    industryTags: influencerData.industry_tags || [],

    // Tags
    tags: influencerData.tags ? JSON.parse(influencerData.tags) : [],
    tagsLevelTwo: influencerData.tags_level_two ? JSON.parse(influencerData.tags_level_two) : [],
  }

  return (
    <div className="min-h-screen bg-[#FDFCFA]">
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {/* Header with back button */}
        <div className="mb-6">
          <Button 
            onClick={() => router.push('/')}
            variant="outline"
            className="border-[#F0EFEB] hover:bg-[#F8F7F4]"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回首页
          </Button>
        </div>

        {/* Profile header */}
        <div className="bg-white rounded-2xl shadow-sm border border-[#F0EFEB] p-8 mb-8">
          <div className="flex items-start gap-8">
            <div className="relative">
              <img
                src={kolData.avatar || "/placeholder.svg"}
                alt={kolData.name}
                className="w-32 h-32 rounded-2xl object-cover border-4 border-white shadow-md"
              />
              <div className="absolute -bottom-2 -right-2 bg-[#059669] text-white text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                活跃
              </div>
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-4 mb-3">
                <h1 className="text-3xl font-normal text-[#1A1A1A]">{kolData.name}</h1>
                <Badge className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0]">
                  ID: {talentId}
                </Badge>
                {kolData.tags.length > 0 && (
                  <Badge className="bg-[#CC5500] text-white hover:bg-[#B84C00]">{kolData.tags[0]}</Badge>
                )}
                <Badge className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0]">MCN: {kolData.mcn}</Badge>
              </div>

              <div className="flex items-center gap-6 text-[#6B6967] mb-4">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>{kolData.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>{kolData.followers} 粉丝</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-[#D97706]" />
                  <span>综合评分 {kolData.score}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-[#059669]" />
                  <span>回复率 {kolData.metrics.messageReplyRate}</span>
                </div>
              </div>

              {/* Self introduction */}
              <div className="bg-[#F8F7F4] rounded-lg p-4 mb-6">
                <p className="text-sm text-[#6B6967] leading-relaxed">{kolData.selfIntro}</p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">平均播放量</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.avgViews}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">平均点赞数</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.avgLikes}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">互动率</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.interactionRate}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">CPM (20-60s)</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.cpm20_60}</div>
                  <div className="text-xs text-[#059669] mt-1">超越 {kolData.metrics.cpm20_60_rank} 达人</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">30天粉丝增长</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.fansGrowthRate30d}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs content */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 bg-white rounded-xl p-1 shadow-sm border border-[#F0EFEB] mb-8">
            <TabsTrigger
              value="overview"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              数据概览
            </TabsTrigger>
            <TabsTrigger
              value="content"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              内容分析
            </TabsTrigger>
            <TabsTrigger
              value="videos"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              最新作品
            </TabsTrigger>
            <TabsTrigger
              value="industry"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              行业经验
            </TabsTrigger>
            <TabsTrigger
              value="audience"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              粉丝画像
            </TabsTrigger>
            <TabsTrigger
              value="pricing"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              合作报价
            </TabsTrigger>
          </TabsList>

          {/* Data Overview Tab */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-[#CC5500]" />
                    核心数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均播放量</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.avgViews}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均互动率</span>
                    <span className="font-semibold text-[#059669]">{kolData.metrics.interactionRate}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均点赞数</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.avgLikes}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均评论数</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.avgComments}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <DollarSign className="w-5 h-5 text-[#CC5500]" />
                    CPM数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">1-20s CPM</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.cpm1_20}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">20-60s CPM</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.cpm20_60}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">60s+ CPM</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.cpm60}</span>
                  </div>
                  <div className="pt-2">
                    <div className="text-[#6B6967] text-sm mb-2">CPM排名</div>
                    <Progress value={parseInt(kolData.metrics.cpm20_60_rank)} className="h-2" />
                    <div className="text-xs text-[#9B9894] mt-1">超过 {kolData.metrics.cpm20_60_rank} 达人</div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Briefcase className="w-5 h-5 text-[#CC5500]" />
                    商业数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">历史订单</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.orderCnt}单</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">进行中订单</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.processOrderCnt}单</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">消息回复率</span>
                    <span className="font-semibold text-[#059669]">{kolData.metrics.messageReplyRate}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">30天有订单</span>
                    <span className={`font-semibold ${kolData.metrics.hasOrder30d ? 'text-[#059669]' : 'text-[#DC2626]'}`}>
                      {kolData.metrics.hasOrder30d ? '是' : '否'}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Award className="w-5 h-5 text-[#CC5500]" />
                    综合评分
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center py-4">
                    <div className="relative w-24 h-24">
                      <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="35" fill="none" stroke="#F0EFEB" strokeWidth="6" />
                        <circle
                          cx="50"
                          cy="50"
                          r="35"
                          fill="none"
                          stroke="#CC5500"
                          strokeWidth="6"
                          strokeDasharray="219.8"
                          strokeDashoffset={219.8 - (parseFloat(kolData.score) / 10) * 219.8}
                          strokeLinecap="round"
                        />
                      </svg>
                      <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <div className="text-2xl font-bold text-[#1A1A1A]">{kolData.score}</div>
                        <div className="text-xs text-[#6B6967]">优秀</div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-[#6B6967]">粉丝增长</span>
                      <span className="text-[#1A1A1A]">{kolData.metrics.fansGrowthRate30d}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#6B6967]">平台热度</span>
                      <span className="text-[#1A1A1A]">{kolData.metrics.platformHotRate}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Content Analysis Tab */}
          <TabsContent value="content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <PieChart className="w-5 h-5 text-[#CC5500]" />
                    内容主题分布
                  </CardTitle>
                  <CardDescription>共 {influencerData.all_content_theme_label_item_cnt || 0} 个作品</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {kolData.contentThemes.slice(0, 8).map((theme) => (
                      <div key={theme.name}>
                        <div className="flex justify-between text-sm mb-2">
                          <span className="text-[#6B6967]">{theme.name}</span>
                          <span className="font-medium text-[#1A1A1A]">{theme.count}个 ({theme.percentage}%)</span>
                        </div>
                        <Progress value={theme.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Award className="w-5 h-5 text-[#CC5500]" />
                    内容特色与标签
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-[#1A1A1A] mb-3">主要标签</h4>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {kolData.tags.map((tag: string) => (
                          <Badge key={tag} className="bg-[#CC5500] text-white hover:bg-[#B84C00]">
                            {tag}
                          </Badge>
                        ))}
                        {kolData.tagsLevelTwo.map((tag: string) => (
                          <Badge key={tag} className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0]">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="bg-[#F8F7F4] rounded-lg p-4">
                      <h4 className="font-medium text-[#1A1A1A] mb-2">适合行业</h4>
                      <div className="flex flex-wrap gap-2">
                        {kolData.industryTags.map((tag: string) => (
                          <Badge key={tag} className="bg-white text-[#6B6967] border border-[#E5E3DF] text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {kolData.hotListRanks.length > 0 && (
                      <div className="bg-[#F8F7F4] rounded-lg p-4">
                        <h4 className="font-medium text-[#1A1A1A] mb-2">榜单排名</h4>
                        {kolData.hotListRanks.map((rank: any) => (
                          <div key={rank.industry_id} className="text-sm text-[#6B6967] mb-1">
                            <span className="font-medium text-[#CC5500]">{rank.hot_list_name}</span> - {rank.industry_name}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Latest Videos Tab */}
          <TabsContent value="videos">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {kolData.latestVideos.map((video: any) => (
                <Card
                  key={video.id}
                  className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl overflow-hidden cursor-pointer group"
                  onClick={() => setSelectedVideo(video)}
                >
                  <div className="relative">
                    <img
                      src={formatImageUrl(video.coverUri)}
                      alt={video.title}
                      className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                      <PlayCircle className="w-16 h-16 text-white opacity-70 transition-opacity duration-300 group-hover:opacity-90" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {formatDuration(video.duration)}
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-medium text-[#1A1A1A] mb-2 line-clamp-2 text-sm leading-relaxed">
                      {video.title}
                    </h3>
                    <div className="text-xs text-[#6B6967] mb-3">{video.createTime}</div>

                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.watchCnt}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ThumbsUp className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.likeCnt}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.commentCnt}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <BarChart3 className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.interactRate}</span>
                      </div>
                    </div>

                    {video.contentThemeLabels.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-1">
                        {video.contentThemeLabels.slice(0, 2).map((label: string) => (
                          <Badge key={label} className="bg-[#F8F7F4] text-[#6B6967] text-xs px-2 py-0.5">
                            {label}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Pricing Tab */}
          <TabsContent value="pricing">
            <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                  <DollarSign className="w-5 h-5 text-[#CC5500]" />
                  合作报价
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-none">
                      <TableHead className="w-2/5 border-b border-[#F0EFEB] text-[#6B6967] font-normal">合作类型</TableHead>
                      <TableHead className="text-center border-b border-[#F0EFEB] text-[#6B6967] font-normal">价格</TableHead>
                      <TableHead className="text-center border-b border-[#F0EFEB] text-[#6B6967] font-normal">结算方式</TableHead>
                      <TableHead className="text-right border-b border-[#F0EFEB] text-[#6B6967] font-normal">备注</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {kolData.pricing.map((item, index) => (
                      <TableRow key={index} className="border-b-0">
                        <TableCell className="py-3 font-medium text-[#1A1A1A]">{item.type}</TableCell>
                        <TableCell className="py-3 text-center font-semibold text-[#CC5500]">{item.price}</TableCell>
                        <TableCell className="py-3 text-center text-[#6B6967]">{item.settlementDesc}</TableCell>
                        <TableCell className="py-3 text-right text-xs text-[#6B6967]">
                          {item.priceExtraInfo?.floor_price && (
                            <div>保底 ¥{Math.round(parseInt(item.priceExtraInfo.floor_price) / 10000)}万</div>
                          )}
                          {item.priceExtraInfo?.ceiling_price && (
                            <div>封顶 ¥{Math.round(parseInt(item.priceExtraInfo.ceiling_price) / 10000)}万</div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Industry Experience Tab */}
          <TabsContent value="industry">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Briefcase className="w-5 h-5 text-[#CC5500]" />
                    订单行业分布
                  </CardTitle>
                  <CardDescription>共 {kolData.metrics.orderCnt} 个订单</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {kolData.industryExperience.orderDistribution.map((industry: any) => {
                      const industryNames: { [key: string]: string } = {
                        "1907": "3C及电器",
                        "1913": "美妆",
                        "1901": "零售",
                        "1916": "汽车",
                        "1914": "3C数码家电",
                        "1930": "其他"
                      }
                      return (
                        <div key={industry.key}>
                          <div className="flex justify-between text-sm mb-2">
                            <span className="text-[#6B6967]">{industryNames[industry.key] || `行业${industry.key}`}</span>
                            <span className="font-medium text-[#1A1A1A]">{industry.value}单 ({Math.round(parseFloat(industry.rate) * 100)}%)</span>
                          </div>
                          <Progress value={parseFloat(industry.rate) * 100} className="h-2" />
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Eye className="w-5 h-5 text-[#CC5500]" />
                    播放量行业分布
                  </CardTitle>
                  <CardDescription>总播放量分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {kolData.industryExperience.vvDistribution.map((industry: any) => {
                      const industryNames: { [key: string]: string } = {
                        "1907": "3C及电器",
                        "1913": "美妆",
                        "1901": "零售",
                        "1916": "汽车",
                        "1914": "3C数码家电",
                        "1930": "其他"
                      }
                      return (
                        <div key={industry.key}>
                          <div className="flex justify-between text-sm mb-2">
                            <span className="text-[#6B6967]">{industryNames[industry.key] || `行业${industry.key}`}</span>
                            <span className="font-medium text-[#1A1A1A]">{Math.round(parseInt(industry.value) / 10000)}万 ({Math.round(parseFloat(industry.rate) * 100)}%)</span>
                          </div>
                          <Progress value={parseFloat(industry.rate) * 100} className="h-2" />
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Audience Tab */}
          <TabsContent value="audience">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A]">性别分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-[#6B6967]">男性</span>
                      <span className="font-semibold text-[#1A1A1A]">67%</span>
                    </div>
                    <Progress value={67} className="h-3" />
                    <div className="flex items-center justify-between">
                      <span className="text-[#6B6967]">女性</span>
                      <span className="font-semibold text-[#1A1A1A]">33%</span>
                    </div>
                    <Progress value={33} className="h-3" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A]">年龄分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {[
                      { age: "18-23", percent: 49 },
                      { age: "24-30", percent: 28 },
                      { age: "31-40", percent: 16 },
                      { age: "41-50", percent: 3 },
                      { age: "50+", percent: 4 }
                    ].map(({ age, percent }) => (
                      <div key={age}>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-[#6B6967]">{age}岁</span>
                          <span className="font-medium text-[#1A1A1A]">{percent}%</span>
                        </div>
                        <Progress value={percent} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        {/* Video dialog */}
        {selectedVideo && (
          <Dialog open={!!selectedVideo} onOpenChange={() => setSelectedVideo(null)}>
            <DialogContent className="max-w-3xl h-4/5 p-0">
              <iframe
                src={selectedVideo.embedUrl}
                width="100%"
                height="100%"
                allow="autoplay; encrypted-media"
                allowFullScreen
                className="rounded-lg"
              ></iframe>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  )
}
