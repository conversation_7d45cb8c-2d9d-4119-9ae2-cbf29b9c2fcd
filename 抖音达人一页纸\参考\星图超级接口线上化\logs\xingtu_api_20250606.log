{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:08.269688Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:08.269688Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.325129Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.325129Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.325629Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.325629Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.326629Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.326629Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.326629Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.326629Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.327129Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.327129Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.327129Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.327129Z"}
{"status_code": 200, "response_time_ms": 184.52, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.511652Z"}
{"status_code": 200, "response_time_ms": 184.52, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.511652Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.512653Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.512653Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.512653Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.512653Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.513153Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.513153Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.513153Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.513153Z"}
{"status_code": 200, "response_time_ms": 385.82, "response_size_bytes": 2759, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.898977Z"}
{"status_code": 200, "response_time_ms": 385.82, "response_size_bytes": 2759, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.898977Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.899978Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.899978Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.900477Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.900477Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.900477Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.900477Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.900977Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.900977Z"}
{"status_code": 200, "response_time_ms": 81.39, "response_size_bytes": 83, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.982371Z"}
{"status_code": 200, "response_time_ms": 81.39, "response_size_bytes": 83, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.982371Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.983494Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.983494Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.983994Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:11:12.983994Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.983994Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:11:12.983994Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "107955119950"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.983994Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "107955119950"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:12.983994Z"}
{"status_code": 200, "response_time_ms": 71.02, "response_size_bytes": 143, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:13.055018Z"}
{"status_code": 200, "response_time_ms": 71.02, "response_size_bytes": 143, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:11:13.055018Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:40.325831Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:40.325831Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.607451Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.607451Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:44.607951Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:44.607951Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.608451Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.608451Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.608451Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.608451Z"}
{"author_id": "6651749112423120908", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:44.608451Z"}
{"author_id": "6651749112423120908", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:44.608451Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.608951Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.608951Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.609451Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:44.609451Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:44.609451Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:44.609451Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:44.609451Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:44.609451Z"}
{"status_code": 200, "response_time_ms": 3009.0, "response_size_bytes": 2759, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.618451Z"}
{"status_code": 200, "response_time_ms": 3009.0, "response_size_bytes": 2759, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.618451Z"}
{"author_id": "6651749112423120908", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.618955Z"}
{"author_id": "6651749112423120908", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.618955Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.619455Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.619455Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.619455Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.619455Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.619455Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.619455Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.619955Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.619955Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.620455Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:47.620455Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:47.620455Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:47.620455Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.620955Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.620955Z"}
{"status_code": 200, "response_time_ms": 378.62, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.999572Z"}
{"status_code": 200, "response_time_ms": 378.62, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:47.999572Z"}
{"author_id": "7119367979820646432", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:48.000072Z"}
{"author_id": "7119367979820646432", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:48.000072Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:48.003072Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:48.003072Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:51.908210Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:51.908210Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:51.908710Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:51.908710Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:51.910211Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:51.910211Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:51.910710Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:51.910710Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:51.911210Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:51.911210Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:51.911710Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:51.911710Z"}
{"status_code": 200, "response_time_ms": 274.64, "response_size_bytes": 180, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:52.186353Z"}
{"status_code": 200, "response_time_ms": 274.64, "response_size_bytes": 180, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:52.186353Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:52.187356Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:52.187356Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:52.187857Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:52.187857Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:52.188356Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:15:52.188356Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:52.188856Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:52.188856Z"}
{"status_code": 200, "response_time_ms": 85.54, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:52.274399Z"}
{"status_code": 200, "response_time_ms": 85.54, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:15:52.274399Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:59.788902Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:15:59.788902Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.692141Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.692141Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:03.692642Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:03.692642Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.693141Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.693141Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.693141Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.693141Z"}
{"author_id": "6651749112423120908", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:03.693141Z"}
{"author_id": "6651749112423120908", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:03.693141Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.693642Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.693642Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.694142Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:03.694142Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:03.694142Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:03.694142Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:03.694642Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:03.694642Z"}
{"status_code": 200, "response_time_ms": 575.36, "response_size_bytes": 2759, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.270005Z"}
{"status_code": 200, "response_time_ms": 575.36, "response_size_bytes": 2759, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.270005Z"}
{"author_id": "6651749112423120908", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.270005Z"}
{"author_id": "6651749112423120908", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.270005Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.270505Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.270505Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.271004Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.271004Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.271004Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.271004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.271004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.271004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.271505Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.271505Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:04.271505Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:04.271505Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.272004Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.272004Z"}
{"status_code": 200, "response_time_ms": 565.52, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.837524Z"}
{"status_code": 200, "response_time_ms": 565.52, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.837524Z"}
{"author_id": "7119367979820646432", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.838025Z"}
{"author_id": "7119367979820646432", "successful": 1, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:04.838025Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.840023Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:04.840023Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:08.905284Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 946 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:08.905284Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:08.905784Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:08.905784Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:08.906284Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:08.906284Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:08.906783Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:08.906783Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:08.906783Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:08.906783Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:08.907284Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "6651749112423120908"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:08.907284Z"}
{"status_code": 200, "response_time_ms": 271.72, "response_size_bytes": 180, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:09.178999Z"}
{"status_code": 200, "response_time_ms": 271.72, "response_size_bytes": 180, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:09.178999Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:09.180004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:09.180004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:09.180507Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:16:09.180507Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:09.180507Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:16:09.180507Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:09.181008Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:09.181008Z"}
{"status_code": 200, "response_time_ms": 141.09, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:09.322100Z"}
{"status_code": 200, "response_time_ms": 141.09, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:16:09.322100Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:14.346809Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:14.346809Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 944 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.701697Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 944 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.701697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.702199Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.702199Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.702697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.702697Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.702697Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.702697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.703197Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.703197Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.703697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.703697Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.703697Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.703697Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.707197Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.707197Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.707697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.707697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.707697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.707697Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.708198Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.708198Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.709698Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.709698Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.710197Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.710197Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.710197Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.710197Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.710197Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.710197Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.710697Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.710697Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.710697Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.710697Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.711196Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.711196Z"}
{"status_code": 200, "response_time_ms": 218.2, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.929399Z"}
{"status_code": 200, "response_time_ms": 218.2, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.929399Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.929899Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.929899Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.930403Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:18.930403Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.930403Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:18.930403Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.930403Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:18.930403Z"}
{"status_code": 200, "response_time_ms": 83.58, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.013978Z"}
{"status_code": 200, "response_time_ms": 83.58, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.013978Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.014478Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.014478Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.014977Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.014977Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.014977Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.014977Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.015477Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.015477Z"}
{"status_code": 200, "response_time_ms": 172.59, "response_size_bytes": 892, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.188067Z"}
{"status_code": 200, "response_time_ms": 172.59, "response_size_bytes": 892, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.188067Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.188567Z"}
{"status_code": 200, "response_time_ms": 88.04, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.276609Z"}
{"status_code": 200, "response_time_ms": 88.04, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.276609Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.277113Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.277113Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.277613Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.277613Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.277613Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.277613Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.277613Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.277613Z"}
{"status_code": 200, "response_time_ms": 82.59, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.360207Z"}
{"status_code": 200, "response_time_ms": 82.59, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.360207Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.360706Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.360706Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.361208Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.361208Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.361208Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.361208Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.361706Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.361706Z"}
{"status_code": 200, "response_time_ms": 613.08, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.974787Z"}
{"status_code": 200, "response_time_ms": 613.08, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.974787Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.975286Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.975286Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.975786Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:19.975786Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.976287Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:19.976287Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.976287Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:19.976287Z"}
{"status_code": 200, "response_time_ms": 97.07, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.073358Z"}
{"status_code": 200, "response_time_ms": 97.07, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.073358Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.073858Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.073858Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.074358Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.074358Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.074859Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.074859Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.074859Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.074859Z"}
{"status_code": 200, "response_time_ms": 85.01, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.159872Z"}
{"status_code": 200, "response_time_ms": 85.01, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.159872Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.160373Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.160373Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.160873Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.160873Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.160873Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.160873Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.161372Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.161372Z"}
{"status_code": 200, "response_time_ms": 95.62, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.256995Z"}
{"status_code": 200, "response_time_ms": 95.62, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.256995Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.257495Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.257495Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.257995Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.257995Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.257995Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.257995Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.258495Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.258495Z"}
{"status_code": 200, "response_time_ms": 80.52, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.339012Z"}
{"status_code": 200, "response_time_ms": 80.52, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.339012Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.339513Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.339513Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.339513Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.339513Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.340012Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.340012Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.340012Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.340012Z"}
{"status_code": 200, "response_time_ms": 463.2, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.803207Z"}
{"status_code": 200, "response_time_ms": 463.2, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.803207Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.803708Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.803708Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.804209Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:20.804209Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.804708Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:20.804708Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.804708Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:20.804708Z"}
{"status_code": 200, "response_time_ms": 2672.54, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.477252Z"}
{"status_code": 200, "response_time_ms": 2672.54, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.477252Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.477905Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.477905Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.478905Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.478905Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:23.478905Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:23.478905Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.479404Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.479404Z"}
{"status_code": 200, "response_time_ms": 121.86, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.601261Z"}
{"status_code": 200, "response_time_ms": 121.86, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.601261Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.602261Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.602261Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.602762Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.602762Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:23.603261Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:23.603261Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.603761Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.603761Z"}
{"status_code": 200, "response_time_ms": 340.6, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.944363Z"}
{"status_code": 200, "response_time_ms": 340.6, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.944363Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.945935Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.945935Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.946435Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:23.946435Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:23.946435Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:23.946435Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.947436Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:23.947436Z"}
{"status_code": 200, "response_time_ms": 74.53, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.021969Z"}
{"status_code": 200, "response_time_ms": 74.53, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.021969Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.022969Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.022969Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.023469Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.023469Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.023969Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.023969Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.024470Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.024470Z"}
{"status_code": 200, "response_time_ms": 109.52, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.133988Z"}
{"status_code": 200, "response_time_ms": 109.52, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.133988Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.134986Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.134986Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.135490Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.135490Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.135994Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.135994Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.136493Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.136493Z"}
{"status_code": 200, "response_time_ms": 99.51, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.236002Z"}
{"status_code": 200, "response_time_ms": 99.51, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.236002Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.237001Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.237001Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.237501Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.237501Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.238001Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.238001Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.238503Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.238503Z"}
{"status_code": 200, "response_time_ms": 110.53, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.349029Z"}
{"status_code": 200, "response_time_ms": 110.53, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.349029Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.349529Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.349529Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.350029Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.350029Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.350529Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.350529Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.351028Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.351028Z"}
{"status_code": 200, "response_time_ms": 121.51, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.472538Z"}
{"status_code": 200, "response_time_ms": 121.51, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.472538Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.473538Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.473538Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.474038Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.474038Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.474038Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.474038Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.474538Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.474538Z"}
{"status_code": 200, "response_time_ms": 148.11, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.622652Z"}
{"status_code": 200, "response_time_ms": 148.11, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.622652Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.623152Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.623152Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.623651Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.623651Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.624152Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.624152Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.624652Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.624652Z"}
{"status_code": 200, "response_time_ms": 67.5, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.692154Z"}
{"status_code": 200, "response_time_ms": 67.5, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.692154Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.692655Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.692655Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.693655Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.693655Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.693655Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.693655Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.694155Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.694155Z"}
{"status_code": 200, "response_time_ms": 68.63, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.762784Z"}
{"status_code": 200, "response_time_ms": 68.63, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.762784Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.763784Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.763784Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.764284Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.764284Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.764284Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.764284Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.764784Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.764784Z"}
{"status_code": 200, "response_time_ms": 180.88, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.945667Z"}
{"status_code": 200, "response_time_ms": 180.88, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.945667Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.946167Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.946167Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.947167Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:24.947167Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.947167Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:24.947167Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.947667Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:24.947667Z"}
{"status_code": 200, "response_time_ms": 231.07, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.178732Z"}
{"status_code": 200, "response_time_ms": 231.07, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.178732Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.179232Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.179232Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.179732Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.179732Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.180233Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.180233Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.180732Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.180732Z"}
{"status_code": 200, "response_time_ms": 97.53, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.278258Z"}
{"status_code": 200, "response_time_ms": 97.53, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.278258Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.279258Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.279258Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.279757Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.279757Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.280257Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.280257Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.280758Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.280758Z"}
{"status_code": 200, "response_time_ms": 107.05, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.387809Z"}
{"status_code": 200, "response_time_ms": 107.05, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.387809Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.388309Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.388309Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.389309Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.389309Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.389309Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.389309Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.389809Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.389809Z"}
{"status_code": 200, "response_time_ms": 106.53, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.496335Z"}
{"status_code": 200, "response_time_ms": 106.53, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.496335Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.496835Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.496835Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.497836Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.497836Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.497836Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.497836Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.498336Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.498336Z"}
{"status_code": 200, "response_time_ms": 119.26, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.617598Z"}
{"status_code": 200, "response_time_ms": 119.26, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.617598Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.618598Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.618598Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.619098Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:33:25.619098Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.619598Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:33:25.619598Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.620098Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.620098Z"}
{"status_code": 200, "response_time_ms": 289.41, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.909511Z"}
{"status_code": 200, "response_time_ms": 289.41, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.909511Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.910011Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:33:25.910011Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:02.502191Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:02.502191Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 947 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.908749Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 947 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.908749Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.909750Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.909750Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.909750Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.909750Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:06.910250Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:06.914824Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:06.914824Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.915323Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.915323Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.915823Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.915823Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:06.915823Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:06.915823Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.917825Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.917825Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.917825Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.917825Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:06.918323Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:06.918323Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.918323Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.918323Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.919327Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:06.919327Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:06.919327Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:06.919327Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:06.919831Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:06.919831Z"}
{"status_code": 200, "response_time_ms": 189.56, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.109386Z"}
{"status_code": 200, "response_time_ms": 189.56, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.109386Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.109886Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.109886Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.110386Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.110386Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.110386Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.110386Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.110386Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.110386Z"}
{"status_code": 200, "response_time_ms": 86.05, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.196434Z"}
{"status_code": 200, "response_time_ms": 86.05, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.196434Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.196934Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.196934Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.196934Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.196934Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.197433Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.197433Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.197933Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.197933Z"}
{"status_code": 200, "response_time_ms": 192.05, "response_size_bytes": 915, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.389978Z"}
{"status_code": 200, "response_time_ms": 192.05, "response_size_bytes": 915, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.389978Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.390478Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.390478Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.390977Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.390977Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.390977Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.390977Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.391477Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.391477Z"}
{"status_code": 200, "response_time_ms": 154.51, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.545990Z"}
{"status_code": 200, "response_time_ms": 154.51, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.545990Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.546491Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.546491Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.546990Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.546990Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.546990Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.546990Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.547491Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.547491Z"}
{"status_code": 200, "response_time_ms": 78.03, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.625519Z"}
{"status_code": 200, "response_time_ms": 78.03, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.625519Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.626020Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.626020Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.626519Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:07.626519Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.626519Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:07.626519Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.627020Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:07.627020Z"}
{"status_code": 200, "response_time_ms": 623.74, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.250758Z"}
{"status_code": 200, "response_time_ms": 623.74, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.250758Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.251318Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.251318Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.251817Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.251817Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.251817Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.251817Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.252320Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.252320Z"}
{"status_code": 200, "response_time_ms": 95.38, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.347705Z"}
{"status_code": 200, "response_time_ms": 95.38, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.347705Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.348338Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.348338Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.348838Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.348838Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.348838Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.348838Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.349339Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.349339Z"}
{"status_code": 200, "response_time_ms": 81.84, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.431184Z"}
{"status_code": 200, "response_time_ms": 81.84, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.431184Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.431847Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.431847Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.432346Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.432346Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.432346Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.432346Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.432846Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.432846Z"}
{"status_code": 200, "response_time_ms": 120.43, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.553281Z"}
{"status_code": 200, "response_time_ms": 120.43, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.553281Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.553882Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.553882Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.554381Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.554381Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.554881Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.554881Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.555382Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.555382Z"}
{"status_code": 200, "response_time_ms": 75.82, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.631200Z"}
{"status_code": 200, "response_time_ms": 75.82, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.631200Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.631701Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.631701Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.631701Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:08.631701Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.632200Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:08.632200Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.632200Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:08.632200Z"}
{"status_code": 200, "response_time_ms": 468.59, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:09.100793Z"}
{"status_code": 200, "response_time_ms": 468.59, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:09.100793Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:09.101293Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:09.101293Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:09.101293Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:09.101293Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:09.101794Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:09.101794Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:09.101794Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:09.101794Z"}
{"status_code": 200, "response_time_ms": 2340.04, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.441835Z"}
{"status_code": 200, "response_time_ms": 2340.04, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.441835Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.442835Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.442835Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.443335Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.443335Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:11.443335Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:11.443335Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.444335Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.444335Z"}
{"status_code": 200, "response_time_ms": 127.15, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.571486Z"}
{"status_code": 200, "response_time_ms": 127.15, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.571486Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.572486Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.572486Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.572986Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.572986Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:11.573486Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:11.573486Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.573987Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.573987Z"}
{"status_code": 200, "response_time_ms": 356.65, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.930640Z"}
{"status_code": 200, "response_time_ms": 356.65, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.930640Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.931641Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.931641Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.932141Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:11.932141Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:11.932641Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:11.932641Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.933140Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:11.933140Z"}
{"status_code": 200, "response_time_ms": 112.65, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.045793Z"}
{"status_code": 200, "response_time_ms": 112.65, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.045793Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.046793Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.046793Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.047294Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.047294Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.047794Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.047794Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.048294Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.048294Z"}
{"status_code": 200, "response_time_ms": 104.44, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.152734Z"}
{"status_code": 200, "response_time_ms": 104.44, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.152734Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.153850Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.153850Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.154850Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.154850Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.154850Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.154850Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.156351Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.156351Z"}
{"status_code": 200, "response_time_ms": 98.06, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.254412Z"}
{"status_code": 200, "response_time_ms": 98.06, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.254412Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.255414Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.255414Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.255914Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.255914Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.256414Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.256414Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.256914Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.256914Z"}
{"status_code": 200, "response_time_ms": 116.6, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.373513Z"}
{"status_code": 200, "response_time_ms": 116.6, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.373513Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.374512Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.374512Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.375012Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.375012Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.375512Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.375512Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.376012Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.376012Z"}
{"status_code": 200, "response_time_ms": 136.19, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.512202Z"}
{"status_code": 200, "response_time_ms": 136.19, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.512202Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.512702Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.512702Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.513702Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.513702Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.513702Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.513702Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.514203Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.514203Z"}
{"status_code": 200, "response_time_ms": 155.54, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.669740Z"}
{"status_code": 200, "response_time_ms": 155.54, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.669740Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.670741Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.670741Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.671241Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.671241Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.671741Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.671741Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.672241Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.672241Z"}
{"status_code": 200, "response_time_ms": 141.54, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.813786Z"}
{"status_code": 200, "response_time_ms": 141.54, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.813786Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.814288Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.814288Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.814788Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.814788Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.815288Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.815288Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.815788Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.815788Z"}
{"status_code": 200, "response_time_ms": 74.04, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.890325Z"}
{"status_code": 200, "response_time_ms": 74.04, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.890325Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.890824Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.890824Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.891325Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:12.891325Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.891824Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:12.891824Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.892324Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:12.892324Z"}
{"status_code": 200, "response_time_ms": 252.89, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.145218Z"}
{"status_code": 200, "response_time_ms": 252.89, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.145218Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.146217Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.146217Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.146718Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.146718Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.146718Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.146718Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.147718Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.147718Z"}
{"status_code": 200, "response_time_ms": 289.9, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.437623Z"}
{"status_code": 200, "response_time_ms": 289.9, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.437623Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.438812Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.438812Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.439312Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.439312Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.439812Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.439812Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.440312Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.440312Z"}
{"status_code": 200, "response_time_ms": 92.33, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.533265Z"}
{"status_code": 200, "response_time_ms": 92.33, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.533265Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.533769Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.533769Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.534266Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.534266Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.534766Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.534766Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.535265Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.535265Z"}
{"status_code": 200, "response_time_ms": 119.78, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.655040Z"}
{"status_code": 200, "response_time_ms": 119.78, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.655040Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.655729Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.655729Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.656732Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.656732Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.656732Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.656732Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.657735Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.657735Z"}
{"status_code": 200, "response_time_ms": 112.06, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.769288Z"}
{"status_code": 200, "response_time_ms": 112.06, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.769288Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.769788Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.769788Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.770777Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.770777Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.770777Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.770777Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.771279Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.771279Z"}
{"status_code": 200, "response_time_ms": 104.31, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.875590Z"}
{"status_code": 200, "response_time_ms": 104.31, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.875590Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.876090Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.876090Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.877090Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:49:13.877090Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.877090Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:49:13.877090Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.877590Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:13.877590Z"}
{"status_code": 200, "response_time_ms": 331.61, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:14.209204Z"}
{"status_code": 200, "response_time_ms": 331.61, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:14.209204Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:14.209703Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:49:14.209703Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:51.033401Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:51.033401Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 952 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300419Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 952 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300419Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300920Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300920Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300920Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300920Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300920Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.300920Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.301418Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.301418Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.301918Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.301918Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:50:55.301918Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:50:55.301918Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:50:55.302418Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:50:55.302418Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.303443Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.303443Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.303443Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:50:55.303443Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:50:55.303443Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:50:55.303443Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:00.095906Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:00.095906Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 952 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.228770Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 952 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.228770Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.229271Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.229271Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.229770Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.229770Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.229770Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.229770Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.230270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.230270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.230770Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.230770Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.230770Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.230770Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.231771Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.231771Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.232270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.232270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.232270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.232270Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.232270Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.232270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.234270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.234270Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.234270Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.234270Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.234770Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.234770Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.235270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.235270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.235770Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.235770Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.235770Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.235770Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.236270Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.236270Z"}
{"status_code": 200, "response_time_ms": 163.41, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.399684Z"}
{"status_code": 200, "response_time_ms": 163.41, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.399684Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.400369Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.400369Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.400868Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.400868Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.400868Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.400868Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.400868Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.400868Z"}
{"status_code": 200, "response_time_ms": 82.34, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.483210Z"}
{"status_code": 200, "response_time_ms": 82.34, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.483210Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.483893Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.483893Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.484393Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.484393Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.484393Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.484393Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.484893Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.484893Z"}
{"status_code": 200, "response_time_ms": 77.01, "response_size_bytes": 892, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.561907Z"}
{"status_code": 200, "response_time_ms": 77.01, "response_size_bytes": 892, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.561907Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.562409Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.562409Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.562912Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.562912Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.562912Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.562912Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.563411Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.563411Z"}
{"status_code": 200, "response_time_ms": 70.52, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.633932Z"}
{"status_code": 200, "response_time_ms": 70.52, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.633932Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.634431Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.634431Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.634431Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.634431Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.634931Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.634931Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.634931Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.634931Z"}
{"status_code": 200, "response_time_ms": 76.01, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.710939Z"}
{"status_code": 200, "response_time_ms": 76.01, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.710939Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.711439Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.711439Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.711439Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:04.711439Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.711939Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:04.711939Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.712439Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:04.712439Z"}
{"status_code": 200, "response_time_ms": 682.1, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.394534Z"}
{"status_code": 200, "response_time_ms": 682.1, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.394534Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.395629Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.395629Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.395629Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.395629Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.396129Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.396129Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.396629Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.396629Z"}
{"status_code": 200, "response_time_ms": 100.55, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.497181Z"}
{"status_code": 200, "response_time_ms": 100.55, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.497181Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.497682Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.497682Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.498182Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.498182Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.498182Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.498182Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.498682Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.498682Z"}
{"status_code": 200, "response_time_ms": 73.9, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.572584Z"}
{"status_code": 200, "response_time_ms": 73.9, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.572584Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.573085Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.573085Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.573085Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.573085Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.573584Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.573584Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.573584Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.573584Z"}
{"status_code": 200, "response_time_ms": 84.02, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.657602Z"}
{"status_code": 200, "response_time_ms": 84.02, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.657602Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.658102Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.658102Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.658602Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.658602Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.658602Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.658602Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.659102Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.659102Z"}
{"status_code": 200, "response_time_ms": 68.34, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.727442Z"}
{"status_code": 200, "response_time_ms": 68.34, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.727442Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.728126Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.728126Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.728626Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:05.728626Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.728626Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:05.728626Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.729126Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:05.729126Z"}
{"status_code": 200, "response_time_ms": 435.18, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:06.164305Z"}
{"status_code": 200, "response_time_ms": 435.18, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:06.164305Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:06.164805Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:06.164805Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:06.165305Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:06.165305Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:06.165305Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:06.165305Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:06.165305Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:06.165305Z"}
{"status_code": 200, "response_time_ms": 1964.25, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.129551Z"}
{"status_code": 200, "response_time_ms": 1964.25, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.129551Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.130095Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.130095Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.130595Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.130595Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.131095Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.131095Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.131095Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.131095Z"}
{"status_code": 200, "response_time_ms": 117.51, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.248605Z"}
{"status_code": 200, "response_time_ms": 117.51, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.248605Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.249145Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.249145Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.249645Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.249645Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.249645Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.249645Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.250144Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.250144Z"}
{"status_code": 200, "response_time_ms": 229.03, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.479178Z"}
{"status_code": 200, "response_time_ms": 229.03, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.479178Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.479679Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.479679Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.480178Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.480178Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.480178Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.480178Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.480678Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.480678Z"}
{"status_code": 200, "response_time_ms": 72.0, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.552679Z"}
{"status_code": 200, "response_time_ms": 72.0, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.552679Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.553181Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.553181Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.553684Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.553684Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.553684Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.553684Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.554184Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.554184Z"}
{"status_code": 200, "response_time_ms": 95.03, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.649217Z"}
{"status_code": 200, "response_time_ms": 95.03, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.649217Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.649720Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.649720Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.650220Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.650220Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.650220Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.650220Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.650719Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.650719Z"}
{"status_code": 200, "response_time_ms": 87.51, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.738226Z"}
{"status_code": 200, "response_time_ms": 87.51, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.738226Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.738725Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.738725Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.738725Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.738725Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.739226Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.739226Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.739226Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.739226Z"}
{"status_code": 200, "response_time_ms": 104.01, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.843238Z"}
{"status_code": 200, "response_time_ms": 104.01, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.843238Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.843238Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.843238Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.844730Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.844730Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.844730Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.844730Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.845234Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.845234Z"}
{"status_code": 200, "response_time_ms": 122.03, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.967260Z"}
{"status_code": 200, "response_time_ms": 122.03, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.967260Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.967761Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.967761Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.967761Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:08.967761Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.967761Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:08.967761Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.968260Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:08.968260Z"}
{"status_code": 200, "response_time_ms": 138.04, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.106295Z"}
{"status_code": 200, "response_time_ms": 138.04, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.106295Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.106796Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.106796Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.106796Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.106796Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.106796Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.106796Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.107295Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.107295Z"}
{"status_code": 200, "response_time_ms": 61.52, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.168813Z"}
{"status_code": 200, "response_time_ms": 61.52, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.168813Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.169313Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.169313Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.169813Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.169813Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.169813Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.169813Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.169813Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.169813Z"}
{"status_code": 200, "response_time_ms": 67.86, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.237673Z"}
{"status_code": 200, "response_time_ms": 67.86, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.237673Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.238320Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.238320Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.238820Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.238820Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.238820Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.238820Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.238820Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.238820Z"}
{"status_code": 200, "response_time_ms": 212.42, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.451244Z"}
{"status_code": 200, "response_time_ms": 212.42, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.451244Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.451743Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.451743Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.451743Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.451743Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.452243Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.452243Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.452243Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.452243Z"}
{"status_code": 200, "response_time_ms": 219.71, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.671957Z"}
{"status_code": 200, "response_time_ms": 219.71, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.671957Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.672457Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.672457Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.672457Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.672457Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.672958Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.672958Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.672958Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.672958Z"}
{"status_code": 200, "response_time_ms": 84.45, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.757405Z"}
{"status_code": 200, "response_time_ms": 84.45, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.757405Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.757983Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.757983Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.758482Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.758482Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.758482Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.758482Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.758983Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.758983Z"}
{"status_code": 200, "response_time_ms": 101.52, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.860503Z"}
{"status_code": 200, "response_time_ms": 101.52, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.860503Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.861004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.861004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.861004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.861004Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.861004Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.861004Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.861503Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.861503Z"}
{"status_code": 200, "response_time_ms": 111.54, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.973039Z"}
{"status_code": 200, "response_time_ms": 111.54, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.973039Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.973540Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.973540Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.974040Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:09.974040Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.974040Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:09.974040Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.974538Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:09.974538Z"}
{"status_code": 200, "response_time_ms": 95.66, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.070202Z"}
{"status_code": 200, "response_time_ms": 95.66, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.070202Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:10.070702Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:10.070702Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:10.071201Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:52:10.071201Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:10.071201Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:52:10.071201Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.071201Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.071201Z"}
{"status_code": 200, "response_time_ms": 357.83, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.429027Z"}
{"status_code": 200, "response_time_ms": 357.83, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.429027Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.429527Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:52:10.429527Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:41.964782Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:41.964782Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 952 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.130796Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 952 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.130796Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.131296Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.131296Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.131296Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.131296Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.131797Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.131797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.132296Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.132296Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.132296Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.132296Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.132296Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.132296Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.133297Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.133297Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.133797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.133797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.133797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.133797Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.133797Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.133797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.135797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.135797Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.135797Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.135797Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.135797Z"}
{"author_id": "7119367979820646432", "event": "Fetching author information", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.135797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.136297Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.136297Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.136797Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.136797Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.136797Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.136797Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.137297Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_seed_base_info", "params": {"range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.137297Z"}
{"status_code": 200, "response_time_ms": 172.43, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.309722Z"}
{"status_code": 200, "response_time_ms": 172.43, "response_size_bytes": 149, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.309722Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.310306Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.310306Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.310806Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.310806Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.310806Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.310806Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.311306Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_commerce_spread_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.311306Z"}
{"status_code": 200, "response_time_ms": 79.39, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.390693Z"}
{"status_code": 200, "response_time_ms": 79.39, "response_size_bytes": 434, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.390693Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.391318Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.391318Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.391818Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.391818Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.391818Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.391818Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.392322Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_content_label_density", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.392322Z"}
{"status_code": 200, "response_time_ms": 85.83, "response_size_bytes": 892, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.478155Z"}
{"status_code": 200, "response_time_ms": 85.83, "response_size_bytes": 892, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.478155Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.478844Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.478844Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.479343Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.479343Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.479343Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.479343Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.479844Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_contract_base_info", "params": {"range": "90", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.479844Z"}
{"status_code": 200, "response_time_ms": 83.41, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.563250Z"}
{"status_code": 200, "response_time_ms": 83.41, "response_size_bytes": 923, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.563250Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.563851Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.563851Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.564351Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.564351Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.564351Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.564351Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.564351Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_global_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.564351Z"}
{"status_code": 200, "response_time_ms": 81.01, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.645364Z"}
{"status_code": 200, "response_time_ms": 81.01, "response_size_bytes": 141, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.645364Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.646008Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.646008Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.646513Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:47.646513Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.646513Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:47.646513Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.647012Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_homepage_videos", "params": {"page": "1", "limit": "10", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:47.647012Z"}
{"status_code": 200, "response_time_ms": 720.93, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.367942Z"}
{"status_code": 200, "response_time_ms": 720.93, "response_size_bytes": 9400, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.367942Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.368441Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.368441Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.368941Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.368941Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.368941Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.368941Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.368941Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_order_experience", "params": {"period": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.368941Z"}
{"status_code": 200, "response_time_ms": 72.33, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.441269Z"}
{"status_code": 200, "response_time_ms": 72.33, "response_size_bytes": 758, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.441269Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.441969Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.441969Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.442467Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.442467Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.442467Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.442467Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.442967Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_side_base_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.442967Z"}
{"status_code": 200, "response_time_ms": 75.34, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.518307Z"}
{"status_code": 200, "response_time_ms": 75.34, "response_size_bytes": 82, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.518307Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.518808Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.518808Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.519307Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.519307Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.519307Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.519307Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.519807Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_product_list", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.519807Z"}
{"status_code": 200, "response_time_ms": 75.98, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.595789Z"}
{"status_code": 200, "response_time_ms": 75.98, "response_size_bytes": 150, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.595789Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.596315Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.596315Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.596814Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.596814Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.596814Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.596814Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.597315Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/aggregator/get_author_video_live_linkage_stat", "params": {"time_period": "30", "star_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.597315Z"}
{"status_code": 200, "response_time_ms": 128.57, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.725888Z"}
{"status_code": 200, "response_time_ms": 128.57, "response_size_bytes": 297, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.725888Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.726388Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.726388Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.726388Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:48.726388Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.726388Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:48.726388Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.726888Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_base_info", "params": {"platform_source": "1", "platform_channel": "1", "recommend": "true", "need_sec_uid": "true", "need_linkage_info": "true", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:48.726888Z"}
{"status_code": 200, "response_time_ms": 493.16, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:49.220051Z"}
{"status_code": 200, "response_time_ms": 493.16, "response_size_bytes": 2662, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:49.220051Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:49.220551Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:49.220551Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:49.221050Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:49.221050Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:49.221050Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:49.221050Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:49.221050Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_marketing_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:49.221050Z"}
{"status_code": 200, "response_time_ms": 2123.48, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.344533Z"}
{"status_code": 200, "response_time_ms": 2123.48, "response_size_bytes": 3835, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.344533Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.345151Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.345151Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.345649Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.345649Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.345649Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.345649Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.346149Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_platform_channel_info_v2", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.346149Z"}
{"status_code": 200, "response_time_ms": 217.91, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.564056Z"}
{"status_code": 200, "response_time_ms": 217.91, "response_size_bytes": 221, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.564056Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.564680Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.564680Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.565180Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.565180Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.565180Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.565180Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.565680Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/author/get_author_show_items_v2", "params": {"platform_channel": "1", "platform_source": "1", "limit": "10", "only_assign": "false", "flow_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.565680Z"}
{"status_code": 200, "response_time_ms": 232.94, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.798616Z"}
{"status_code": 200, "response_time_ms": 232.94, "response_size_bytes": 39135, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.798616Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.799115Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.799115Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.799614Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.799614Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.799614Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.799614Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.800114Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data/get_author_hot_comment_tokens", "params": {"num": "10", "without_emoji": "true", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.800114Z"}
{"status_code": 200, "response_time_ms": 68.51, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.868620Z"}
{"status_code": 200, "response_time_ms": 68.51, "response_size_bytes": 501, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.868620Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.869120Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.869120Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.869120Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.869120Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.869620Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.869620Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.869620Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_audience_distribution", "params": {"platform_source": "1", "platform_channel": "1", "link_type": "5", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.869620Z"}
{"status_code": 200, "response_time_ms": 102.02, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.971639Z"}
{"status_code": 200, "response_time_ms": 102.02, "response_size_bytes": 6320, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.971639Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.972139Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.972139Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.972139Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:51.972139Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.972638Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:51.972638Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.972638Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_cp_info", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:51.972638Z"}
{"status_code": 200, "response_time_ms": 86.52, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.059158Z"}
{"status_code": 200, "response_time_ms": 86.52, "response_size_bytes": 234, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.059158Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.059657Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.059657Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.060157Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.060157Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.060157Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.060157Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.060657Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_daily_link", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "link_type": "5", "start_date": "2025-03-23", "end_date": "2025-04-22", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.060657Z"}
{"status_code": 200, "response_time_ms": 95.86, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.156520Z"}
{"status_code": 200, "response_time_ms": 95.86, "response_size_bytes": 1293, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.156520Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.157174Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.157174Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.157673Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.157673Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.157673Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.157673Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.158173Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_card", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.158173Z"}
{"status_code": 200, "response_time_ms": 124.03, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.282202Z"}
{"status_code": 200, "response_time_ms": 124.03, "response_size_bytes": 298, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.282202Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.282704Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.282704Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.282704Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.282704Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.283203Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.283203Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.283203Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_link_struct", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.283203Z"}
{"status_code": 200, "response_time_ms": 127.18, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.410379Z"}
{"status_code": 200, "response_time_ms": 127.18, "response_size_bytes": 505, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.410379Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.411004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.411004Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.411504Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.411504Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.411504Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.411504Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.411504Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_info", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.411504Z"}
{"status_code": 200, "response_time_ms": 62.49, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.473995Z"}
{"status_code": 200, "response_time_ms": 62.49, "response_size_bytes": 182, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.473995Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.474498Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.474498Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.475001Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.475001Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.475001Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.475001Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.475501Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_local_sales_performance", "params": {"platform_source": "1", "platform_channel": "1", "time_range": "30", "local_item_type": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.475501Z"}
{"status_code": 200, "response_time_ms": 66.51, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.542009Z"}
{"status_code": 200, "response_time_ms": 66.51, "response_size_bytes": 184, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.542009Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.542509Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.542509Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.543010Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.543010Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.543010Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.543010Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.543010Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_shopping_videos", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.543010Z"}
{"status_code": 200, "response_time_ms": 197.97, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.740978Z"}
{"status_code": 200, "response_time_ms": 197.97, "response_size_bytes": 4046, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.740978Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.741478Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.741478Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.741478Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.741478Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.741977Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.741977Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.741977Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_spread_videos", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.741977Z"}
{"status_code": 200, "response_time_ms": 214.57, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.956545Z"}
{"status_code": 200, "response_time_ms": 214.57, "response_size_bytes": 4061, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.956545Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.957045Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.957045Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.957546Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:52.957546Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.957546Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:52.957546Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.958045Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/author_video_distribution", "params": {"platform_source": "1", "platform_channel": "1", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:52.958045Z"}
{"status_code": 200, "response_time_ms": 94.24, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.052287Z"}
{"status_code": 200, "response_time_ms": 94.24, "response_size_bytes": 123, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.052287Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.052789Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.052789Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.052789Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.052789Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.053287Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.053287Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.053287Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_convert_ability", "params": {"platform_source": "1", "platform_channel": "1", "industry_id": "0", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.053287Z"}
{"status_code": 200, "response_time_ms": 108.57, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.161859Z"}
{"status_code": 200, "response_time_ms": 108.57, "response_size_bytes": 328, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.161859Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.162360Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.162360Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.162859Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.162859Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.162859Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.162859Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.163359Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_daily_fans", "params": {"platform_source": "1", "start_date": "2025-03-23", "end_date": "2025-04-22", "author_type": "1", "author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.163359Z"}
{"status_code": 200, "response_time_ms": 101.02, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.264379Z"}
{"status_code": 200, "response_time_ms": 101.02, "response_size_bytes": 1009, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.264379Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.264379Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.264379Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.264879Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.264879Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.264879Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.264879Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.264879Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/data_sp/get_author_spread_info", "params": {"platform_source": "1", "platform_channel": "1", "type": "2", "flow_type": "0", "only_assign": "true", "range": "2", "o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.264879Z"}
{"status_code": 200, "response_time_ms": 114.52, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.379395Z"}
{"status_code": 200, "response_time_ms": 114.52, "response_size_bytes": 629, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.379395Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.379897Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.379897Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.380395Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-06T08:53:53.380395Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.380395Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-06T08:53:53.380395Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.380895Z"}
{"method": "GET", "url": "https://www.xingtu.cn/gw/api/gauthor/author_get_business_card_info", "params": {"o_author_id": "7119367979820646432"}, "headers": {}, "event": "API request", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.380895Z"}
{"status_code": 200, "response_time_ms": 286.73, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.667624Z"}
{"status_code": 200, "response_time_ms": 286.73, "response_size_bytes": 1048, "event": "API response", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.667624Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.667624Z"}
{"author_id": "7119367979820646432", "successful": 29, "failed": 0, "success_rate": "100.0%", "event": "Author info fetch completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-06T08:53:53.667624Z"}
