{"event": "Settings validation passed", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:22.973338Z"}
{"event": "Settings validation passed", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:22.973338Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:22.974057Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "CookieCloud client initialized", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:22.974057Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 908 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:27.091146Z"}
{"operation": "initialize", "domain": "www.xingtu.cn", "success": true, "details": "Initial cookie load successful: 908 domains", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:27.091146Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-05T16:26:27.091772Z"}
{"event": "Xingtu session setup completed", "logger": "xingtu_client", "level": "info", "timestamp": "2025-06-05T16:26:27.091772Z"}
{"request_id": "611cdb0d-9c88-4450-9f20-750769d1e60e", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:31.162842Z"}
{"request_id": "611cdb0d-9c88-4450-9f20-750769d1e60e", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:31.162842Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.165028Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.165028Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.165608Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.165608Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.165975Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.165975Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.166564Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.166564Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.167012Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.167012Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:31.167445Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:31.167445Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.169081Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.169081Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.169606Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:31.169606Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:31.169926Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:31.169926Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:31.170243Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:31.170243Z"}
{"request_id": "611cdb0d-9c88-4450-9f20-750769d1e60e", "status_code": 200, "response_time_ms": 8.02, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:31.170810Z"}
{"request_id": "611cdb0d-9c88-4450-9f20-750769d1e60e", "status_code": 200, "response_time_ms": 8.02, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:31.170810Z"}
{"request_id": "0bb4a3ca-e5c4-44bb-8f80-b0a4141ef2a2", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:36.315486Z"}
{"request_id": "0bb4a3ca-e5c4-44bb-8f80-b0a4141ef2a2", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:36.315486Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.316270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.316270Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.316838Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.316838Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.317130Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.317130Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.317639Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.317639Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.318082Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.318082Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:36.318319Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:36.318319Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.319793Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.319793Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.320433Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:26:36.320433Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:36.320721Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:26:36.320721Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:36.320991Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:36.320991Z"}
{"request_id": "0bb4a3ca-e5c4-44bb-8f80-b0a4141ef2a2", "status_code": 200, "response_time_ms": 5.93, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:36.321375Z"}
{"request_id": "0bb4a3ca-e5c4-44bb-8f80-b0a4141ef2a2", "status_code": 200, "response_time_ms": 5.93, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:26:36.321375Z"}
{"request_id": "a90a5a62-ee2c-4cdb-ae2b-16db2914ec56", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:01.189025Z"}
{"request_id": "a90a5a62-ee2c-4cdb-ae2b-16db2914ec56", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:01.189025Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.190220Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.190220Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.190746Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.190746Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.191006Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.191006Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.191628Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.191628Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.192238Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.192238Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:01.192667Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:01.192667Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.194685Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.194685Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.195269Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:01.195269Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:01.195540Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:01.195540Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:01.195779Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:01.195779Z"}
{"request_id": "a90a5a62-ee2c-4cdb-ae2b-16db2914ec56", "status_code": 200, "response_time_ms": 7.24, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:01.196182Z"}
{"request_id": "a90a5a62-ee2c-4cdb-ae2b-16db2914ec56", "status_code": 200, "response_time_ms": 7.24, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:01.196182Z"}
{"request_id": "ff7b5d9c-0991-467d-9df2-98a91766c70f", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:31.213348Z"}
{"request_id": "ff7b5d9c-0991-467d-9df2-98a91766c70f", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:31.213348Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.214449Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.214449Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.215030Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.215030Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.215364Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.215364Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.215917Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.215917Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.216343Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.216343Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:31.216581Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:31.216581Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.218111Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.218111Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.218596Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:27:31.218596Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:31.218879Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:27:31.218879Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:31.219173Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:31.219173Z"}
{"request_id": "ff7b5d9c-0991-467d-9df2-98a91766c70f", "status_code": 200, "response_time_ms": 6.31, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:31.219570Z"}
{"request_id": "ff7b5d9c-0991-467d-9df2-98a91766c70f", "status_code": 200, "response_time_ms": 6.31, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:27:31.219570Z"}
{"request_id": "666e541b-dcf4-4b51-b978-5e20c2fd2733", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:01.237986Z"}
{"request_id": "666e541b-dcf4-4b51-b978-5e20c2fd2733", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:01.237986Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.239136Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.239136Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.239869Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.239869Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.240181Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.240181Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.240750Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.240750Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.241202Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.241202Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:01.241433Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:01.241433Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.242945Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.242945Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.243405Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:01.243405Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:01.243645Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:01.243645Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:01.243947Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:01.243947Z"}
{"request_id": "666e541b-dcf4-4b51-b978-5e20c2fd2733", "status_code": 200, "response_time_ms": 6.38, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:01.244267Z"}
{"request_id": "666e541b-dcf4-4b51-b978-5e20c2fd2733", "status_code": 200, "response_time_ms": 6.38, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:01.244267Z"}
{"request_id": "01257481-ab29-43d8-b4bc-6ba79b3e80c6", "method": "GET", "path": "/api/author/6651749112423120908/global_info", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:19.490124Z"}
{"request_id": "01257481-ab29-43d8-b4bc-6ba79b3e80c6", "method": "GET", "path": "/api/author/6651749112423120908/global_info", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:19.490124Z"}
{"request_id": "01257481-ab29-43d8-b4bc-6ba79b3e80c6", "status_code": 404, "response_time_ms": 0.64, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:19.490717Z"}
{"request_id": "01257481-ab29-43d8-b4bc-6ba79b3e80c6", "status_code": 404, "response_time_ms": 0.64, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:19.490717Z"}
{"request_id": "7ccaa708-6c7f-4148-a23a-ced9aaa951ea", "method": "GET", "path": "/api/author/6651749112423120908/global_info", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:26.213801Z"}
{"request_id": "7ccaa708-6c7f-4148-a23a-ced9aaa951ea", "method": "GET", "path": "/api/author/6651749112423120908/global_info", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:26.213801Z"}
{"request_id": "7ccaa708-6c7f-4148-a23a-ced9aaa951ea", "status_code": 404, "response_time_ms": 0.65, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:26.214420Z"}
{"request_id": "7ccaa708-6c7f-4148-a23a-ced9aaa951ea", "status_code": 404, "response_time_ms": 0.65, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:26.214420Z"}
{"request_id": "860f3d10-5f2c-4533-aa58-df1fb0e90419", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.262173Z"}
{"request_id": "860f3d10-5f2c-4533-aa58-df1fb0e90419", "method": "GET", "path": "/health", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.262173Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.263019Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.263019Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.263758Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.263758Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.264139Z"}
{"operation": "validate", "domain": "www.xingtu.cn", "success": true, "details": "Some optional cookies missing: ['sessionid'], but validation passed", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.264139Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.264817Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.264817Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.265299Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.265299Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:31.265569Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:31.265569Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.267109Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.267109Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.267604Z"}
{"operation": "get_xingtu", "domain": "www.xingtu.cn", "success": true, "details": "Prepared 20 cookies for Xingtu authentication", "event": "Cookie operation", "logger": "cookie_manager", "level": "info", "timestamp": "2025-06-05T16:28:31.267604Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:31.267888Z"}
{"event": "No CSRF token found in cookies", "logger": "cookie_manager", "level": "warning", "timestamp": "2025-06-05T16:28:31.267888Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.268198Z"}
{"component": "overall", "status": "healthy", "details": {"cookie_manager": {"healthy": true, "details": {"cookie_count": 0, "domains_count": 908, "last_updated": null, "status": "unhealthy"}}, "xingtu_client": {"healthy": true, "details": {"session_ready": true, "cookies_available": true}}, "excel_exporter": {"healthy": true, "details": {"export_dir_exists": null, "writable": null}}}, "event": "Health check", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.268198Z"}
{"request_id": "860f3d10-5f2c-4533-aa58-df1fb0e90419", "status_code": 200, "response_time_ms": 6.47, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.268603Z"}
{"request_id": "860f3d10-5f2c-4533-aa58-df1fb0e90419", "status_code": 200, "response_time_ms": 6.47, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.268603Z"}
{"request_id": "fe8fe800-3fca-4b3c-9bc9-bc5f22faf8ff", "method": "GET", "path": "/docs", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.589157Z"}
{"request_id": "fe8fe800-3fca-4b3c-9bc9-bc5f22faf8ff", "method": "GET", "path": "/docs", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.589157Z"}
{"request_id": "fe8fe800-3fca-4b3c-9bc9-bc5f22faf8ff", "status_code": 404, "response_time_ms": 0.72, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.589839Z"}
{"request_id": "fe8fe800-3fca-4b3c-9bc9-bc5f22faf8ff", "status_code": 404, "response_time_ms": 0.72, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:31.589839Z"}
{"request_id": "b225ad48-cd22-43c8-bfd5-ec3a204c2d02", "method": "GET", "path": "/", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:44.736226Z"}
{"request_id": "b225ad48-cd22-43c8-bfd5-ec3a204c2d02", "method": "GET", "path": "/", "client_ip": "12ca17b49af22894", "user_agent": "curl/7.88.1", "event": "Incoming request", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:44.736226Z"}
{"request_id": "b225ad48-cd22-43c8-bfd5-ec3a204c2d02", "status_code": 200, "response_time_ms": 2.27, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:44.738468Z"}
{"request_id": "b225ad48-cd22-43c8-bfd5-ec3a204c2d02", "status_code": 200, "response_time_ms": 2.27, "response_size_bytes": null, "event": "Outgoing response", "logger": "xingtu_api", "level": "info", "timestamp": "2025-06-05T16:28:44.738468Z"}
