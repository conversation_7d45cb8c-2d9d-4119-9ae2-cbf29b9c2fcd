#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整版达人信息获取测试 - 测试实际的API调用
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from utils.logger import setup_logger
from config.settings import settings
from config.api_endpoints import get_all_categories


async def test_single_endpoint(xingtu_client, author_id, endpoint_name):
    """测试单个端点"""
    print(f"🎯 测试端点: {endpoint_name}")
    
    try:
        result = await xingtu_client.get_author_info(
            author_id=author_id,
            endpoints=[endpoint_name]
        )
        
        if result:
            successful = result.get('successful_endpoints', {})
            failed = result.get('failed_endpoints', {})
            
            if endpoint_name in successful:
                data = successful[endpoint_name]
                print(f"   ✅ 成功获取数据")
                if isinstance(data, dict) and 'data' in data:
                    if data['data']:
                        data_keys = list(data['data'].keys()) if isinstance(data['data'], dict) else []
                        print(f"   📊 数据字段: {data_keys[:10]}..." if len(data_keys) > 10 else f"   📊 数据字段: {data_keys}")
                    else:
                        print(f"   ⚠️  返回数据为空")
                else:
                    print(f"   📄 原始响应: {str(data)[:200]}...")
                    
            elif endpoint_name in failed:
                error = failed[endpoint_name]
                print(f"   ❌ 获取失败: {error}")
            else:
                print(f"   ⚠️  未找到结果")
        else:
            print(f"   ❌ 无响应")
            
    except Exception as e:
        print(f"   ❌ 异常: {e}")


async def test_author_data_fetch():
    """测试达人数据获取功能"""
    
    print("🧪 完整版达人信息获取测试")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger("full_test", "INFO")
    
    try:
        # 初始化组件
        print("📁 初始化组件...")
        cookie_manager = CookieManager()
        xingtu_client = XingtuClient(cookie_manager)
        
        # 检查组件状态
        print(f"🍪 Cookie管理器: {'✅ 健康' if cookie_manager.is_healthy() else '❌ 异常'}")
        client_status = xingtu_client.get_health_status()
        print(f"🔗 Xingtu客户端: {'✅ 健康' if client_status['session_ready'] else '❌ 异常'}")
        
        # 测试用的作者ID列表（可以修改为真实的作者ID）
        test_author_ids = [
            "**********", # 示例作者ID，需要替换为真实ID
            "123456789",  # 备用测试ID
        ]
        
        print(f"\n🎯 准备测试的作者ID: {test_author_ids}")
        print("💡 注意: 请将上述ID替换为真实的星图作者ID")
        
        # 获取可用端点
        available_endpoints = xingtu_client.get_available_endpoints()
        print(f"\n📡 可用端点总数: {len(available_endpoints)}")
        
        # 按分类显示端点
        categories = get_all_categories()
        for category in categories:
            category_endpoints = [ep for ep in available_endpoints if ep['category'] == category]
            print(f"   📋 {category}: {len(category_endpoints)}个端点")
        
        # 选择要测试的作者ID
        test_author_id = test_author_ids[0]
        print(f"\n🔍 开始测试作者 {test_author_id} 的数据获取...")
        
        # 测试策略1: 测试基础信息端点
        print(f"\n📊 策略1: 测试基础信息端点")
        basic_endpoints = ["global_info", "base_info", "side_base_info"]
        
        for endpoint_name in basic_endpoints:
            if any(ep['name'] == endpoint_name for ep in available_endpoints):
                await test_single_endpoint(xingtu_client, test_author_id, endpoint_name)
            else:
                print(f"   ⚠️  端点 {endpoint_name} 不可用")
        
        # 测试策略2: 测试营销相关端点
        print(f"\n💰 策略2: 测试营销相关端点")
        marketing_endpoints = ["marketing_info", "commerce_seed_base_info", "commerce_spread_info"]
        
        for endpoint_name in marketing_endpoints:
            if any(ep['name'] == endpoint_name for ep in available_endpoints):
                await test_single_endpoint(xingtu_client, test_author_id, endpoint_name)
            else:
                print(f"   ⚠️  端点 {endpoint_name} 不可用")
        
        # 测试策略3: 批量测试（一次调用多个端点）
        print(f"\n🚀 策略3: 批量测试多个端点")
        batch_endpoints = ["global_info", "base_info", "marketing_info"]
        available_batch = [ep for ep in batch_endpoints if any(avail['name'] == ep for avail in available_endpoints)]
        
        if available_batch:
            print(f"🔄 同时测试端点: {available_batch}")
            
            try:
                batch_result = await xingtu_client.get_author_info(
                    author_id=test_author_id,
                    endpoints=available_batch
                )
                
                if batch_result:
                    successful = batch_result.get('successful_endpoints', {})
                    failed = batch_result.get('failed_endpoints', {})
                    total_time = batch_result.get('total_time', 0)
                    
                    print(f"   ✅ 批量测试结果:")
                    print(f"   📊 成功: {len(successful)}/{len(available_batch)}个端点")
                    print(f"   ⏱️  总耗时: {total_time:.2f}秒")
                    
                    for endpoint_name in successful:
                        print(f"      ✅ {endpoint_name}: 数据获取成功")
                    
                    for endpoint_name in failed:
                        print(f"      ❌ {endpoint_name}: {failed[endpoint_name]}")
                        
                else:
                    print(f"   ❌ 批量测试无响应")
                    
            except Exception as e:
                print(f"   ❌ 批量测试异常: {e}")
        
        # 测试策略4: 测试分类获取
        print(f"\n📂 策略4: 按分类测试")
        for category in ["basic", "commerce"][:2]:  # 只测试前两个分类
            print(f"🏷️  测试分类: {category}")
            
            try:
                category_result = await xingtu_client.get_author_info_by_category(
                    author_id=test_author_id,
                    category=category
                )
                
                if category_result:
                    successful = category_result.get('successful_endpoints', {})
                    failed = category_result.get('failed_endpoints', {})
                    
                    print(f"   📊 分类 {category} 结果: {len(successful)}成功, {len(failed)}失败")
                    
                    if successful:
                        for endpoint_name in list(successful.keys())[:3]:  # 只显示前3个
                            print(f"      ✅ {endpoint_name}")
                    
                    if failed:
                        for endpoint_name in list(failed.keys())[:3]:  # 只显示前3个
                            print(f"      ❌ {endpoint_name}: {failed[endpoint_name][:50]}...")
                else:
                    print(f"   ❌ 分类 {category} 无响应")
                    
            except Exception as e:
                print(f"   ❌ 分类 {category} 测试异常: {e}")
        
        print(f"\n🏁 测试完成")
        
        # 最终总结
        print(f"\n📋 测试总结:")
        print(f"   🎯 测试作者ID: {test_author_id}")
        print(f"   📡 可用端点: {len(available_endpoints)}个")
        print(f"   📂 可用分类: {len(categories)}个")
        print(f"   🍪 Cookie状态: {'正常' if cookie_manager.is_healthy() else '异常'}")
        print(f"   🔗 客户端状态: {'正常' if client_status['session_ready'] else '异常'}")
        
        print(f"\n💡 提示:")
        print(f"   - 如果所有端点都返回错误，请检查作者ID是否有效")
        print(f"   - 如果部分端点成功，说明系统基本功能正常")
        print(f"   - 如果Cookie相关错误，请检查CookieCloud配置")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 星图超级接口线上化 - 完整版本地测试")
    print("ℹ️  这个测试会尝试获取真实的达人数据")
    print("ℹ️  请确保提供了有效的作者ID")
    print("=" * 60)
    
    # 运行异步测试
    try:
        asyncio.run(test_author_data_fetch())
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 测试结束") 