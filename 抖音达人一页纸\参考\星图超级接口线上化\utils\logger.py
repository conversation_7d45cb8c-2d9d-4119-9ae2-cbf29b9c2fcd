#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Logging configuration for 星图超级接口线上化
"""

import logging
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Any, Dict
import structlog


def setup_logger(name: str = "xingtu_api", level: str = "INFO") -> structlog.BoundLogger:
    """
    Setup structured logger with console and file handlers
    
    Args:
        name: Logger name
        level: Logging level
        
    Returns:
        Configured structured logger instance
    """
    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Set logging level
    log_level = getattr(logging, level.upper(), logging.INFO)
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=log_level,
    )
    
    # Create file handler
    log_file = log_dir / f"xingtu_api_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(log_level)
    
    # Add file handler to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)
    
    # Create structured logger
    logger = structlog.get_logger(name)
    
    return logger


def log_api_request(logger: structlog.BoundLogger, method: str, url: str, 
                   params: Dict[str, Any] = None, headers: Dict[str, str] = None):
    """Log API request details"""
    logger.info(
        "API request",
        method=method,
        url=url,
        params=params or {},
        headers={k: v for k, v in (headers or {}).items() if k.lower() not in ['cookie', 'authorization']}
    )


def log_api_response(logger: structlog.BoundLogger, status_code: int, 
                    response_time: float, response_size: int = None):
    """Log API response details"""
    logger.info(
        "API response",
        status_code=status_code,
        response_time_ms=round(response_time * 1000, 2),
        response_size_bytes=response_size
    )


def log_error(logger: structlog.BoundLogger, error: Exception, context: Dict[str, Any] = None):
    """Log error with context"""
    logger.error(
        "Error occurred",
        error_type=type(error).__name__,
        error_message=str(error),
        context=context or {}
    )


def log_cookie_operation(logger: structlog.BoundLogger, operation: str, 
                        domain: str, success: bool, details: str = None):
    """Log cookie operations"""
    logger.info(
        "Cookie operation",
        operation=operation,
        domain=domain,
        success=success,
        details=details
    )


def log_export_operation(logger: structlog.BoundLogger, operation: str, 
                        file_path: str, record_count: int = None, 
                        file_size: int = None, success: bool = True):
    """Log export operations"""
    logger.info(
        "Export operation",
        operation=operation,
        file_path=file_path,
        record_count=record_count,
        file_size_bytes=file_size,
        success=success
    )


class RequestLogger:
    """Request logging middleware"""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
    
    def log_request(self, request_id: str, method: str, path: str, 
                   client_ip: str, user_agent: str = None):
        """Log incoming request"""
        self.logger.info(
            "Incoming request",
            request_id=request_id,
            method=method,
            path=path,
            client_ip=client_ip,
            user_agent=user_agent
        )
    
    def log_response(self, request_id: str, status_code: int, 
                    response_time: float, response_size: int = None):
        """Log outgoing response"""
        self.logger.info(
            "Outgoing response",
            request_id=request_id,
            status_code=status_code,
            response_time_ms=round(response_time * 1000, 2),
            response_size_bytes=response_size
        )


# Default logger instance
logger = setup_logger()


# Performance monitoring
class PerformanceMonitor:
    """Performance monitoring utilities"""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
    
    def log_performance_metrics(self, operation: str, duration: float, 
                              success: bool, metadata: Dict[str, Any] = None):
        """Log performance metrics"""
        self.logger.info(
            "Performance metrics",
            operation=operation,
            duration_ms=round(duration * 1000, 2),
            success=success,
            metadata=metadata or {}
        )


# Health check logging
def log_health_check(logger: structlog.BoundLogger, component: str, 
                    status: str, details: Dict[str, Any] = None):
    """Log health check results"""
    logger.info(
        "Health check",
        component=component,
        status=status,
        details=details or {}
    )
