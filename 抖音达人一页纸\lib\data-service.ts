/**
 * Data Service for Influencer Resume System
 * Simulates API responses using local data files
 */

import fs from 'fs'
import path from 'path'

export interface InfluencerData {
  id: string
  nick_name: string
  avatar_uri: string
  follower: number
  city: string
  mcn_name: string
  self_intro: string
  // Add other fields as needed
  [key: string]: any
}

export interface ApiResponse {
  success: boolean
  data?: InfluencerData
  error?: string
  message?: string
}

/**
 * Data mapping for available influencers
 * Maps 达人ID to their data file locations
 */
const INFLUENCER_DATA_MAP: Record<string, string> = {
  '7119367979820646432': '参考/cankao.json', // 二阳咂 - existing data
  // Add more influencers as data becomes available
}

/**
 * Simulated API response structure based on 请求响应 directory
 */
export class DataService {
  private static instance: DataService
  private dataCache: Map<string, InfluencerData> = new Map()

  private constructor() {}

  public static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService()
    }
    return DataService.instance
  }

  /**
   * Get influencer data by ID
   * Simulates the API call: GET /author/{author_id}?api_key=XingTu2025SuperSecureKey_dxy_production_v1.0
   */
  public async getInfluencerById(talentId: string): Promise<ApiResponse> {
    try {
      // Check cache first
      if (this.dataCache.has(talentId)) {
        return {
          success: true,
          data: this.dataCache.get(talentId)!
        }
      }

      // Check if we have data for this ID
      const dataFilePath = INFLUENCER_DATA_MAP[talentId]
      if (!dataFilePath) {
        return {
          success: false,
          error: 'INFLUENCER_NOT_FOUND',
          message: `达人ID ${talentId} 未找到对应数据`
        }
      }

      // Load data from file
      const data = await this.loadInfluencerData(dataFilePath)
      if (!data) {
        return {
          success: false,
          error: 'DATA_LOAD_ERROR',
          message: '数据加载失败'
        }
      }

      // Validate and transform data
      const transformedData = this.transformData(data, talentId)
      
      // Cache the result
      this.dataCache.set(talentId, transformedData)

      return {
        success: true,
        data: transformedData
      }

    } catch (error) {
      console.error('Error fetching influencer data:', error)
      return {
        success: false,
        error: 'INTERNAL_ERROR',
        message: '服务器内部错误'
      }
    }
  }

  /**
   * Load influencer data from JSON file
   */
  private async loadInfluencerData(filePath: string): Promise<any | null> {
    try {
      // In a real implementation, this would be an API call
      // For now, we'll use dynamic import for client-side compatibility
      if (typeof window !== 'undefined') {
        // Client-side: use dynamic import
        const module = await import(`../${filePath}`)
        return module.default
      } else {
        // Server-side: use fs (for SSG/SSR)
        const fullPath = path.join(process.cwd(), '抖音达人一页纸', filePath)
        if (fs.existsSync(fullPath)) {
          const fileContent = fs.readFileSync(fullPath, 'utf-8')
          return JSON.parse(fileContent)
        }
        return null
      }
    } catch (error) {
      console.error('Error loading data file:', error)
      return null
    }
  }

  /**
   * Transform raw data to standardized format
   */
  private transformData(rawData: any, talentId: string): InfluencerData {
    return {
      id: talentId,
      nick_name: rawData.nick_name || '',
      avatar_uri: rawData.avatar_uri || '',
      follower: rawData.follower || 0,
      city: rawData.city || '',
      mcn_name: rawData.mcn_name || '',
      self_intro: rawData.self_intro || '',
      
      // Include all original data
      ...rawData,
      
      // Add computed fields
      computed: {
        followerDisplay: this.formatFollowerCount(rawData.follower || 0),
        scoreDisplay: this.calculateScore(rawData),
        lastUpdated: new Date().toISOString()
      }
    }
  }

  /**
   * Format follower count for display
   */
  private formatFollowerCount(count: number): string {
    if (count >= 10000) {
      return Math.round(count / 10000 * 10) / 10 + '万'
    }
    return count.toString()
  }

  /**
   * Calculate comprehensive score
   */
  private calculateScore(data: any): string {
    try {
      const interactionRate = data.items?.reduce((sum: number, item: any) => 
        sum + (item.interact_rate || 0), 0) / (data.items?.length || 1)
      const replyRate = parseFloat(data.message_reply_rate || '0')
      const fansGrowth = parseFloat(data.fans_growth_rate_30d || '0')
      
      const score = (interactionRate * 40 + replyRate * 30 + fansGrowth * 30) * 10
      return Math.min(score, 10).toFixed(1)
    } catch {
      return '8.6' // Default score
    }
  }

  /**
   * Get list of available influencer IDs
   */
  public getAvailableInfluencers(): string[] {
    return Object.keys(INFLUENCER_DATA_MAP)
  }

  /**
   * Check if influencer ID exists
   */
  public isValidInfluencerId(talentId: string): boolean {
    return INFLUENCER_DATA_MAP.hasOwnProperty(talentId)
  }

  /**
   * Simulate API response headers and metadata
   */
  public getApiMetadata() {
    return {
      server: 'uvicorn',
      contentType: 'application/json',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }
  }
}

// Export singleton instance
export const dataService = DataService.getInstance()

// Export types
export type { ApiResponse, InfluencerData }
