{"version": 3, "sources": ["../../../../src/build/babel/loader/get-config.ts"], "sourcesContent": ["import { readFileSync } from 'fs'\nimport J<PERSON>N5 from 'next/dist/compiled/json5'\n\nimport { createConfigItem, loadOptions } from 'next/dist/compiled/babel/core'\nimport loadConfig from 'next/dist/compiled/babel/core-lib-config'\n\nimport type { NextBabelLoaderOptions, NextJsLoaderContext } from './types'\nimport { consumeIterator } from './util'\nimport * as Log from '../../output/log'\nimport jsx from 'next/dist/compiled/babel/plugin-syntax-jsx'\n\nconst nextDistPath =\n  /(next[\\\\/]dist[\\\\/]shared[\\\\/]lib)|(next[\\\\/]dist[\\\\/]client)|(next[\\\\/]dist[\\\\/]pages)/\n\n/**\n * The properties defined here are the conditions with which subsets of inputs\n * can be identified that are able to share a common Babel config.  For example,\n * in dev mode, different transforms must be applied to a source file depending\n * on whether you're compiling for the client or for the server - thus `isServer`\n * is germane.\n *\n * However, these characteristics need not protect against circumstances that\n * will not be encountered in Next.js.  For example, a source file may be\n * transformed differently depending on whether we're doing a production compile\n * or for HMR in dev mode.  However, those two circumstances will never be\n * encountered within the context of a single V8 context (and, thus, shared\n * cache).  Therefore, hasReactRefresh is _not_ germane to caching.\n *\n * NOTE: This approach does not support multiple `.babelrc` files in a\n * single project.  A per-cache-key config will be generated once and,\n * if `.babelrc` is present, that config will be used for any subsequent\n * transformations.\n */\ninterface CharacteristicsGermaneToCaching {\n  isServer: boolean\n  isPageFile: boolean\n  isNextDist: boolean\n  hasModuleExports: boolean\n  fileExt: string\n}\n\nconst fileExtensionRegex = /\\.([a-z]+)$/\nfunction getCacheCharacteristics(\n  loaderOptions: NextBabelLoaderOptions,\n  source: string,\n  filename: string\n): CharacteristicsGermaneToCaching {\n  const { isServer, pagesDir } = loaderOptions\n  const isPageFile = filename.startsWith(pagesDir)\n  const isNextDist = nextDistPath.test(filename)\n  const hasModuleExports = source.indexOf('module.exports') !== -1\n  const fileExt = fileExtensionRegex.exec(filename)?.[1] || 'unknown'\n\n  return {\n    isServer,\n    isPageFile,\n    isNextDist,\n    hasModuleExports,\n    fileExt,\n  }\n}\n\n/**\n * Return an array of Babel plugins, conditioned upon loader options and\n * source file characteristics.\n */\nfunction getPlugins(\n  loaderOptions: NextBabelLoaderOptions,\n  cacheCharacteristics: CharacteristicsGermaneToCaching\n) {\n  const { isServer, isPageFile, isNextDist, hasModuleExports } =\n    cacheCharacteristics\n\n  const { development } = loaderOptions\n  const hasReactRefresh =\n    loaderOptions.transformMode !== 'standalone'\n      ? loaderOptions.hasReactRefresh\n      : false\n\n  const applyCommonJsItem = hasModuleExports\n    ? createConfigItem(require('../plugins/commonjs'), { type: 'plugin' })\n    : null\n  const reactRefreshItem = hasReactRefresh\n    ? createConfigItem(\n        [\n          require('next/dist/compiled/react-refresh/babel'),\n          { skipEnvCheck: true },\n        ],\n        { type: 'plugin' }\n      )\n    : null\n  const pageConfigItem =\n    !isServer && isPageFile\n      ? createConfigItem([require('../plugins/next-page-config')], {\n          type: 'plugin',\n        })\n      : null\n  const disallowExportAllItem =\n    !isServer && isPageFile\n      ? createConfigItem(\n          [require('../plugins/next-page-disallow-re-export-all-exports')],\n          { type: 'plugin' }\n        )\n      : null\n  const transformDefineItem = createConfigItem(\n    [\n      require.resolve('next/dist/compiled/babel/plugin-transform-define'),\n      {\n        'process.env.NODE_ENV': development ? 'development' : 'production',\n        'typeof window': isServer ? 'undefined' : 'object',\n        'process.browser': isServer ? false : true,\n      },\n      'next-js-transform-define-instance',\n    ],\n    { type: 'plugin' }\n  )\n  const nextSsgItem =\n    !isServer && isPageFile\n      ? createConfigItem([require.resolve('../plugins/next-ssg-transform')], {\n          type: 'plugin',\n        })\n      : null\n  const commonJsItem = isNextDist\n    ? createConfigItem(\n        require('next/dist/compiled/babel/plugin-transform-modules-commonjs'),\n        { type: 'plugin' }\n      )\n    : null\n  const nextFontUnsupported = createConfigItem(\n    [require('../plugins/next-font-unsupported')],\n    { type: 'plugin' }\n  )\n\n  return [\n    reactRefreshItem,\n    pageConfigItem,\n    disallowExportAllItem,\n    applyCommonJsItem,\n    transformDefineItem,\n    nextSsgItem,\n    commonJsItem,\n    nextFontUnsupported,\n  ].filter(Boolean)\n}\n\nconst isJsonFile = /\\.(json|babelrc)$/\nconst isJsFile = /\\.js$/\n\n/**\n * While this function does block execution while reading from disk, it\n * should not introduce any issues.  The function is only invoked when\n * generating a fresh config, and only a small handful of configs should\n * be generated during compilation.\n */\nfunction getCustomBabelConfig(configFilePath: string) {\n  if (isJsonFile.exec(configFilePath)) {\n    const babelConfigRaw = readFileSync(configFilePath, 'utf8')\n    return JSON5.parse(babelConfigRaw)\n  } else if (isJsFile.exec(configFilePath)) {\n    return require(configFilePath)\n  }\n  throw new Error(\n    'The Next.js Babel loader does not support .mjs or .cjs config files.'\n  )\n}\n\nlet babelConfigWarned = false\n/**\n * Check if custom babel configuration from user only contains options that\n * can be migrated into latest Next.js features supported by SWC.\n *\n * This raises soft warning messages only, not making any errors yet.\n */\nfunction checkCustomBabelConfigDeprecation(\n  config: Record<string, any> | undefined\n) {\n  if (!config || Object.keys(config).length === 0) {\n    return\n  }\n\n  const { plugins, presets, ...otherOptions } = config\n  if (Object.keys(otherOptions ?? {}).length > 0) {\n    return\n  }\n\n  if (babelConfigWarned) {\n    return\n  }\n\n  babelConfigWarned = true\n\n  const isPresetReadyToDeprecate =\n    !presets ||\n    presets.length === 0 ||\n    (presets.length === 1 && presets[0] === 'next/babel')\n  const pluginReasons = []\n  const unsupportedPlugins = []\n\n  if (Array.isArray(plugins)) {\n    for (const plugin of plugins) {\n      const pluginName = Array.isArray(plugin) ? plugin[0] : plugin\n\n      // [NOTE]: We cannot detect if the user uses babel-plugin-macro based transform plugins,\n      // such as `styled-components/macro` in here.\n      switch (pluginName) {\n        case 'styled-components':\n        case 'babel-plugin-styled-components':\n          pluginReasons.push(\n            `\\t- 'styled-components' can be enabled via 'compiler.styledComponents' in 'next.config.js'`\n          )\n          break\n        case '@emotion/babel-plugin':\n          pluginReasons.push(\n            `\\t- '@emotion/babel-plugin' can be enabled via 'compiler.emotion' in 'next.config.js'`\n          )\n          break\n        case 'babel-plugin-relay':\n          pluginReasons.push(\n            `\\t- 'babel-plugin-relay' can be enabled via 'compiler.relay' in 'next.config.js'`\n          )\n          break\n        case 'react-remove-properties':\n          pluginReasons.push(\n            `\\t- 'react-remove-properties' can be enabled via 'compiler.reactRemoveProperties' in 'next.config.js'`\n          )\n          break\n        case 'transform-remove-console':\n          pluginReasons.push(\n            `\\t- 'transform-remove-console' can be enabled via 'compiler.removeConsole' in 'next.config.js'`\n          )\n          break\n        default:\n          unsupportedPlugins.push(pluginName)\n          break\n      }\n    }\n  }\n\n  if (isPresetReadyToDeprecate && unsupportedPlugins.length === 0) {\n    Log.warn(\n      `It looks like there is a custom Babel configuration that can be removed${\n        pluginReasons.length > 0 ? ':' : '.'\n      }`\n    )\n\n    if (pluginReasons.length > 0) {\n      Log.warn(`Next.js supports the following features natively: `)\n      Log.warn(pluginReasons.join(''))\n      Log.warn(\n        `For more details configuration options, please refer https://nextjs.org/docs/architecture/nextjs-compiler#supported-features`\n      )\n    }\n  }\n}\n\n/**\n * Generate a new, flat Babel config, ready to be handed to Babel-traverse.\n * This config should have no unresolved overrides, presets, etc.\n */\nfunction getFreshConfig(\n  this: NextJsLoaderContext,\n  cacheCharacteristics: CharacteristicsGermaneToCaching,\n  loaderOptions: NextBabelLoaderOptions,\n  target: string,\n  filename: string,\n  inputSourceMap?: object | null\n) {\n  const hasReactCompiler = (() => {\n    if (\n      loaderOptions.reactCompilerPlugins &&\n      loaderOptions.reactCompilerPlugins.length === 0\n    ) {\n      return false\n    }\n\n    if (/[/\\\\]node_modules[/\\\\]/.test(filename)) {\n      return false\n    }\n\n    if (\n      loaderOptions.reactCompilerExclude &&\n      loaderOptions.reactCompilerExclude(filename)\n    ) {\n      return false\n    }\n\n    return true\n  })()\n\n  const reactCompilerPluginsIfEnabled = hasReactCompiler\n    ? loaderOptions.reactCompilerPlugins ?? []\n    : []\n\n  let { isServer, pagesDir, srcDir, development } = loaderOptions\n\n  let options = {\n    babelrc: false,\n    cloneInputAst: false,\n    filename,\n    inputSourceMap: inputSourceMap || undefined,\n\n    // Ensure that Webpack will get a full absolute path in the sourcemap\n    // so that it can properly map the module back to its internal cached\n    // modules.\n    sourceFileName: filename,\n    sourceMaps: this.sourceMap,\n  } as any\n\n  const baseCaller = {\n    name: 'next-babel-turbo-loader',\n    supportsStaticESM: true,\n    supportsDynamicImport: true,\n\n    // Provide plugins with insight into webpack target.\n    // https://github.com/babel/babel-loader/issues/787\n    target: target,\n\n    // Webpack 5 supports TLA behind a flag. We enable it by default\n    // for Babel, and then webpack will throw an error if the experimental\n    // flag isn't enabled.\n    supportsTopLevelAwait: true,\n\n    isServer,\n    srcDir,\n    pagesDir,\n    isDev: development,\n\n    ...loaderOptions.caller,\n  }\n\n  if (loaderOptions.transformMode === 'standalone') {\n    if (!reactCompilerPluginsIfEnabled.length) {\n      return null\n    }\n\n    options.plugins = [jsx, ...reactCompilerPluginsIfEnabled]\n    options.presets = [\n      [\n        require('next/dist/compiled/babel/preset-typescript'),\n        { allowNamespaces: true },\n      ],\n    ]\n    options.caller = baseCaller\n  } else {\n    let { configFile, hasJsxRuntime } = loaderOptions\n    let customConfig: any = configFile\n      ? getCustomBabelConfig(configFile)\n      : undefined\n\n    checkCustomBabelConfigDeprecation(customConfig)\n\n    // Set the default sourcemap behavior based on Webpack's mapping flag,\n    // but allow users to override if they want.\n    options.sourceMaps =\n      loaderOptions.sourceMaps === undefined\n        ? this.sourceMap\n        : loaderOptions.sourceMaps\n\n    options.plugins = [\n      ...getPlugins(loaderOptions, cacheCharacteristics),\n      ...reactCompilerPluginsIfEnabled,\n      ...(customConfig?.plugins || []),\n    ]\n\n    // target can be provided in babelrc\n    options.target = isServer ? undefined : customConfig?.target\n\n    // env can be provided in babelrc\n    options.env = customConfig?.env\n\n    options.presets = (() => {\n      // If presets is defined the user will have next/babel in their babelrc\n      if (customConfig?.presets) {\n        return customConfig.presets\n      }\n\n      // If presets is not defined the user will likely have \"env\" in their babelrc\n      if (customConfig) {\n        return undefined\n      }\n\n      // If no custom config is provided the default is to use next/babel\n      return ['next/babel']\n    })()\n\n    options.overrides = loaderOptions.overrides\n\n    options.caller = {\n      ...baseCaller,\n      hasJsxRuntime,\n    }\n  }\n\n  // Babel does strict checks on the config so undefined is not allowed\n  if (typeof options.target === 'undefined') {\n    delete options.target\n  }\n\n  Object.defineProperty(options.caller, 'onWarning', {\n    enumerable: false,\n    writable: false,\n    value: (reason: any) => {\n      if (!(reason instanceof Error)) {\n        reason = new Error(reason)\n      }\n      this.emitWarning(reason)\n    },\n  })\n\n  const loadedOptions = loadOptions(options)\n  const config = consumeIterator(loadConfig(loadedOptions))\n\n  return config\n}\n\n/**\n * Each key returned here corresponds with a Babel config that can be shared.\n * The conditions of permissible sharing between files is dependent on specific\n * file attributes and Next.js compiler states: `CharacteristicsGermaneToCaching`.\n */\nfunction getCacheKey(cacheCharacteristics: CharacteristicsGermaneToCaching) {\n  const { isServer, isPageFile, isNextDist, hasModuleExports, fileExt } =\n    cacheCharacteristics\n\n  const flags =\n    0 |\n    (isServer ? 0b0001 : 0) |\n    (isPageFile ? 0b0010 : 0) |\n    (isNextDist ? 0b0100 : 0) |\n    (hasModuleExports ? 0b1000 : 0)\n\n  return fileExt + flags\n}\n\ntype BabelConfig = any\nconst configCache: Map<any, BabelConfig> = new Map()\nconst configFiles: Set<string> = new Set()\n\nexport default function getConfig(\n  this: NextJsLoaderContext,\n  {\n    source,\n    target,\n    loaderOptions,\n    filename,\n    inputSourceMap,\n  }: {\n    source: string\n    loaderOptions: NextBabelLoaderOptions\n    target: string\n    filename: string\n    inputSourceMap?: object | null\n  }\n): BabelConfig {\n  const cacheCharacteristics = getCacheCharacteristics(\n    loaderOptions,\n    source,\n    filename\n  )\n\n  if (loaderOptions.transformMode === 'default' && loaderOptions.configFile) {\n    // Ensures webpack invalidates the cache for this loader when the config file changes\n    this.addDependency(loaderOptions.configFile)\n  }\n\n  const cacheKey = getCacheKey(cacheCharacteristics)\n  if (configCache.has(cacheKey)) {\n    const cachedConfig = configCache.get(cacheKey)\n    if (!cachedConfig) {\n      return null\n    }\n\n    return {\n      ...cachedConfig,\n      options: {\n        ...cachedConfig.options,\n        cwd: loaderOptions.cwd,\n        root: loaderOptions.cwd,\n        filename,\n        sourceFileName: filename,\n      },\n    }\n  }\n\n  if (\n    loaderOptions.transformMode === 'default' &&\n    loaderOptions.configFile &&\n    !configFiles.has(loaderOptions.configFile)\n  ) {\n    configFiles.add(loaderOptions.configFile)\n    Log.info(\n      `Using external babel configuration from ${loaderOptions.configFile}`\n    )\n  }\n\n  const freshConfig = getFreshConfig.call(\n    this,\n    cacheCharacteristics,\n    loaderOptions,\n    target,\n    filename,\n    inputSourceMap\n  )\n\n  configCache.set(cacheKey, freshConfig)\n\n  return freshConfig\n}\n"], "names": ["getConfig", "nextDistPath", "fileExtensionRegex", "getCacheCharacteristics", "loaderOptions", "source", "filename", "isServer", "pagesDir", "isPageFile", "startsWith", "isNextDist", "test", "hasModuleExports", "indexOf", "fileExt", "exec", "getPlugins", "cacheCharacteristics", "development", "hasReactRefresh", "transformMode", "applyCommonJsItem", "createConfigItem", "require", "type", "reactRefreshItem", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pageConfigItem", "disallowExportAllItem", "transformDefineItem", "resolve", "nextSsgItem", "commonJsItem", "nextFontUnsupported", "filter", "Boolean", "isJsonFile", "isJsFile", "getCustomBabelConfig", "config<PERSON><PERSON><PERSON><PERSON>", "babelConfigRaw", "readFileSync", "JSON5", "parse", "Error", "babelConfigWarned", "checkCustomBabelConfigDeprecation", "config", "Object", "keys", "length", "plugins", "presets", "otherOptions", "isPresetReadyToDeprecate", "pluginReasons", "unsupportedPlugins", "Array", "isArray", "plugin", "pluginName", "push", "Log", "warn", "join", "getFreshConfig", "target", "inputSourceMap", "hasReactCompiler", "reactCompilerPlugins", "reactCompilerExclude", "reactCompilerPluginsIfEnabled", "srcDir", "options", "babelrc", "cloneInputAst", "undefined", "sourceFileName", "sourceMaps", "sourceMap", "baseCaller", "name", "supportsStaticESM", "supportsDynamicImport", "supportsTopLevelAwait", "isDev", "caller", "jsx", "allowNamespaces", "configFile", "hasJsxRuntime", "customConfig", "env", "overrides", "defineProperty", "enumerable", "writable", "value", "reason", "emitWarning", "loadedOptions", "loadOptions", "consumeIterator", "loadConfig", "get<PERSON><PERSON><PERSON><PERSON>", "flags", "config<PERSON><PERSON>", "Map", "configFiles", "Set", "addDependency", "cache<PERSON>ey", "has", "cachedConfig", "get", "cwd", "root", "add", "info", "freshConfig", "call", "set"], "mappings": ";;;;+BAsbA;;;eAAwBA;;;oBAtbK;8DACX;sBAE4B;sEACvB;sBAGS;6DACX;wEACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhB,MAAMC,eACJ;AA6BF,MAAMC,qBAAqB;AAC3B,SAASC,wBACPC,aAAqC,EACrCC,MAAc,EACdC,QAAgB;QAMAJ;IAJhB,MAAM,EAAEK,QAAQ,EAAEC,QAAQ,EAAE,GAAGJ;IAC/B,MAAMK,aAAaH,SAASI,UAAU,CAACF;IACvC,MAAMG,aAAaV,aAAaW,IAAI,CAACN;IACrC,MAAMO,mBAAmBR,OAAOS,OAAO,CAAC,sBAAsB,CAAC;IAC/D,MAAMC,UAAUb,EAAAA,2BAAAA,mBAAmBc,IAAI,CAACV,8BAAxBJ,wBAAmC,CAAC,EAAE,KAAI;IAE1D,OAAO;QACLK;QACAE;QACAE;QACAE;QACAE;IACF;AACF;AAEA;;;CAGC,GACD,SAASE,WACPb,aAAqC,EACrCc,oBAAqD;IAErD,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAE,GAC1DK;IAEF,MAAM,EAAEC,WAAW,EAAE,GAAGf;IACxB,MAAMgB,kBACJhB,cAAciB,aAAa,KAAK,eAC5BjB,cAAcgB,eAAe,GAC7B;IAEN,MAAME,oBAAoBT,mBACtBU,IAAAA,sBAAgB,EAACC,QAAQ,wBAAwB;QAAEC,MAAM;IAAS,KAClE;IACJ,MAAMC,mBAAmBN,kBACrBG,IAAAA,sBAAgB,EACd;QACEC,QAAQ;QACR;YAAEG,cAAc;QAAK;KACtB,EACD;QAAEF,MAAM;IAAS,KAEnB;IACJ,MAAMG,iBACJ,CAACrB,YAAYE,aACTc,IAAAA,sBAAgB,EAAC;QAACC,QAAQ;KAA+B,EAAE;QACzDC,MAAM;IACR,KACA;IACN,MAAMI,wBACJ,CAACtB,YAAYE,aACTc,IAAAA,sBAAgB,EACd;QAACC,QAAQ;KAAuD,EAChE;QAAEC,MAAM;IAAS,KAEnB;IACN,MAAMK,sBAAsBP,IAAAA,sBAAgB,EAC1C;QACEC,QAAQO,OAAO,CAAC;QAChB;YACE,wBAAwBZ,cAAc,gBAAgB;YACtD,iBAAiBZ,WAAW,cAAc;YAC1C,mBAAmBA,WAAW,QAAQ;QACxC;QACA;KACD,EACD;QAAEkB,MAAM;IAAS;IAEnB,MAAMO,cACJ,CAACzB,YAAYE,aACTc,IAAAA,sBAAgB,EAAC;QAACC,QAAQO,OAAO,CAAC;KAAiC,EAAE;QACnEN,MAAM;IACR,KACA;IACN,MAAMQ,eAAetB,aACjBY,IAAAA,sBAAgB,EACdC,QAAQ,+DACR;QAAEC,MAAM;IAAS,KAEnB;IACJ,MAAMS,sBAAsBX,IAAAA,sBAAgB,EAC1C;QAACC,QAAQ;KAAoC,EAC7C;QAAEC,MAAM;IAAS;IAGnB,OAAO;QACLC;QACAE;QACAC;QACAP;QACAQ;QACAE;QACAC;QACAC;KACD,CAACC,MAAM,CAACC;AACX;AAEA,MAAMC,aAAa;AACnB,MAAMC,WAAW;AAEjB;;;;;CAKC,GACD,SAASC,qBAAqBC,cAAsB;IAClD,IAAIH,WAAWrB,IAAI,CAACwB,iBAAiB;QACnC,MAAMC,iBAAiBC,IAAAA,gBAAY,EAACF,gBAAgB;QACpD,OAAOG,cAAK,CAACC,KAAK,CAACH;IACrB,OAAO,IAAIH,SAAStB,IAAI,CAACwB,iBAAiB;QACxC,OAAOhB,QAAQgB;IACjB;IACA,MAAM,qBAEL,CAFK,IAAIK,MACR,yEADI,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,IAAIC,oBAAoB;AACxB;;;;;CAKC,GACD,SAASC,kCACPC,MAAuC;IAEvC,IAAI,CAACA,UAAUC,OAAOC,IAAI,CAACF,QAAQG,MAAM,KAAK,GAAG;QAC/C;IACF;IAEA,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAE,GAAGC,cAAc,GAAGN;IAC9C,IAAIC,OAAOC,IAAI,CAACI,gBAAgB,CAAC,GAAGH,MAAM,GAAG,GAAG;QAC9C;IACF;IAEA,IAAIL,mBAAmB;QACrB;IACF;IAEAA,oBAAoB;IAEpB,MAAMS,2BACJ,CAACF,WACDA,QAAQF,MAAM,KAAK,KAClBE,QAAQF,MAAM,KAAK,KAAKE,OAAO,CAAC,EAAE,KAAK;IAC1C,MAAMG,gBAAgB,EAAE;IACxB,MAAMC,qBAAqB,EAAE;IAE7B,IAAIC,MAAMC,OAAO,CAACP,UAAU;QAC1B,KAAK,MAAMQ,UAAUR,QAAS;YAC5B,MAAMS,aAAaH,MAAMC,OAAO,CAACC,UAAUA,MAAM,CAAC,EAAE,GAAGA;YAEvD,wFAAwF;YACxF,6CAA6C;YAC7C,OAAQC;gBACN,KAAK;gBACL,KAAK;oBACHL,cAAcM,IAAI,CAChB,CAAC,0FAA0F,CAAC;oBAE9F;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qFAAqF,CAAC;oBAEzF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,gFAAgF,CAAC;oBAEpF;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,qGAAqG,CAAC;oBAEzG;gBACF,KAAK;oBACHN,cAAcM,IAAI,CAChB,CAAC,8FAA8F,CAAC;oBAElG;gBACF;oBACEL,mBAAmBK,IAAI,CAACD;oBACxB;YACJ;QACF;IACF;IAEA,IAAIN,4BAA4BE,mBAAmBN,MAAM,KAAK,GAAG;QAC/DY,KAAIC,IAAI,CACN,CAAC,uEAAuE,EACtER,cAAcL,MAAM,GAAG,IAAI,MAAM,KACjC;QAGJ,IAAIK,cAAcL,MAAM,GAAG,GAAG;YAC5BY,KAAIC,IAAI,CAAC,CAAC,kDAAkD,CAAC;YAC7DD,KAAIC,IAAI,CAACR,cAAcS,IAAI,CAAC;YAC5BF,KAAIC,IAAI,CACN,CAAC,4HAA4H,CAAC;QAElI;IACF;AACF;AAEA;;;CAGC,GACD,SAASE,eAEPhD,oBAAqD,EACrDd,aAAqC,EACrC+D,MAAc,EACd7D,QAAgB,EAChB8D,cAA8B;IAE9B,MAAMC,mBAAmB,AAAC,CAAA;QACxB,IACEjE,cAAckE,oBAAoB,IAClClE,cAAckE,oBAAoB,CAACnB,MAAM,KAAK,GAC9C;YACA,OAAO;QACT;QAEA,IAAI,yBAAyBvC,IAAI,CAACN,WAAW;YAC3C,OAAO;QACT;QAEA,IACEF,cAAcmE,oBAAoB,IAClCnE,cAAcmE,oBAAoB,CAACjE,WACnC;YACA,OAAO;QACT;QAEA,OAAO;IACT,CAAA;IAEA,MAAMkE,gCAAgCH,mBAClCjE,cAAckE,oBAAoB,IAAI,EAAE,GACxC,EAAE;IAEN,IAAI,EAAE/D,QAAQ,EAAEC,QAAQ,EAAEiE,MAAM,EAAEtD,WAAW,EAAE,GAAGf;IAElD,IAAIsE,UAAU;QACZC,SAAS;QACTC,eAAe;QACftE;QACA8D,gBAAgBA,kBAAkBS;QAElC,qEAAqE;QACrE,qEAAqE;QACrE,WAAW;QACXC,gBAAgBxE;QAChByE,YAAY,IAAI,CAACC,SAAS;IAC5B;IAEA,MAAMC,aAAa;QACjBC,MAAM;QACNC,mBAAmB;QACnBC,uBAAuB;QAEvB,oDAAoD;QACpD,mDAAmD;QACnDjB,QAAQA;QAER,gEAAgE;QAChE,sEAAsE;QACtE,sBAAsB;QACtBkB,uBAAuB;QAEvB9E;QACAkE;QACAjE;QACA8E,OAAOnE;QAEP,GAAGf,cAAcmF,MAAM;IACzB;IAEA,IAAInF,cAAciB,aAAa,KAAK,cAAc;QAChD,IAAI,CAACmD,8BAA8BrB,MAAM,EAAE;YACzC,OAAO;QACT;QAEAuB,QAAQtB,OAAO,GAAG;YAACoC,wBAAG;eAAKhB;SAA8B;QACzDE,QAAQrB,OAAO,GAAG;YAChB;gBACE7B,QAAQ;gBACR;oBAAEiE,iBAAiB;gBAAK;aACzB;SACF;QACDf,QAAQa,MAAM,GAAGN;IACnB,OAAO;QACL,IAAI,EAAES,UAAU,EAAEC,aAAa,EAAE,GAAGvF;QACpC,IAAIwF,eAAoBF,aACpBnD,qBAAqBmD,cACrBb;QAEJ9B,kCAAkC6C;QAElC,sEAAsE;QACtE,4CAA4C;QAC5ClB,QAAQK,UAAU,GAChB3E,cAAc2E,UAAU,KAAKF,YACzB,IAAI,CAACG,SAAS,GACd5E,cAAc2E,UAAU;QAE9BL,QAAQtB,OAAO,GAAG;eACbnC,WAAWb,eAAec;eAC1BsD;eACCoB,CAAAA,gCAAAA,aAAcxC,OAAO,KAAI,EAAE;SAChC;QAED,oCAAoC;QACpCsB,QAAQP,MAAM,GAAG5D,WAAWsE,YAAYe,gCAAAA,aAAczB,MAAM;QAE5D,iCAAiC;QACjCO,QAAQmB,GAAG,GAAGD,gCAAAA,aAAcC,GAAG;QAE/BnB,QAAQrB,OAAO,GAAG,AAAC,CAAA;YACjB,uEAAuE;YACvE,IAAIuC,gCAAAA,aAAcvC,OAAO,EAAE;gBACzB,OAAOuC,aAAavC,OAAO;YAC7B;YAEA,6EAA6E;YAC7E,IAAIuC,cAAc;gBAChB,OAAOf;YACT;YAEA,mEAAmE;YACnE,OAAO;gBAAC;aAAa;QACvB,CAAA;QAEAH,QAAQoB,SAAS,GAAG1F,cAAc0F,SAAS;QAE3CpB,QAAQa,MAAM,GAAG;YACf,GAAGN,UAAU;YACbU;QACF;IACF;IAEA,qEAAqE;IACrE,IAAI,OAAOjB,QAAQP,MAAM,KAAK,aAAa;QACzC,OAAOO,QAAQP,MAAM;IACvB;IAEAlB,OAAO8C,cAAc,CAACrB,QAAQa,MAAM,EAAE,aAAa;QACjDS,YAAY;QACZC,UAAU;QACVC,OAAO,CAACC;YACN,IAAI,CAAEA,CAAAA,kBAAkBtD,KAAI,GAAI;gBAC9BsD,SAAS,qBAAiB,CAAjB,IAAItD,MAAMsD,SAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAAgB;YAC3B;YACA,IAAI,CAACC,WAAW,CAACD;QACnB;IACF;IAEA,MAAME,gBAAgBC,IAAAA,iBAAW,EAAC5B;IAClC,MAAM1B,SAASuD,IAAAA,qBAAe,EAACC,IAAAA,sBAAU,EAACH;IAE1C,OAAOrD;AACT;AAEA;;;;CAIC,GACD,SAASyD,YAAYvF,oBAAqD;IACxE,MAAM,EAAEX,QAAQ,EAAEE,UAAU,EAAEE,UAAU,EAAEE,gBAAgB,EAAEE,OAAO,EAAE,GACnEG;IAEF,MAAMwF,QACJ,IACCnG,CAAAA,WAAW,IAAS,CAAA,IACpBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,aAAa,IAAS,CAAA,IACtBE,CAAAA,mBAAmB,IAAS,CAAA;IAE/B,OAAOE,UAAU2F;AACnB;AAGA,MAAMC,cAAqC,IAAIC;AAC/C,MAAMC,cAA2B,IAAIC;AAEtB,SAAS9G,UAEtB,EACEK,MAAM,EACN8D,MAAM,EACN/D,aAAa,EACbE,QAAQ,EACR8D,cAAc,EAOf;IAED,MAAMlD,uBAAuBf,wBAC3BC,eACAC,QACAC;IAGF,IAAIF,cAAciB,aAAa,KAAK,aAAajB,cAAcsF,UAAU,EAAE;QACzE,qFAAqF;QACrF,IAAI,CAACqB,aAAa,CAAC3G,cAAcsF,UAAU;IAC7C;IAEA,MAAMsB,WAAWP,YAAYvF;IAC7B,IAAIyF,YAAYM,GAAG,CAACD,WAAW;QAC7B,MAAME,eAAeP,YAAYQ,GAAG,CAACH;QACrC,IAAI,CAACE,cAAc;YACjB,OAAO;QACT;QAEA,OAAO;YACL,GAAGA,YAAY;YACfxC,SAAS;gBACP,GAAGwC,aAAaxC,OAAO;gBACvB0C,KAAKhH,cAAcgH,GAAG;gBACtBC,MAAMjH,cAAcgH,GAAG;gBACvB9G;gBACAwE,gBAAgBxE;YAClB;QACF;IACF;IAEA,IACEF,cAAciB,aAAa,KAAK,aAChCjB,cAAcsF,UAAU,IACxB,CAACmB,YAAYI,GAAG,CAAC7G,cAAcsF,UAAU,GACzC;QACAmB,YAAYS,GAAG,CAAClH,cAAcsF,UAAU;QACxC3B,KAAIwD,IAAI,CACN,CAAC,wCAAwC,EAAEnH,cAAcsF,UAAU,EAAE;IAEzE;IAEA,MAAM8B,cAActD,eAAeuD,IAAI,CACrC,IAAI,EACJvG,sBACAd,eACA+D,QACA7D,UACA8D;IAGFuC,YAAYe,GAAG,CAACV,UAAUQ;IAE1B,OAAOA;AACT"}