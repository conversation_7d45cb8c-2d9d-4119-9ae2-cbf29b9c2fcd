@echo off
chcp 65001 > nul
setlocal EnableDelayedExpansion

REM 星图超级接口线上化 - Docker启动脚本 (Windows)
REM 使用修复后的配置启动服务

echo 🚀 启动星图超级接口线上化...
echo.

REM 检查.env文件是否存在
if not exist ".env" (
    echo ❌ .env文件不存在，请先配置环境变量
    pause
    exit /b 1
)

REM 创建必要的数据目录
if not exist "data\logs" mkdir data\logs
if not exist "data\exports" mkdir data\exports

REM 构建并启动服务
echo 🔨 构建Docker镜像...
docker-compose build xingtu-api

echo 🌐 启动服务...
docker-compose up -d xingtu-api

REM 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak > nul

REM 检查健康状态
echo 🏥 检查服务健康状态...
docker exec xingtu-super-api curl -f -s http://localhost:8080/health > nul 2>&1
if !errorlevel! equ 0 (
    echo ✅ 服务启动成功！
    echo 🌐 API地址: http://localhost:8080
    echo 📊 API文档: http://localhost:8080/docs
    echo 🏥 健康检查: http://localhost:8080/health
) else (
    echo ❌ 服务启动失败，请检查日志:
    docker logs xingtu-super-api
)

echo.
echo 📋 查看运行状态: docker-compose ps
echo 📝 查看日志: docker-compose logs -f xingtu-api
echo 🛑 停止服务: docker-compose down

pause 