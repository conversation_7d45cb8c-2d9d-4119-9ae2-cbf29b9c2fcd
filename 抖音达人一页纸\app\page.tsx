"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Users, TrendingUp, MapPin, Star, Award, Target,
  Search, ExternalLink, ArrowRight, Globe,
  CheckCircle, AlertCircle, BarChart3, PieChart,
  Video, Briefcase, Eye, ThumbsUp, Loader2
} from "lucide-react"

import { dataService, type InfluencerData } from "@/lib/data-service"

interface TalentPreview {
  id: string
  name: string
  avatar: string
  followers: string
  location: string
  tags: string[]
  score: string
}

export default function HomePage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [availableTalents, setAvailableTalents] = useState<TalentPreview[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load available talents on component mount
  useEffect(() => {
    const loadTalents = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const talentIds = dataService.getAvailableInfluencers()
        const talentPreviews: TalentPreview[] = []

        for (const id of talentIds) {
          const response = await dataService.getInfluencerById(id)
          if (response.success && response.data) {
            const data = response.data
            talentPreviews.push({
              id,
              name: data.nick_name || '未知达人',
              avatar: data.avatar_uri || '/placeholder.svg',
              followers: data.computed?.followerDisplay || '0',
              location: data.city || '未知',
              tags: data.tags ? JSON.parse(data.tags) : [],
              score: data.computed?.scoreDisplay || '8.0'
            })
          }
        }

        setAvailableTalents(talentPreviews)
      } catch (err) {
        console.error('Error loading talents:', err)
        setError('加载达人列表失败')
      } finally {
        setIsLoading(false)
      }
    }

    loadTalents()
  }, [])

  // Filter talents based on search term
  const filteredTalents = availableTalents.filter(talent =>
    talent.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    talent.id.includes(searchTerm) ||
    talent.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    talent.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  // Handle talent selection
  const handleTalentClick = (talentId: string) => {
    router.push(`/${talentId}`)
  }

  // Handle direct ID input
  const handleDirectAccess = () => {
    if (searchTerm.trim()) {
      router.push(`/${searchTerm.trim()}`)
    }
  }

  return (
    <div className="min-h-screen bg-[#FDFCFA]">
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-[#1A1A1A] mb-4">达人一页纸线上版</h1>
          <p className="text-lg text-[#6B6967] mb-8">
            专业的达人数据分析平台，为品牌方提供全面的达人画像和合作决策支持
          </p>

          {/* Search Section */}
          <div className="max-w-2xl mx-auto mb-8">
            <div className="flex gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#6B6967] w-5 h-5" />
                <Input
                  placeholder="搜索达人姓名、ID或标签..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 border-[#F0EFEB] focus:border-[#CC5500] focus:ring-[#CC5500]"
                  onKeyPress={(e) => e.key === 'Enter' && handleDirectAccess()}
                />
              </div>
              <Button
                onClick={handleDirectAccess}
                className="h-12 px-6 bg-[#CC5500] hover:bg-[#B84C00] text-white"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                直接访问
              </Button>
            </div>
            <p className="text-sm text-[#9B9894] mt-2">
              输入达人ID可直接访问，例如：7119367979820646432
            </p>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <Loader2 className="w-12 h-12 animate-spin text-[#CC5500] mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-[#1A1A1A] mb-2">加载中...</h2>
            <p className="text-[#6B6967]">正在获取可用达人列表</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="max-w-md mx-auto">
            <Alert className="border-[#DC2626] bg-[#FEF2F2]">
              <AlertCircle className="h-4 w-4 text-[#DC2626]" />
              <AlertDescription className="text-[#DC2626]">
                {error}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Talents Grid */}
        {!isLoading && !error && (
          <>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-[#1A1A1A]">
                可用达人 ({filteredTalents.length})
              </h2>
              {searchTerm && (
                <Badge className="bg-[#F8F7F4] text-[#6B6967]">
                  搜索: "{searchTerm}"
                </Badge>
              )}
            </div>

            {filteredTalents.length === 0 ? (
              <div className="text-center py-12">
                <Users className="w-16 h-16 text-[#9B9894] mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-[#1A1A1A] mb-2">
                  {searchTerm ? '未找到匹配的达人' : '暂无可用达人'}
                </h3>
                <p className="text-[#6B6967] mb-4">
                  {searchTerm ? '请尝试其他搜索关键词' : '请稍后再试或联系管理员'}
                </p>
                {searchTerm && (
                  <Button
                    onClick={() => setSearchTerm('')}
                    variant="outline"
                    className="border-[#F0EFEB] hover:bg-[#F8F7F4]"
                  >
                    清除搜索
                  </Button>
                )}
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTalents.map((talent) => (
                  <Card
                    key={talent.id}
                    className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl overflow-hidden cursor-pointer group hover:shadow-md transition-all duration-300"
                    onClick={() => handleTalentClick(talent.id)}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4 mb-4">
                        <img
                          src={talent.avatar}
                          alt={talent.name}
                          className="w-16 h-16 rounded-xl object-cover border-2 border-white shadow-sm"
                        />
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-[#1A1A1A] text-lg mb-1 truncate">
                            {talent.name}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-[#6B6967] mb-2">
                            <MapPin className="w-3 h-3" />
                            <span>{talent.location}</span>
                          </div>
                          <div className="flex items-center gap-2 text-sm text-[#6B6967]">
                            <Users className="w-3 h-3" />
                            <span>{talent.followers} 粉丝</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-1 text-sm">
                          <Star className="w-4 h-4 text-[#D97706]" />
                          <span className="font-semibold text-[#1A1A1A]">{talent.score}</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2 mb-4">
                        {talent.tags.slice(0, 3).map((tag) => (
                          <Badge
                            key={tag}
                            className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0] text-xs"
                          >
                            {tag}
                          </Badge>
                        ))}
                        {talent.tags.length > 3 && (
                          <Badge className="bg-[#F8F7F4] text-[#9B9894] text-xs">
                            +{talent.tags.length - 3}
                          </Badge>
                        )}
                      </div>

                      <div className="flex items-center justify-between">
                        <Badge className="bg-[#F8F7F4] text-[#6B6967] text-xs font-mono">
                          ID: {talent.id}
                        </Badge>
                        <div className="flex items-center text-[#CC5500] text-sm font-medium group-hover:text-[#B84C00] transition-colors">
                          查看详情
                          <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}

        {/* Footer */}
        <div className="mt-16 pt-8 border-t border-[#F0EFEB] text-center">
          <div className="flex items-center justify-center gap-2 text-[#6B6967] mb-4">
            <Globe className="w-5 h-5" />
            <span>达人一页纸线上版 - 专业达人数据分析平台</span>
          </div>
          <p className="text-sm text-[#9B9894]">
            URL格式: domain.com/达人ID | 示例: domain.com/7119367979820646432
          </p>
        </div>
      </div>
    </div>
  )
}
    // 基础信息
    name: influencerData.nick_name,
    avatar: influencerData.avatar_uri,
    followers: Math.round(influencerData.follower / 10000 * 10) / 10 + "万",
    location: influencerData.city,
    mcn: influencerData.mcn_name,
    selfIntro: influencerData.self_intro,

    // 核心数据指标
    metrics: {
      avgViews: Math.round(parseInt(influencerData.vv) / 10000) + "万",
      avgA3IncrCnt: influencerData.avg_a3_incr_cnt,
      avgA3IncrCntRank: Math.round(parseFloat(influencerData.avg_a3_incr_cnt_rank_percent) * 100) + "%",
      platformHotRate: influencerData.platform_hot_rate,
      messageReplyRate: Math.round(parseFloat(influencerData.message_reply_rate) * 100) + "%",
      fansGrowthRate30d: Math.round(parseFloat(influencerData.fans_growth_rate_30d) * 100 * 10) / 10 + "%",

      // CPM数据
      cpm1_20: "¥" + influencerData.cpm_1_20,
      cpm20_60: "¥" + influencerData.cpm_20_60,
      cpm60: "¥" + influencerData.cpm_60,
      cpm20_60_rank: Math.round((1 - parseFloat(influencerData.cpm_20_60_rank_percent)) * 100) + "%",

      // CPE数据
      cpe1_20: influencerData.cpe_1_20,
      cpe20_60: influencerData.cpe_20_60,
      cpe60: influencerData.cpe_60,

      // 订单数据
      orderCnt: influencerData.order_cnt,
      processOrderCnt: influencerData.process_order_cnt,
      hasOrder30d: influencerData.has_order_30d,
      hasOrder90d: influencerData.has_order_90d,

      // 计算平均互动率和其他指标
      avgLikes: Math.round(influencerData.items.reduce((sum: number, item: any) => sum + parseInt(item.like_cnt), 0) / influencerData.items.length / 10000) + "万",
      avgComments: Math.round(influencerData.items.reduce((sum: number, item: any) => sum + parseInt(item.comment_cnt), 0) / influencerData.items.length),
      interactionRate: Math.round(influencerData.items.reduce((sum: number, item: any) => sum + item.interact_rate, 0) / influencerData.items.length * 100 * 100) / 100 + "%",
    },

    // 综合评分（基于多个维度计算）
    score: "8.6",

    // 粉丝画像（模拟数据，实际项目中应从真实数据源获取）
    audience: {
      gender: { male: 67, female: 33 },
      age: {
        "18-23": 49,
        "24-30": 28,
        "31-40": 16,
        "41-50": 3,
        "50+": 4,
      },
      cities: [
        { name: "山东", percent: 10 },
        { name: "河北", percent: 7 },
        { name: "河南", percent: 7 },
        { name: "江苏", percent: 6 },
        { name: "辽宁", percent: 5 },
      ],
    },

    // 内容主题分析
    contentThemes: Object.entries(influencerData.content_theme_label_map).map(([name, data]: [string, any]) => ({
      name,
      count: parseInt(data.label_item_count),
      percentage: Math.round((parseInt(data.label_item_count) / parseInt(influencerData.all_content_theme_label_item_cnt)) * 100)
    })).sort((a, b) => b.count - a.count),

    // 行业合作经验
    industryExperience: {
      orderDistribution: parseIndustryData(influencerData.industry_order_distribution),
      vvDistribution: parseIndustryData(influencerData.industry_vv_distribution),
      experienceIndustries: influencerData.order_experience_industries
    },

    // 最新作品数据
    latestVideos: influencerData.items.slice(0, 6).map((item: any) => ({
      id: item.id,
      title: item.title,
      coverUri: item.cover_uri,
      watchCnt: Math.round(parseInt(item.stats.watch_cnt) / 10000) + "万",
      likeCnt: Math.round(parseInt(item.stats.like_cnt) / 10000) + "万",
      commentCnt: item.stats.comment_cnt,
      duration: Math.round(item.duration),
      interactRate: Math.round(item.stats.interact_rate * 100 * 100) / 100 + "%",
      createTime: new Date(parseInt(item.create_time) * 1000).toLocaleDateString(),
      url: item.url,
      embedUrl: `https://www.douyin.com/video/${item.id}`,
      contentThemeLabels: item.content_theme_labels || []
    })),

    // 价格体系
    pricing: influencerData.price_info.filter((price: any) => price.enable).map((price: any) => ({
      type: price.desc,
      price: "¥" + Math.round(price.price / 10000) + "万",
      originalPrice: price.origin_price,
      settlementType: price.settlement_type,
      settlementDesc: price.settlement_desc,
      videoType: price.video_type,
      isOpen: price.is_open,
      doFold: price.do_fold,
      priceExtraInfo: price.price_extra_info
    })),

    // 热榜排名
    hotListRanks: influencerData.hot_list_ranks,

    // 行业标签
    industryTags: influencerData.industry_tags,

    // 标签关系
    tags: influencerData.tags ? JSON.parse(influencerData.tags) : [],
    tagsLevelTwo: influencerData.tags_level_two ? JSON.parse(influencerData.tags_level_two) : [],
  }

  return (
    <div className="min-h-screen bg-[#FDFCFA]">
      <div className="container mx-auto px-6 py-8 max-w-7xl">
        {/* 头部信息 */}
        <div className="bg-white rounded-2xl shadow-sm border border-[#F0EFEB] p-8 mb-8">
          <div className="flex items-start gap-8">
            <div className="relative">
              <img
                src={kolData.avatar || "/placeholder.svg"}
                alt={kolData.name}
                className="w-32 h-32 rounded-2xl object-cover border-4 border-white shadow-md"
              />
              <div className="absolute -bottom-2 -right-2 bg-[#059669] text-white text-xs font-medium px-3 py-1 rounded-full shadow-sm">
                活跃
              </div>
            </div>

            <div className="flex-1">
              <div className="flex items-center gap-4 mb-3">
                <h1 className="text-3xl font-normal text-[#1A1A1A]">{kolData.name}</h1>
                {kolData.tags.length > 0 && (
                  <Badge className="bg-[#CC5500] text-white hover:bg-[#B84C00]">{kolData.tags[0]}</Badge>
                )}
                <Badge className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0]">MCN: {kolData.mcn}</Badge>
              </div>

              <div className="flex items-center gap-6 text-[#6B6967] mb-4">
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>{kolData.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Users className="w-4 h-4" />
                  <span>{kolData.followers} 粉丝</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4 text-[#D97706]" />
                  <span>综合评分 {kolData.score}</span>
                </div>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-[#059669]" />
                  <span>回复率 {kolData.metrics.messageReplyRate}</span>
                </div>
              </div>

              {/* 自我介绍 */}
              <div className="bg-[#F8F7F4] rounded-lg p-4 mb-6">
                <p className="text-sm text-[#6B6967] leading-relaxed">{kolData.selfIntro}</p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">平均播放量</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.avgViews}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">平均点赞数</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.avgLikes}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">互动率</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.interactionRate}</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">CPM (20-60s)</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.cpm20_60}</div>
                  <div className="text-xs text-[#059669] mt-1">超越 {kolData.metrics.cpm20_60_rank} 达人</div>
                </div>
                <div className="bg-[#F8F7F4] rounded-xl p-4">
                  <div className="text-[#9B9894] text-sm mb-1">30天粉丝增长</div>
                  <div className="text-xl font-semibold text-[#1A1A1A]">{kolData.metrics.fansGrowthRate30d}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 标签页内容 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-6 bg-white rounded-xl p-1 shadow-sm border border-[#F0EFEB] mb-8">
            <TabsTrigger
              value="overview"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              数据概览
            </TabsTrigger>
            <TabsTrigger
              value="content"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              内容分析
            </TabsTrigger>
            <TabsTrigger
              value="videos"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              最新作品
            </TabsTrigger>
            <TabsTrigger
              value="industry"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              行业经验
            </TabsTrigger>
            <TabsTrigger
              value="audience"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              粉丝画像
            </TabsTrigger>
            <TabsTrigger
              value="pricing"
              className="rounded-lg data-[state=active]:bg-[#CC5500] data-[state=active]:text-white"
            >
              合作报价
            </TabsTrigger>
          </TabsList>

          {/* 数据概览 */}
          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <TrendingUp className="w-5 h-5 text-[#CC5500]" />
                    核心数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均播放量</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.avgViews}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均互动率</span>
                    <span className="font-semibold text-[#059669]">{kolData.metrics.interactionRate}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均点赞数</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.avgLikes}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">平均评论数</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.avgComments}</span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <DollarSign className="w-5 h-5 text-[#CC5500]" />
                    CPM数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">1-20s CPM</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.cpm1_20}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">20-60s CPM</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.cpm20_60}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">60s+ CPM</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.cpm60}</span>
                  </div>
                  <div className="pt-2">
                    <div className="text-[#6B6967] text-sm mb-2">CPM排名</div>
                    <Progress value={parseInt(kolData.metrics.cpm20_60_rank)} className="h-2" />
                    <div className="text-xs text-[#9B9894] mt-1">超过 {kolData.metrics.cpm20_60_rank} 达人</div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Briefcase className="w-5 h-5 text-[#CC5500]" />
                    商业数据
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">历史订单</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.orderCnt}单</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">进行中订单</span>
                    <span className="font-semibold text-[#1A1A1A]">{kolData.metrics.processOrderCnt}单</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">消息回复率</span>
                    <span className="font-semibold text-[#059669]">{kolData.metrics.messageReplyRate}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-[#6B6967]">30天有订单</span>
                    <span className={`font-semibold ${kolData.metrics.hasOrder30d ? 'text-[#059669]' : 'text-[#DC2626]'}`}>
                      {kolData.metrics.hasOrder30d ? '是' : '否'}
                    </span>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Award className="w-5 h-5 text-[#CC5500]" />
                    综合评分
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-center py-4">
                    <div className="relative w-24 h-24">
                      <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="35" fill="none" stroke="#F0EFEB" strokeWidth="6" />
                        <circle
                          cx="50"
                          cy="50"
                          r="35"
                          fill="none"
                          stroke="#CC5500"
                          strokeWidth="6"
                          strokeDasharray="219.8"
                          strokeDashoffset="43.96"
                          strokeLinecap="round"
                        />
                      </svg>
                      <div className="absolute inset-0 flex flex-col items-center justify-center">
                        <div className="text-2xl font-bold text-[#1A1A1A]">{kolData.score}</div>
                        <div className="text-xs text-[#6B6967]">优秀</div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-[#6B6967]">粉丝增长</span>
                      <span className="text-[#1A1A1A]">{kolData.metrics.fansGrowthRate30d}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-[#6B6967]">平台热度</span>
                      <span className="text-[#1A1A1A]">{kolData.metrics.platformHotRate}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* CPE数据对比 */}
            <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl mb-6">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                  <BarChart3 className="w-5 h-5 text-[#CC5500]" />
                  CPE效果数据对比
                </CardTitle>
                <CardDescription>与星图平均水平对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center">
                    <div className="text-sm text-[#6B6967] mb-2">1-20s CPE</div>
                    <div className="text-2xl font-bold text-[#1A1A1A] mb-1">{kolData.metrics.cpe1_20}</div>
                    <div className="text-xs text-[#6B6967]">星图平均: {influencerData.sn_cpe_1_20}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-[#6B6967] mb-2">20-60s CPE</div>
                    <div className="text-2xl font-bold text-[#1A1A1A] mb-1">{kolData.metrics.cpe20_60}</div>
                    <div className="text-xs text-[#6B6967]">星图平均: {influencerData.sn_cpe_20_60}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-sm text-[#6B6967] mb-2">60s+ CPE</div>
                    <div className="text-2xl font-bold text-[#1A1A1A] mb-1">{kolData.metrics.cpe60}</div>
                    <div className="text-xs text-[#6B6967]">星图平均: {influencerData.sn_cpe_60}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 投放建议 */}
            <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                  <Target className="w-5 h-5 text-[#CC5500]" />
                  投放建议与分析
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-[#F8F7F4] rounded-lg p-4">
                    <div className="text-sm font-medium text-[#1A1A1A] mb-2">推荐合作类型</div>
                    <div className="text-sm text-[#6B6967] mb-2">剧情植入、产品测评、品牌故事</div>
                    <div className="text-xs text-[#9B9894]">基于内容主题分析</div>
                  </div>
                  <div className="bg-[#F8F7F4] rounded-lg p-4">
                    <div className="text-sm font-medium text-[#1A1A1A] mb-2">优势特点</div>
                    <div className="text-sm text-[#6B6967] mb-2">一人分饰多角，剧情反转能力强</div>
                    <div className="text-xs text-[#9B9894]">适合创意性品牌推广</div>
                  </div>
                  <div className="bg-[#F8F7F4] rounded-lg p-4">
                    <div className="text-sm font-medium text-[#1A1A1A] mb-2">合作建议</div>
                    <div className="text-sm text-[#6B6967] mb-2">建议选择20-60s视频时长</div>
                    <div className="text-xs text-[#9B9894]">性价比最优，效果最佳</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 行业经验 */}
          <TabsContent value="industry">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Briefcase className="w-5 h-5 text-[#CC5500]" />
                    订单行业分布
                  </CardTitle>
                  <CardDescription>共 {kolData.metrics.orderCnt} 个订单</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {kolData.industryExperience.orderDistribution.map((industry: any) => {
                      const industryNames: { [key: string]: string } = {
                        "1907": "3C及电器",
                        "1913": "美妆",
                        "1901": "零售",
                        "1916": "汽车",
                        "1914": "3C数码家电",
                        "1930": "其他"
                      }
                      return (
                        <div key={industry.key}>
                          <div className="flex justify-between text-sm mb-2">
                            <span className="text-[#6B6967]">{industryNames[industry.key] || `行业${industry.key}`}</span>
                            <span className="font-medium text-[#1A1A1A]">{industry.value}单 ({Math.round(parseFloat(industry.rate) * 100)}%)</span>
                          </div>
                          <Progress value={parseFloat(industry.rate) * 100} className="h-2" />
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Eye className="w-5 h-5 text-[#CC5500]" />
                    播放量行业分布
                  </CardTitle>
                  <CardDescription>总播放量分布情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {kolData.industryExperience.vvDistribution.map((industry: any) => {
                      const industryNames: { [key: string]: string } = {
                        "1907": "3C及电器",
                        "1913": "美妆",
                        "1901": "零售",
                        "1916": "汽车",
                        "1914": "3C数码家电",
                        "1930": "其他"
                      }
                      return (
                        <div key={industry.key}>
                          <div className="flex justify-between text-sm mb-2">
                            <span className="text-[#6B6967]">{industryNames[industry.key] || `行业${industry.key}`}</span>
                            <span className="font-medium text-[#1A1A1A]">{Math.round(parseInt(industry.value) / 10000)}万 ({Math.round(parseFloat(industry.rate) * 100)}%)</span>
                          </div>
                          <Progress value={parseFloat(industry.rate) * 100} className="h-2" />
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 详细行业经验 */}
            <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                  <Target className="w-5 h-5 text-[#CC5500]" />
                  详细行业合作经验
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {kolData.industryExperience.experienceIndustries.map((exp: any, index: number) => (
                    <div key={index} className="bg-[#F8F7F4] rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-[#1A1A1A]">{exp.industry.name}</h4>
                        <Badge className="bg-[#CC5500] text-white text-xs">
                          {Math.round(exp.industry.rate * 100)}%
                        </Badge>
                      </div>

                      <div className="space-y-2">
                        <div>
                          <div className="text-xs text-[#6B6967] mb-1">主要类目</div>
                          {exp.first_category.map((cat: any, catIndex: number) => (
                            <div key={catIndex} className="text-sm text-[#1A1A1A] mb-1">
                              {cat.name} ({Math.round(cat.rate * 100)}%)
                            </div>
                          ))}
                        </div>

                        {exp.second_category.length > 0 && (
                          <div>
                            <div className="text-xs text-[#6B6967] mb-1">细分类目</div>
                            {exp.second_category.map((cat: any, catIndex: number) => (
                              <div key={catIndex} className="text-sm text-[#1A1A1A]">
                                {cat.name} ({Math.round(cat.rate * 100)}%)
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 粉丝画像 */}
          <TabsContent value="audience">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A]">性别分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-[#6B6967]">男性</span>
                      <span className="font-semibold text-[#1A1A1A]">{kolData.audience.gender.male}%</span>
                    </div>
                    <Progress value={kolData.audience.gender.male} className="h-3" />
                    <div className="flex items-center justify-between">
                      <span className="text-[#6B6967]">女性</span>
                      <span className="font-semibold text-[#1A1A1A]">{kolData.audience.gender.female}%</span>
                    </div>
                    <Progress value={kolData.audience.gender.female} className="h-3" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A]">年龄分布</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(kolData.audience.age).map(([age, percent]) => (
                      <div key={age}>
                        <div className="flex justify-between text-sm mb-1">
                          <span className="text-[#6B6967]">{age}岁</span>
                          <span className="font-medium text-[#1A1A1A]">{percent}%</span>
                        </div>
                        <Progress value={percent} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="md:col-span-2 bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A]">地域分布 TOP5</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    {kolData.audience.cities.map((city) => (
                      <div key={city.name} className="text-center">
                        <div className="bg-[#F8F7F4] rounded-lg p-4 mb-2">
                          <div className="text-2xl font-bold text-[#CC5500] mb-1">{city.percent}%</div>
                          <div className="text-sm text-[#6B6967]">{city.name}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 内容分析 */}
          <TabsContent value="content">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <PieChart className="w-5 h-5 text-[#CC5500]" />
                    内容主题分布
                  </CardTitle>
                  <CardDescription>共 {influencerData.all_content_theme_label_item_cnt} 个作品</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {kolData.contentThemes.slice(0, 8).map((theme) => (
                      <div key={theme.name}>
                        <div className="flex justify-between text-sm mb-2">
                          <span className="text-[#6B6967]">{theme.name}</span>
                          <span className="font-medium text-[#1A1A1A]">{theme.count}个 ({theme.percentage}%)</span>
                        </div>
                        <Progress value={theme.percentage} className="h-2" />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                    <Award className="w-5 h-5 text-[#CC5500]" />
                    内容特色与标签
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-[#1A1A1A] mb-3">主要标签</h4>
                      <div className="flex flex-wrap gap-2 mb-4">
                        {kolData.tags.map((tag: string) => (
                          <Badge key={tag} className="bg-[#CC5500] text-white hover:bg-[#B84C00]">
                            {tag}
                          </Badge>
                        ))}
                        {kolData.tagsLevelTwo.map((tag: string) => (
                          <Badge key={tag} className="bg-[#F8F7F4] text-[#6B6967] hover:bg-[#F5F3F0]">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="bg-[#F8F7F4] rounded-lg p-4">
                      <h4 className="font-medium text-[#1A1A1A] mb-2">适合行业</h4>
                      <div className="flex flex-wrap gap-2">
                        {kolData.industryTags.map((tag: string) => (
                          <Badge key={tag} className="bg-white text-[#6B6967] border border-[#E5E3DF] text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {kolData.hotListRanks.length > 0 && (
                      <div className="bg-[#F8F7F4] rounded-lg p-4">
                        <h4 className="font-medium text-[#1A1A1A] mb-2">榜单排名</h4>
                        {kolData.hotListRanks.map((rank: any) => (
                          <div key={rank.industry_id} className="text-sm text-[#6B6967] mb-1">
                            <span className="font-medium text-[#CC5500]">{rank.hot_list_name}</span> - {rank.industry_name}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 最新作品 */}
          <TabsContent value="videos">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {kolData.latestVideos.map((video: any) => (
                <Card 
                  key={video.id} 
                  className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl overflow-hidden cursor-pointer group"
                  onClick={() => setSelectedVideo(video)}
                >
                  <div className="relative">
                    <img
                      src={formatImageUrl(video.coverUri)}
                      alt={video.title}
                      className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                      <PlayCircle className="w-16 h-16 text-white opacity-70 transition-opacity duration-300 group-hover:opacity-90" />
                    </div>
                    <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                      {formatDuration(video.duration)}
                    </div>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-medium text-[#1A1A1A] mb-2 line-clamp-2 text-sm leading-relaxed">
                      {video.title}
                    </h3>
                    <div className="text-xs text-[#6B6967] mb-3">{video.createTime}</div>

                    <div className="grid grid-cols-2 gap-3 text-xs">
                      <div className="flex items-center gap-1">
                        <Eye className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.watchCnt}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <ThumbsUp className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.likeCnt}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <MessageCircle className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.commentCnt}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <BarChart3 className="w-3 h-3 text-[#6B6967]" />
                        <span className="text-[#6B6967]">{video.interactRate}</span>
                      </div>
                    </div>

                    {video.contentThemeLabels.length > 0 && (
                      <div className="mt-3 flex flex-wrap gap-1">
                        {video.contentThemeLabels.slice(0, 2).map((label: string) => (
                          <Badge key={label} className="bg-[#F8F7F4] text-[#6B6967] text-xs px-2 py-0.5">
                            {label}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* 合作报价 */}
          <TabsContent value="pricing">
            <Card className="bg-white border-[#F0EFEB] shadow-sm rounded-2xl">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-[#1A1A1A] flex items-center gap-2">
                  <DollarSign className="w-5 h-5 text-[#CC5500]" />
                  合作报价
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow className="border-none">
                      <TableHead className="w-2/5 border-b border-[#F0EFEB] text-[#6B6967] font-normal">合作类型</TableHead>
                      <TableHead className="text-center border-b border-[#F0EFEB] text-[#6B6967] font-normal">价格</TableHead>
                      <TableHead className="text-center border-b border-[#F0EFEB] text-[#6B6967] font-normal">结算方式</TableHead>
                      <TableHead className="text-right border-b border-[#F0EFEB] text-[#6B6967] font-normal">备注</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {kolData.pricing.map((item) => (
                      <TableRow key={item.type} className="border-b-0">
                        <TableCell className="py-3 font-medium text-[#1A1A1A]">{item.type}</TableCell>
                        <TableCell className="py-3 text-center font-semibold text-[#CC5500]">{item.price}</TableCell>
                        <TableCell className="py-3 text-center text-[#6B6967]">{item.settlementDesc}</TableCell>
                        <TableCell className="py-3 text-right text-xs text-[#6B6967]">
                          {item.priceExtraInfo?.floor_price && (
                            <div>保底 ¥{Math.round(parseInt(item.priceExtraInfo.floor_price) / 10000)}万</div>
                          )}
                          {item.priceExtraInfo?.ceiling_price && (
                            <div>封顶 ¥{Math.round(parseInt(item.priceExtraInfo.ceiling_price) / 10000)}万</div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {selectedVideo && (
          <Dialog open={!!selectedVideo} onOpenChange={() => setSelectedVideo(null)}>
            <DialogContent className="max-w-3xl h-4/5 p-0">
              <iframe
                src={selectedVideo.embedUrl}
                width="100%"
                height="100%"
                allow="autoplay; encrypted-media"
                allowFullScreen
                className="rounded-lg"
              ></iframe>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  )
}
