#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API修复效果
"""

import asyncio
import sys
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from utils.logger import setup_logger
from utils.validators import validate_author_id


async def test_author_id_validation():
    """测试作者ID验证"""
    print("🔍 测试作者ID验证")
    print("-" * 40)
    
    test_cases = [
        ("7119367979820646432", "原始问题ID"),
        ("6651749112423120908", "已知有效ID"),
        ("123", "太短的ID"),
        ("12345678901234567890123456", "太长的ID"),
        ("1111111111111111111", "全相同数字"),
        ("abc123", "包含字母"),
        ("", "空字符串"),
        ("107955119950", "较短但可能有效的ID")
    ]
    
    for author_id, description in test_cases:
        is_valid = validate_author_id(author_id)
        status = "✅ 有效" if is_valid else "❌ 无效"
        print(f"{status} {author_id} ({description})")


async def test_api_responses():
    """测试API响应处理"""
    print("\n🔍 测试API响应处理")
    print("-" * 40)
    
    # 设置日志
    logger = setup_logger("test_api", "DEBUG")
    
    try:
        # 初始化组件
        cookie_manager = CookieManager()
        xingtu_client = XingtuClient(cookie_manager)
        
        # 测试有效的作者ID
        valid_author_id = "6651749112423120908"
        print(f"\n📊 测试有效作者ID: {valid_author_id}")
        
        author_data = await xingtu_client.get_author_info(valid_author_id, ["base_info"])
        
        if author_data:
            metadata = author_data.get("_metadata", {})
            print(f"✅ 成功获取数据")
            print(f"   成功请求: {metadata.get('successful_requests', 0)}")
            print(f"   失败请求: {metadata.get('failed_requests', 0)}")
            print(f"   成功率: {metadata.get('success_rate', 0):.2%}")
            
            # 检查具体端点结果
            base_info = author_data.get("base_info", {})
            if base_info.get("success"):
                print(f"   base_info: ✅ 成功")
                data = base_info.get("data", {})
                if isinstance(data, dict) and "nick_name" in data:
                    print(f"   昵称: {data['nick_name']}")
            else:
                print(f"   base_info: ❌ 失败 - {base_info.get('error', '未知错误')}")
        
        # 测试无效的作者ID
        invalid_author_id = "7119367979820646432"
        print(f"\n📊 测试问题作者ID: {invalid_author_id}")
        
        author_data = await xingtu_client.get_author_info(invalid_author_id, ["base_info"])
        
        if author_data:
            metadata = author_data.get("_metadata", {})
            print(f"⚠️  获取到响应")
            print(f"   成功请求: {metadata.get('successful_requests', 0)}")
            print(f"   失败请求: {metadata.get('failed_requests', 0)}")
            print(f"   成功率: {metadata.get('success_rate', 0):.2%}")
            
            # 检查具体端点结果
            base_info = author_data.get("base_info", {})
            if base_info.get("success"):
                print(f"   base_info: ✅ 成功")
            else:
                print(f"   base_info: ❌ 失败")
                print(f"   错误信息: {base_info.get('error', '未知错误')}")
                
                # 显示更多调试信息
                if "content_type" in base_info:
                    print(f"   内容类型: {base_info['content_type']}")
                if "response_preview" in base_info:
                    print(f"   响应预览: {base_info['response_preview'][:100]}...")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


async def test_error_handling():
    """测试错误处理改进"""
    print("\n🔍 测试错误处理改进")
    print("-" * 40)
    
    # 模拟不同类型的错误响应
    from core.xingtu_client import XingtuClient
    from core.cookie_manager import CookieManager
    
    cookie_manager = CookieManager()
    xingtu_client = XingtuClient(cookie_manager)
    
    # 测试单个端点
    from config.api_endpoints import XINGTU_API_ENDPOINTS
    
    test_endpoint = XINGTU_API_ENDPOINTS[0]  # base_info
    
    print(f"📋 测试端点: {test_endpoint.name}")
    
    # 测试有效ID
    valid_id = "6651749112423120908"
    result = await xingtu_client._make_request(test_endpoint, valid_id)
    
    print(f"✅ 有效ID ({valid_id}) 结果:")
    print(f"   成功: {result.get('success', False)}")
    print(f"   错误: {result.get('error', '无')}")
    
    # 测试无效ID
    invalid_id = "7119367979820646432"
    result = await xingtu_client._make_request(test_endpoint, invalid_id)
    
    print(f"⚠️  无效ID ({invalid_id}) 结果:")
    print(f"   成功: {result.get('success', False)}")
    print(f"   错误: {result.get('error', '无')}")
    if "content_type" in result:
        print(f"   内容类型: {result['content_type']}")


async def main():
    """主测试函数"""
    print("🧪 API修复效果测试")
    print("=" * 50)
    
    await test_author_id_validation()
    await test_api_responses()
    await test_error_handling()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print("✅ 作者ID验证已改进")
    print("✅ API响应处理已改进")
    print("✅ 错误处理已增强")
    print("✅ 调试信息已完善")
    
    print("\n💡 建议:")
    print("1. 使用有效的作者ID进行测试")
    print("2. 检查API响应的详细错误信息")
    print("3. 监控成功率指标")


if __name__ == "__main__":
    print("🚀 开始API修复测试")
    print("ℹ️  此工具用于验证API修复效果")
    print("=" * 50)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 测试结束")
