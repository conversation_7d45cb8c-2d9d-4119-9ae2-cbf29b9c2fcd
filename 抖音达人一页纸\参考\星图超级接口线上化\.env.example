# 星图超级接口线上化 Environment Configuration

# ===== CookieCloud Configuration =====
# CookieCloud server URL (required)
COOKIECLOUD_SERVER_URL=http://localhost:8088

# CookieCloud UUID (required)
COOKIECLOUD_UUID=your-uuid-here

# CookieCloud password (required)
COOKIECLOUD_PASSWORD=your-password-here

# ===== Xingtu API Configuration =====
# Xingtu base URL
XINGTU_BASE_URL=https://www.xingtu.cn

# Xingtu domain for cookie filtering
XINGTU_DOMAIN=www.xingtu.cn

# Request timeout in seconds
REQUEST_TIMEOUT=30

# Maximum retries for failed requests
MAX_RETRIES=3

# Retry interval in seconds
RETRY_INTERVAL=2

# Rate limiting - requests per minute
RATE_LIMIT_PER_MINUTE=60

# ===== Application Configuration =====
# Application environment (development, staging, production)
APP_ENV=development

# API host and port
API_HOST=0.0.0.0
API_PORT=8000

# Enable debug mode (true/false)
DEBUG=true

# Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# ===== Export Configuration =====
# Default export directory
EXPORT_DIR=exports

# Maximum export file size in MB
MAX_EXPORT_SIZE_MB=100

# Excel export format (xlsx, xls)
EXCEL_FORMAT=xlsx

# ===== Security Configuration =====
# API key for authentication (optional)
API_KEY=

# CORS allowed origins (comma-separated)
CORS_ORIGINS=*

# Enable request logging (true/false)
ENABLE_REQUEST_LOGGING=true

# ===== Monitoring Configuration =====
# Health check interval in seconds
HEALTH_CHECK_INTERVAL=60

# Cookie refresh interval in minutes
COOKIE_REFRESH_INTERVAL=30

# ===== Docker Configuration =====
# Container timezone
TZ=Asia/Shanghai

# Container user ID (for file permissions)
PUID=1000
PGID=1000
