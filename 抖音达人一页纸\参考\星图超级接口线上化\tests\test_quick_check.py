#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick check test for 星图超级接口线上化
"""

import os
import sys
import time
import json
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def make_request(url, method="GET", data=None, timeout=30):
    """Make HTTP request using urllib"""
    try:
        if data:
            data = json.dumps(data).encode('utf-8')
        
        req = urllib.request.Request(url, data=data, method=method)
        req.add_header('Content-Type', 'application/json')
        
        start_time = time.time()
        with urllib.request.urlopen(req, timeout=timeout) as response:
            response_time = time.time() - start_time
            content = response.read().decode('utf-8')
            
            return {
                "success": True,
                "status_code": response.status,
                "content": content,
                "response_time": response_time
            }
    
    except urllib.error.HTTPError as e:
        response_time = time.time() - start_time if 'start_time' in locals() else 0
        content = e.read().decode('utf-8') if e.fp else ""
        
        return {
            "success": False,
            "status_code": e.code,
            "content": content,
            "response_time": response_time,
            "error": str(e)
        }
    
    except Exception as e:
        return {
            "success": False,
            "status_code": 0,
            "content": "",
            "response_time": 0,
            "error": str(e)
        }


def main():
    """Quick check"""
    print("🔍 Quick API Check...")
    
    base_url = "http://localhost:8000"
    
    # Wait a bit for server to be ready
    print("⏳ Waiting for server...")
    time.sleep(5)
    
    # Test root endpoint
    print("1. Testing root endpoint...")
    result = make_request(f"{base_url}/")
    if result["success"]:
        print(f"✅ Root endpoint: {result['status_code']}")
        data = json.loads(result["content"])
        print(f"   API: {data.get('name', 'unknown')}")
        print(f"   Version: {data.get('version', 'unknown')}")
        print(f"   Status: {data.get('status', 'unknown')}")
    else:
        print(f"❌ Root endpoint failed: {result.get('error', 'Unknown error')}")
        return False
    
    # Test health endpoint
    print("\n2. Testing health endpoint...")
    result = make_request(f"{base_url}/health")
    if result["success"]:
        print(f"✅ Health endpoint: {result['status_code']}")
        data = json.loads(result["content"])
        print(f"   Status: {data.get('status', 'unknown')}")
        
        components = data.get("components", {})
        for component, details in components.items():
            healthy = details.get("healthy", False)
            status = "✅" if healthy else "❌"
            print(f"   {status} {component}: {'Healthy' if healthy else 'Unhealthy'}")
    else:
        print(f"❌ Health endpoint failed: {result.get('error', 'Unknown error')}")
        print(f"   Status code: {result.get('status_code', 'unknown')}")
        print(f"   Content: {result.get('content', 'no content')[:200]}...")
    
    # Test endpoints info
    print("\n3. Testing endpoints info...")
    result = make_request(f"{base_url}/endpoints")
    if result["success"]:
        print(f"✅ Endpoints info: {result['status_code']}")
        data = json.loads(result["content"])
        print(f"   Total endpoints: {data.get('total_endpoints', 0)}")
        categories = data.get('categories', {})
        print(f"   Categories: {list(categories.keys())}")
    else:
        print(f"❌ Endpoints info failed: {result.get('error', 'Unknown error')}")
    
    # Test cookie info
    print("\n4. Testing cookie info...")
    result = make_request(f"{base_url}/cookies/info")
    if result["success"]:
        print(f"✅ Cookie info: {result['status_code']}")
        data = json.loads(result["content"])
        print(f"   Available cookies: {data.get('available_cookies', 0)}")
        print(f"   Last updated: {data.get('last_updated', 'unknown')}")
    else:
        print(f"❌ Cookie info failed: {result.get('error', 'Unknown error')}")
    
    print("\n✅ Quick check completed!")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
