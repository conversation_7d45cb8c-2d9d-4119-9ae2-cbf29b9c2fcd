#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Deployment script for 星图超级接口线上化
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path
import argparse


class Deployer:
    """Deployment manager"""
    
    def __init__(self, environment="production"):
        self.environment = environment
        self.project_root = Path(__file__).parent.parent
        self.compose_file = "docker-compose.yml"
        
    def run_command(self, command, description, check=True):
        """Run a command and handle errors"""
        print(f"🔄 {description}...")
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                check=check, 
                capture_output=True, 
                text=True,
                cwd=self.project_root
            )
            if result.returncode == 0:
                print(f"✅ {description} completed successfully")
                return True, result.stdout
            else:
                print(f"❌ {description} failed: {result.stderr}")
                return False, result.stderr
        except subprocess.CalledProcessError as e:
            print(f"❌ {description} failed: {e.stderr}")
            return False, e.stderr
    
    def check_prerequisites(self):
        """Check deployment prerequisites"""
        print("🔍 Checking prerequisites...")
        
        # Check Docker
        success, _ = self.run_command("docker --version", "Checking Docker", check=False)
        if not success:
            print("❌ Docker is required for deployment")
            return False
        
        # Check Docker Compose
        success, _ = self.run_command("docker-compose --version", "Checking Docker Compose", check=False)
        if not success:
            print("❌ Docker Compose is required for deployment")
            return False
        
        # Check .env file
        env_file = self.project_root / ".env"
        if not env_file.exists():
            print("❌ .env file not found. Please create it from .env.example")
            return False
        
        # Validate environment variables
        required_vars = [
            "COOKIECLOUD_SERVER_URL",
            "COOKIECLOUD_UUID",
            "COOKIECLOUD_PASSWORD"
        ]
        
        env_content = env_file.read_text()
        missing_vars = []
        
        for var in required_vars:
            if f"{var}=" not in env_content or f"{var}=your-" in env_content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Please configure these variables in .env: {', '.join(missing_vars)}")
            return False
        
        print("✅ Prerequisites check passed")
        return True
    
    def prepare_deployment(self):
        """Prepare for deployment"""
        print("🔧 Preparing deployment...")
        
        # Create data directories
        data_dirs = [
            "data/logs",
            "data/exports", 
            "data/nginx-logs",
            "data/redis"
        ]
        
        for directory in data_dirs:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created directory: {directory}")
        
        # Set proper permissions
        if os.name != 'nt':  # Not Windows
            self.run_command("chmod -R 755 data/", "Setting directory permissions", check=False)
        
        return True
    
    def build_images(self):
        """Build Docker images"""
        print("🏗️ Building Docker images...")
        
        success, output = self.run_command(
            "docker-compose build --no-cache",
            "Building application image"
        )
        
        if not success:
            return False
        
        # Check image size
        success, output = self.run_command(
            "docker images | grep xingtu",
            "Checking image size",
            check=False
        )
        
        if success and output:
            print(f"📦 Image info:\n{output}")
        
        return True
    
    def deploy_basic(self):
        """Deploy basic application"""
        print("🚀 Deploying basic application...")
        
        # Stop existing containers
        self.run_command(
            "docker-compose down",
            "Stopping existing containers",
            check=False
        )
        
        # Start application
        success, output = self.run_command(
            "docker-compose up -d xingtu-api",
            "Starting application container"
        )
        
        return success
    
    def deploy_with_nginx(self):
        """Deploy with Nginx reverse proxy"""
        print("🚀 Deploying with Nginx...")
        
        # Stop existing containers
        self.run_command(
            "docker-compose down",
            "Stopping existing containers",
            check=False
        )
        
        # Start with production profile
        success, output = self.run_command(
            "docker-compose --profile production up -d",
            "Starting application with Nginx"
        )
        
        return success
    
    def deploy_full_stack(self):
        """Deploy full stack with all services"""
        print("🚀 Deploying full stack...")
        
        # Stop existing containers
        self.run_command(
            "docker-compose down",
            "Stopping existing containers",
            check=False
        )
        
        # Start all services
        success, output = self.run_command(
            "docker-compose --profile production --profile cache up -d",
            "Starting full stack"
        )
        
        return success
    
    def wait_for_health(self, timeout=120):
        """Wait for application to be healthy"""
        print("⏳ Waiting for application to be healthy...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            success, output = self.run_command(
                "curl -f http://localhost:8000/health",
                "Checking health",
                check=False
            )
            
            if success:
                print("✅ Application is healthy")
                return True
            
            print("⏳ Waiting for application...")
            time.sleep(10)
        
        print("❌ Application health check timeout")
        return False
    
    def run_smoke_tests(self):
        """Run smoke tests"""
        print("🧪 Running smoke tests...")
        
        # Check if test script exists
        test_script = self.project_root / "scripts" / "test_production.py"
        if not test_script.exists():
            print("⚠️  Test script not found, skipping smoke tests")
            return True
        
        success, output = self.run_command(
            f"python {test_script}",
            "Running smoke tests",
            check=False
        )
        
        if success:
            print("✅ Smoke tests passed")
        else:
            print("⚠️  Some smoke tests failed - check logs")
        
        return success
    
    def show_status(self):
        """Show deployment status"""
        print("📊 Deployment Status:")
        print("=" * 50)
        
        # Show running containers
        success, output = self.run_command(
            "docker-compose ps",
            "Getting container status",
            check=False
        )
        
        if success:
            print("🐳 Running containers:")
            print(output)
        
        # Show logs
        print("\n📋 Recent logs:")
        self.run_command(
            "docker-compose logs --tail=20 xingtu-api",
            "Getting recent logs",
            check=False
        )
        
        # Show endpoints
        print("\n🌐 Available endpoints:")
        print("- Health check: http://localhost:8000/health")
        print("- API docs: http://localhost:8000/docs")
        print("- API info: http://localhost:8000/")
        
        if self.environment == "production":
            print("- Nginx (if enabled): http://localhost:80")
    
    def rollback(self):
        """Rollback deployment"""
        print("🔄 Rolling back deployment...")
        
        # Stop current containers
        self.run_command(
            "docker-compose down",
            "Stopping containers",
            check=False
        )
        
        # Remove current images
        self.run_command(
            "docker-compose down --rmi local",
            "Removing images",
            check=False
        )
        
        print("✅ Rollback completed")
    
    def cleanup(self):
        """Cleanup deployment artifacts"""
        print("🧹 Cleaning up...")
        
        # Remove stopped containers
        self.run_command(
            "docker container prune -f",
            "Removing stopped containers",
            check=False
        )
        
        # Remove unused images
        self.run_command(
            "docker image prune -f",
            "Removing unused images",
            check=False
        )
        
        # Remove unused volumes
        self.run_command(
            "docker volume prune -f",
            "Removing unused volumes",
            check=False
        )
        
        print("✅ Cleanup completed")


def main():
    """Main deployment function"""
    parser = argparse.ArgumentParser(description="Deploy 星图超级接口线上化")
    parser.add_argument(
        "--mode",
        choices=["basic", "nginx", "full"],
        default="basic",
        help="Deployment mode"
    )
    parser.add_argument(
        "--environment",
        choices=["development", "staging", "production"],
        default="production",
        help="Deployment environment"
    )
    parser.add_argument(
        "--skip-tests",
        action="store_true",
        help="Skip smoke tests"
    )
    parser.add_argument(
        "--rollback",
        action="store_true",
        help="Rollback deployment"
    )
    parser.add_argument(
        "--cleanup",
        action="store_true",
        help="Cleanup deployment artifacts"
    )
    parser.add_argument(
        "--status",
        action="store_true",
        help="Show deployment status"
    )
    
    args = parser.parse_args()
    
    deployer = Deployer(args.environment)
    
    if args.rollback:
        deployer.rollback()
        return
    
    if args.cleanup:
        deployer.cleanup()
        return
    
    if args.status:
        deployer.show_status()
        return
    
    print("🚀 Starting deployment of 星图超级接口线上化...")
    print(f"Mode: {args.mode}")
    print(f"Environment: {args.environment}")
    print("=" * 50)
    
    # Check prerequisites
    if not deployer.check_prerequisites():
        print("❌ Prerequisites check failed")
        sys.exit(1)
    
    # Prepare deployment
    if not deployer.prepare_deployment():
        print("❌ Deployment preparation failed")
        sys.exit(1)
    
    # Build images
    if not deployer.build_images():
        print("❌ Image build failed")
        sys.exit(1)
    
    # Deploy based on mode
    success = False
    if args.mode == "basic":
        success = deployer.deploy_basic()
    elif args.mode == "nginx":
        success = deployer.deploy_with_nginx()
    elif args.mode == "full":
        success = deployer.deploy_full_stack()
    
    if not success:
        print("❌ Deployment failed")
        sys.exit(1)
    
    # Wait for health
    if not deployer.wait_for_health():
        print("❌ Application failed to become healthy")
        deployer.show_status()
        sys.exit(1)
    
    # Run smoke tests
    if not args.skip_tests:
        if not deployer.run_smoke_tests():
            print("⚠️  Smoke tests failed - deployment may have issues")
    
    # Show status
    deployer.show_status()
    
    print("\n🎉 Deployment completed successfully!")
    print("Your 星图超级接口线上化 is now running!")


if __name__ == "__main__":
    main()
