#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的API测试脚本
"""

import requests
import time
import json

def test_api():
    """测试API端点"""
    base_url = "http://127.0.0.1:8000"
    
    print("🚀 开始测试星图超级接口线上化...")
    print("=" * 50)
    
    # 等待应用启动
    print("⏳ 等待应用启动...")
    time.sleep(2)
    
    # 测试根端点
    print("\n📍 测试根端点 (/)")
    try:
        response = requests.get(f"{base_url}/")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"应用名称: {data.get('name', 'Unknown')}")
            print(f"版本: {data.get('version', 'Unknown')}")
            print(f"状态: {data.get('status', 'Unknown')}")
            print("✅ 根端点测试通过")
        else:
            print(f"❌ 根端点测试失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 根端点测试异常: {e}")
    
    # 测试健康检查端点
    print("\n🏥 测试健康检查端点 (/health)")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"状态码: {response.status_code}")
        if response.status_code in [200, 503]:
            data = response.json()
            print(f"整体状态: {data.get('status', 'Unknown')}")
            components = data.get('components', {})
            for component, info in components.items():
                healthy = info.get('healthy', False)
                status_icon = "✅" if healthy else "❌"
                print(f"  {status_icon} {component}: {'健康' if healthy else '不健康'}")
            print("✅ 健康检查端点测试通过")
        else:
            print(f"❌ 健康检查端点测试失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 健康检查端点测试异常: {e}")
    
    # 测试端点信息
    print("\n📋 测试端点信息 (/endpoints)")
    try:
        response = requests.get(f"{base_url}/endpoints")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"总端点数: {data.get('total_endpoints', 0)}")
            categories = data.get('categories', {})
            print(f"分类数: {len(categories)}")
            for category in list(categories.keys())[:3]:  # 显示前3个分类
                print(f"  - {category}")
            print("✅ 端点信息测试通过")
        else:
            print(f"❌ 端点信息测试失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 端点信息测试异常: {e}")
    
    # 测试无效作者ID
    print("\n👤 测试无效作者ID (/author/invalid)")
    try:
        response = requests.get(f"{base_url}/author/invalid")
        print(f"状态码: {response.status_code}")
        if response.status_code == 400:
            print("✅ 无效作者ID验证通过")
        else:
            print(f"⚠️  无效作者ID返回状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 无效作者ID测试异常: {e}")
    
    # 测试导出格式验证
    print("\n📤 测试无效导出格式 (/export)")
    try:
        export_data = {
            "author_id": "1798563067188323",
            "format": "invalid_format"
        }
        response = requests.post(f"{base_url}/export", json=export_data)
        print(f"状态码: {response.status_code}")
        if response.status_code == 400:
            print("✅ 无效导出格式验证通过")
        elif response.status_code == 500:
            print("⚠️  导出格式验证返回500 (可能是CookieCloud配置问题)")
        else:
            print(f"⚠️  导出格式验证返回状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 导出格式测试异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 API测试完成!")

if __name__ == "__main__":
    test_api() 