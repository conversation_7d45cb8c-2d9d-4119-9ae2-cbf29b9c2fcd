#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Xingtu API endpoint definitions for 星图超级接口线上化
"""

from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class APIEndpoint:
    """API endpoint configuration"""
    name: str
    path: str
    specific_params: Dict[str, str]
    id_param_name: str
    description: str = ""
    category: str = "general"


# Define all Xingtu API endpoints
XINGTU_API_ENDPOINTS: List[APIEndpoint] = [
    # Aggregator endpoints
    APIEndpoint(
        name="commerce_seed_base_info",
        path="/gw/api/aggregator/get_author_commerce_seed_base_info",
        specific_params={"range": "30"},
        id_param_name="o_author_id",
        description="Author commerce seed base information",
        category="commerce"
    ),
    APIEndpoint(
        name="commerce_spread_info",
        path="/gw/api/aggregator/get_author_commerce_spread_info",
        specific_params={},
        id_param_name="o_author_id",
        description="Author commerce spread information",
        category="commerce"
    ),
    APIEndpoint(
        name="content_label_density",
        path="/gw/api/aggregator/get_author_content_label_density",
        specific_params={},
        id_param_name="o_author_id",
        description="Author content label density",
        category="content"
    ),
    APIEndpoint(
        name="contract_base_info",
        path="/gw/api/aggregator/get_author_contract_base_info",
        specific_params={"range": "90"},
        id_param_name="o_author_id",
        description="Author contract base information",
        category="contract"
    ),
    APIEndpoint(
        name="global_info",
        path="/gw/api/aggregator/get_author_global_info",
        specific_params={},
        id_param_name="o_author_id",
        description="Author global information",
        category="basic"
    ),
    APIEndpoint(
        name="homepage_videos",
        path="/gw/api/aggregator/get_author_homepage_videos",
        specific_params={"page": "1", "limit": "10"},
        id_param_name="o_author_id",
        description="Author homepage videos",
        category="content"
    ),
    APIEndpoint(
        name="order_experience",
        path="/gw/api/aggregator/get_author_order_experience",
        specific_params={"period": "30"},
        id_param_name="o_author_id",
        description="Author order experience",
        category="commerce"
    ),
    APIEndpoint(
        name="side_base_info",
        path="/gw/api/aggregator/get_author_side_base_info",
        specific_params={},
        id_param_name="o_author_id",
        description="Author side base information",
        category="basic"
    ),
    APIEndpoint(
        name="video_live_linkage_product_list",
        path="/gw/api/aggregator/get_author_video_live_linkage_product_list",
        specific_params={"time_period": "30"},
        id_param_name="star_author_id",
        description="Author video live linkage product list",
        category="commerce"
    ),
    APIEndpoint(
        name="video_live_linkage_stat",
        path="/gw/api/aggregator/get_author_video_live_linkage_stat",
        specific_params={"time_period": "30"},
        id_param_name="star_author_id",
        description="Author video live linkage statistics",
        category="commerce"
    ),
    
    # Author endpoints
    APIEndpoint(
        name="base_info",
        path="/gw/api/author/get_author_base_info",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "recommend": "true",
            "need_sec_uid": "true",
            "need_linkage_info": "true"
        },
        id_param_name="o_author_id",
        description="Author base information",
        category="basic"
    ),
    APIEndpoint(
        name="marketing_info",
        path="/gw/api/author/get_author_marketing_info",
        specific_params={"platform_source": "1", "platform_channel": "1"},
        id_param_name="o_author_id",
        description="Author marketing information",
        category="marketing"
    ),
    APIEndpoint(
        name="platform_channel_info_v2",
        path="/gw/api/author/get_author_platform_channel_info_v2",
        specific_params={"platform_source": "1", "platform_channel": "1"},
        id_param_name="o_author_id",
        description="Author platform channel information v2",
        category="basic"
    ),
    APIEndpoint(
        name="show_items_v2",
        path="/gw/api/author/get_author_show_items_v2",
        specific_params={
            "platform_channel": "1",
            "platform_source": "1",
            "limit": "10",
            "only_assign": "false",
            "flow_type": "0"
        },
        id_param_name="o_author_id",
        description="Author show items v2",
        category="content"
    ),
    
    # Data endpoints
    APIEndpoint(
        name="hot_comment_tokens",
        path="/gw/api/data/get_author_hot_comment_tokens",
        specific_params={"num": "10", "without_emoji": "true"},
        id_param_name="author_id",
        description="Author hot comment tokens",
        category="analytics"
    ),
    
    # Data SP endpoints
    APIEndpoint(
        name="audience_distribution",
        path="/gw/api/data_sp/author_audience_distribution",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "link_type": "5"
        },
        id_param_name="o_author_id",
        description="Author audience distribution",
        category="analytics"
    ),
    APIEndpoint(
        name="cp_info",
        path="/gw/api/data_sp/author_cp_info",
        specific_params={"platform_source": "1", "platform_channel": "1"},
        id_param_name="o_author_id",
        description="Author CP information",
        category="analytics"
    ),
    APIEndpoint(
        name="daily_link",
        path="/gw/api/data_sp/author_daily_link",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "industry_id": "0",
            "link_type": "5",
            "start_date": "2025-03-23",
            "end_date": "2025-04-22"
        },
        id_param_name="o_author_id",
        description="Author daily link data",
        category="analytics"
    ),
    APIEndpoint(
        name="link_card",
        path="/gw/api/data_sp/author_link_card",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "industry_id": "0"
        },
        id_param_name="o_author_id",
        description="Author link card information",
        category="analytics"
    ),
    APIEndpoint(
        name="link_struct",
        path="/gw/api/data_sp/author_link_struct",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "industry_id": "0"
        },
        id_param_name="o_author_id",
        description="Author link structure",
        category="analytics"
    ),
    APIEndpoint(
        name="local_info",
        path="/gw/api/data_sp/author_local_info",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "time_range": "30"
        },
        id_param_name="o_author_id",
        description="Author local information",
        category="analytics"
    ),
    APIEndpoint(
        name="local_sales_performance",
        path="/gw/api/data_sp/author_local_sales_performance",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "time_range": "30",
            "local_item_type": "0"
        },
        id_param_name="o_author_id",
        description="Author local sales performance",
        category="commerce"
    ),
    APIEndpoint(
        name="shopping_videos",
        path="/gw/api/data_sp/author_shopping_videos",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "industry_id": "0"
        },
        id_param_name="o_author_id",
        description="Author shopping videos",
        category="content"
    ),
    APIEndpoint(
        name="spread_videos",
        path="/gw/api/data_sp/author_spread_videos",
        specific_params={"platform_source": "1", "platform_channel": "1"},
        id_param_name="o_author_id",
        description="Author spread videos",
        category="content"
    ),
    APIEndpoint(
        name="video_distribution",
        path="/gw/api/data_sp/author_video_distribution",
        specific_params={"platform_source": "1", "platform_channel": "1"},
        id_param_name="o_author_id",
        description="Author video distribution",
        category="analytics"
    ),
    APIEndpoint(
        name="convert_ability",
        path="/gw/api/data_sp/get_author_convert_ability",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "industry_id": "0",
            "range": "2"
        },
        id_param_name="o_author_id",
        description="Author convert ability",
        category="analytics"
    ),
    APIEndpoint(
        name="daily_fans",
        path="/gw/api/data_sp/get_author_daily_fans",
        specific_params={
            "platform_source": "1",
            "start_date": "2025-03-23",
            "end_date": "2025-04-22",
            "author_type": "1"
        },
        id_param_name="author_id",
        description="Author daily fans data",
        category="analytics"
    ),
    APIEndpoint(
        name="spread_info_data_sp",
        path="/gw/api/data_sp/get_author_spread_info",
        specific_params={
            "platform_source": "1",
            "platform_channel": "1",
            "type": "2",
            "flow_type": "0",
            "only_assign": "true",
            "range": "2"
        },
        id_param_name="o_author_id",
        description="Author spread information (data_sp)",
        category="analytics"
    ),
    
    # Gauthor endpoints
    APIEndpoint(
        name="business_card_info",
        path="/gw/api/gauthor/author_get_business_card_info",
        specific_params={},
        id_param_name="o_author_id",
        description="Author business card information",
        category="basic"
    )
]


def get_endpoints_by_category(category: str) -> List[APIEndpoint]:
    """Get endpoints by category"""
    return [endpoint for endpoint in XINGTU_API_ENDPOINTS if endpoint.category == category]


def get_endpoint_by_name(name: str) -> APIEndpoint:
    """Get endpoint by name"""
    for endpoint in XINGTU_API_ENDPOINTS:
        if endpoint.name == name:
            return endpoint
    raise ValueError(f"Endpoint '{name}' not found")


def get_all_categories() -> List[str]:
    """Get all available categories"""
    categories = set(endpoint.category for endpoint in XINGTU_API_ENDPOINTS)
    return sorted(list(categories))


# Endpoint categories for organization
ENDPOINT_CATEGORIES = {
    "basic": "Basic author information",
    "commerce": "Commerce and sales data",
    "content": "Content and video information",
    "analytics": "Analytics and performance data",
    "marketing": "Marketing information",
    "contract": "Contract information"
}
