#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel export functionality for Xingtu author data
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import structlog

from config.settings import settings
from utils.logger import log_export_operation, log_error
from utils.validators import (
    validate_export_data, 
    sanitize_filename, 
    flatten_nested_dict,
    clean_text
)


class ExcelExporter:
    """
    Excel export functionality for Xingtu author data
    """
    
    def __init__(self):
        """Initialize Excel exporter"""
        self.logger = structlog.get_logger("excel_exporter")
        self.export_dir = Path(settings.export.export_dir)
        self.export_dir.mkdir(parents=True, exist_ok=True)
        self.max_file_size = settings.export.max_export_size_mb * 1024 * 1024  # Convert to bytes
    
    def _flatten_author_data(self, author_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Flatten nested author data for Excel export
        
        Args:
            author_data: Raw author data from API
            
        Returns:
            Flattened data dictionary
        """
        flattened = {}
        
        for endpoint_name, endpoint_data in author_data.items():
            if endpoint_name.startswith('_'):  # Skip metadata
                continue
                
            if isinstance(endpoint_data, dict):
                if endpoint_data.get("success", False) and endpoint_data.get("data"):
                    # Flatten the data from this endpoint
                    data = endpoint_data["data"]
                    if isinstance(data, dict):
                        flat_data = flatten_nested_dict(data, parent_key=endpoint_name)
                        flattened.update(flat_data)
                    elif isinstance(data, list) and data:
                        # Handle list data - convert to JSON string
                        flattened[f"{endpoint_name}_data"] = json.dumps(data, ensure_ascii=False)
                    else:
                        flattened[f"{endpoint_name}_data"] = str(data)
                else:
                    # Record error information
                    flattened[f"{endpoint_name}_error"] = endpoint_data.get("error", "Unknown error")
        
        return flattened
    
    def _create_summary_data(self, author_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create summary data from author information
        
        Args:
            author_data: Raw author data from API
            
        Returns:
            Summary data dictionary
        """
        summary = {}
        metadata = author_data.get("_metadata", {})
        
        # Basic metadata
        summary["author_id"] = metadata.get("author_id", "")
        summary["fetch_timestamp"] = datetime.fromtimestamp(
            metadata.get("fetch_timestamp", 0)
        ).strftime("%Y-%m-%d %H:%M:%S")
        summary["total_endpoints"] = metadata.get("total_endpoints", 0)
        summary["successful_requests"] = metadata.get("successful_requests", 0)
        summary["failed_requests"] = metadata.get("failed_requests", 0)
        summary["success_rate"] = f"{metadata.get('success_rate', 0):.2%}"
        
        # Extract key information from successful endpoints
        try:
            # Basic info
            if "base_info" in author_data and author_data["base_info"].get("success"):
                base_data = author_data["base_info"].get("data", {})
                summary["author_name"] = base_data.get("nickname", "")
                summary["platform"] = base_data.get("platform_name", "")
                summary["follower_count"] = base_data.get("follower_count", "")
            
            # Global info
            if "global_info" in author_data and author_data["global_info"].get("success"):
                global_data = author_data["global_info"].get("data", {})
                summary["total_videos"] = global_data.get("video_count", "")
                summary["avg_play_count"] = global_data.get("avg_play_count", "")
            
            # Commerce info
            if "commerce_seed_base_info" in author_data and author_data["commerce_seed_base_info"].get("success"):
                commerce_data = author_data["commerce_seed_base_info"].get("data", {})
                summary["commerce_score"] = commerce_data.get("commerce_score", "")
                summary["order_count"] = commerce_data.get("order_count", "")
                
        except Exception as e:
            self.logger.warning("Error extracting summary data", error=str(e))
        
        return summary
    
    def export_to_excel(self, author_data: Dict[str, Any], 
                       filename: Optional[str] = None,
                       include_raw_data: bool = True,
                       include_summary: bool = True) -> Optional[Path]:
        """
        Export author data to Excel file
        
        Args:
            author_data: Author data from Xingtu API
            filename: Custom filename (auto-generated if None)
            include_raw_data: Include raw API responses
            include_summary: Include summary sheet
            
        Returns:
            Path to exported file if successful, None if failed
        """
        try:
            if not validate_export_data([author_data]):
                self.logger.error("Invalid data for export")
                return None
            
            # Generate filename if not provided
            if not filename:
                author_id = author_data.get("_metadata", {}).get("author_id", "unknown")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"xingtu_author_{author_id}_{timestamp}"
            
            filename = sanitize_filename(filename)
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            
            file_path = self.export_dir / filename
            
            self.logger.info("Starting Excel export", filename=filename)
            
            # Create Excel writer
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                
                # Summary sheet
                if include_summary:
                    summary_data = self._create_summary_data(author_data)
                    summary_df = pd.DataFrame([summary_data])
                    summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # Flattened data sheet
                flattened_data = self._flatten_author_data(author_data)
                if flattened_data:
                    flat_df = pd.DataFrame([flattened_data])
                    flat_df.to_excel(writer, sheet_name='Flattened_Data', index=False)
                
                # Raw data sheets (one per endpoint)
                if include_raw_data:
                    for endpoint_name, endpoint_data in author_data.items():
                        if endpoint_name.startswith('_'):  # Skip metadata
                            continue
                        
                        if isinstance(endpoint_data, dict) and endpoint_data.get("success"):
                            data = endpoint_data.get("data")
                            if data:
                                try:
                                    # Convert to DataFrame
                                    if isinstance(data, dict):
                                        df = pd.DataFrame([data])
                                    elif isinstance(data, list):
                                        df = pd.DataFrame(data)
                                    else:
                                        df = pd.DataFrame([{"data": str(data)}])
                                    
                                    # Sanitize sheet name
                                    sheet_name = sanitize_filename(endpoint_name)[:31]  # Excel sheet name limit
                                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                                    
                                except Exception as e:
                                    self.logger.warning(
                                        "Failed to export endpoint data", 
                                        endpoint=endpoint_name, 
                                        error=str(e)
                                    )
            
            # Check file size
            file_size = file_path.stat().st_size
            if file_size > self.max_file_size:
                self.logger.warning(
                    "Exported file exceeds size limit", 
                    file_size=file_size, 
                    limit=self.max_file_size
                )
            
            log_export_operation(
                self.logger,
                "excel_export",
                str(file_path),
                record_count=1,
                file_size=file_size,
                success=True
            )
            
            return file_path
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "excel_export", "filename": filename})
            return None
    
    def export_multiple_authors(self, authors_data: List[Dict[str, Any]], 
                              filename: Optional[str] = None) -> Optional[Path]:
        """
        Export multiple authors data to single Excel file
        
        Args:
            authors_data: List of author data dictionaries
            filename: Custom filename (auto-generated if None)
            
        Returns:
            Path to exported file if successful, None if failed
        """
        try:
            if not validate_export_data(authors_data):
                self.logger.error("Invalid data for export")
                return None
            
            # Generate filename if not provided
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"xingtu_authors_batch_{timestamp}"
            
            filename = sanitize_filename(filename)
            if not filename.endswith('.xlsx'):
                filename += '.xlsx'
            
            file_path = self.export_dir / filename
            
            self.logger.info("Starting batch Excel export", filename=filename, author_count=len(authors_data))
            
            # Prepare data for export
            summary_data = []
            flattened_data = []
            
            for author_data in authors_data:
                summary_data.append(self._create_summary_data(author_data))
                flattened_data.append(self._flatten_author_data(author_data))
            
            # Create Excel writer
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                
                # Summary sheet
                if summary_data:
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # Flattened data sheet
                if flattened_data:
                    flat_df = pd.DataFrame(flattened_data)
                    flat_df.to_excel(writer, sheet_name='Flattened_Data', index=False)
            
            file_size = file_path.stat().st_size
            
            log_export_operation(
                self.logger,
                "batch_excel_export",
                str(file_path),
                record_count=len(authors_data),
                file_size=file_size,
                success=True
            )
            
            return file_path
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "batch_excel_export", "filename": filename})
            return None
    
    def export_to_csv(self, author_data: Dict[str, Any], 
                     filename: Optional[str] = None) -> Optional[Path]:
        """
        Export flattened author data to CSV file
        
        Args:
            author_data: Author data from Xingtu API
            filename: Custom filename (auto-generated if None)
            
        Returns:
            Path to exported file if successful, None if failed
        """
        try:
            if not validate_export_data([author_data]):
                self.logger.error("Invalid data for CSV export")
                return None
            
            # Generate filename if not provided
            if not filename:
                author_id = author_data.get("_metadata", {}).get("author_id", "unknown")
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"xingtu_author_{author_id}_{timestamp}"
            
            filename = sanitize_filename(filename)
            if not filename.endswith('.csv'):
                filename += '.csv'
            
            file_path = self.export_dir / filename
            
            # Flatten data
            flattened_data = self._flatten_author_data(author_data)
            
            if not flattened_data:
                self.logger.error("No data to export to CSV")
                return None
            
            # Create DataFrame and export
            df = pd.DataFrame([flattened_data])
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            file_size = file_path.stat().st_size
            
            log_export_operation(
                self.logger,
                "csv_export",
                str(file_path),
                record_count=1,
                file_size=file_size,
                success=True
            )
            
            return file_path
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "csv_export", "filename": filename})
            return None
    
    def get_export_info(self) -> Dict[str, Any]:
        """
        Get export directory information
        
        Returns:
            Export directory statistics
        """
        try:
            files = list(self.export_dir.glob("*"))
            total_size = sum(f.stat().st_size for f in files if f.is_file())
            
            return {
                "export_directory": str(self.export_dir),
                "total_files": len([f for f in files if f.is_file()]),
                "total_size_bytes": total_size,
                "total_size_mb": round(total_size / (1024 * 1024), 2),
                "max_file_size_mb": settings.export.max_export_size_mb,
                "supported_formats": ["xlsx", "csv", "json"]
            }
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "get_export_info"})
            return {"error": str(e)}
