#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版本地测试脚本 - 逐步测试达人信息获取功能
"""

import sys
import os
from pathlib import Path
import asyncio
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

print("🚀 星图超级接口线上化 - 简化版本地测试")
print("=" * 50)

try:
    # 步骤1: 测试环境变量加载
    print("📁 步骤1: 加载环境变量...")
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 环境变量加载成功")
    
    # 步骤2: 测试配置加载
    print("\n📄 步骤2: 测试配置...")
    from config.settings import settings
    print(f"✅ 配置加载成功")
    print(f"   - 环境: {settings.app.env}")
    print(f"   - 调试模式: {settings.app.debug}")
    print(f"   - Xingtu基础URL: {settings.xingtu.base_url}")
    
    # 步骤3: 测试日志系统
    print("\n📝 步骤3: 测试日志系统...")
    from utils.logger import setup_logger
    logger = setup_logger("simple_test", "INFO")
    print("✅ 日志系统初始化成功")
    
    # 步骤4: 测试API端点配置
    print("\n📡 步骤4: 测试API端点配置...")
    from config.api_endpoints import get_all_categories, XINGTU_API_ENDPOINTS
    categories = get_all_categories()
    print(f"✅ API端点配置加载成功")
    print(f"   - 可用分类数: {len(categories)}")
    print(f"   - 总端点数: {len(XINGTU_API_ENDPOINTS)}")
    
    # 显示可用的端点分类
    print("   - 可用分类:")
    for category in categories:
        count = len([ep for ep in XINGTU_API_ENDPOINTS if ep.category == category])
        print(f"     * {category}: {count}个端点")
    
    # 步骤5: 显示几个示例端点
    print("\n📋 步骤5: 示例端点信息...")
    sample_endpoints = XINGTU_API_ENDPOINTS[:3]
    for i, endpoint in enumerate(sample_endpoints, 1):
        print(f"   {i}. {endpoint.name}")
        print(f"      描述: {endpoint.description}")
        print(f"      路径: {endpoint.path}")
        print(f"      分类: {endpoint.category}")
    
    print("\n🎯 步骤6: 准备测试Cookie管理器...")
    print("注意: 如果下一步卡住，说明Cookie管理器初始化有问题")
    
    # 尝试初始化Cookie管理器（这一步可能会卡住）
    try:
        from core.cookie_manager import CookieManager
        print("✅ Cookie管理器模块导入成功")
        
        print("🍪 正在初始化Cookie管理器...")
        cookie_manager = CookieManager()
        print("✅ Cookie管理器初始化成功")
        
        # 检查cookie状态
        is_healthy = cookie_manager.is_healthy()
        print(f"🍪 Cookie管理器健康状态: {'健康' if is_healthy else '异常'}")
        
        cookies_info = cookie_manager.get_cookies_info()
        print(f"🍪 Cookie信息获取成功")
        print(f"   - 域名: {cookies_info.get('domain', 'N/A')}")
        print(f"   - Cookie数量: {len(cookies_info.get('cookies', []))}")
        
        # 步骤7: 测试Xingtu客户端
        print("\n🔗 步骤7: 测试Xingtu客户端...")
        from core.xingtu_client import XingtuClient
        xingtu_client = XingtuClient(cookie_manager)
        print("✅ Xingtu客户端初始化成功")
        
        # 获取客户端状态
        client_status = xingtu_client.get_health_status()
        print(f"🔗 客户端健康状态:")
        print(f"   - Session准备: {client_status['session_ready']}")
        print(f"   - Cookies可用: {client_status['cookies_available']}")
        
        # 获取可用端点
        available_endpoints = xingtu_client.get_available_endpoints()
        print(f"📡 可用端点: {len(available_endpoints)}个")
        
        print("\n✅ 所有基础组件测试通过!")
        
        # 步骤8: 测试实际作者数据获取
        print("\n⚡️ 步骤8: 测试实际作者数据获取...")
        
        async def fetch_author_data():
            test_author_id = "7119367979820646432" # 您指定的ID
            print(f"⏳ 正在尝试获取作者ID: {test_author_id} 的数据...")
            try:
                author_data = await xingtu_client.get_author_info(test_author_id)
                
                if author_data and "_metadata" in author_data:
                    print(f"✅ 成功获取到数据!")
                    # 为了简洁，只打印部分关键信息
                    print(json.dumps(author_data, indent=2, ensure_ascii=False))
                else:
                    print(f"❌ 获取数据失败: {author_data.get('error', '未知错误')}")

            except Exception as e:
                print(f"❌ 在获取数据时发生严重错误: {e}")
                import traceback
                traceback.print_exc()

        # 运行异步函数
        asyncio.run(fetch_author_data())

    except Exception as e:
        print(f"❌ Cookie管理器或客户端初始化失败: {e}")
        print("这可能是因为:")
        print("1. 缺少必要的环境变量（COOKIECLOUD_*）")
        print("2. CookieCloud服务不可用")
        print("3. 网络连接问题")
        import traceback
        traceback.print_exc()
    
except Exception as e:
    print(f"❌ 测试过程中发生错误: {e}")
    import traceback
    traceback.print_exc()

print("\n👋 测试结束") 