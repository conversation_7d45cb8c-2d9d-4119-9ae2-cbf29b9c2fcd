{"version": 3, "sources": ["../../../../src/build/babel/plugins/next-page-config.ts"], "sourcesContent": ["import { types as BabelTypes } from 'next/dist/compiled/babel/core'\nimport type {\n  PluginObj,\n  PluginPass,\n  Visitor,\n  NodePath,\n} from 'next/dist/compiled/babel/core'\nimport type { PageConfig } from '../../../types'\nimport { STRING_LITERAL_DROP_BUNDLE } from '../../../shared/lib/constants'\n\nconst CONFIG_KEY = 'config'\n\n// replace program path with just a variable with the drop identifier\nfunction replaceBundle(path: any, t: typeof BabelTypes): void {\n  path.parentPath.replaceWith(\n    t.program(\n      [\n        t.variableDeclaration('const', [\n          t.variableDeclarator(\n            t.identifier(STRING_LITERAL_DROP_BUNDLE),\n            t.stringLiteral(`${STRING_LITERAL_DROP_BUNDLE} ${Date.now()}`)\n          ),\n        ]),\n      ],\n      []\n    )\n  )\n}\n\nfunction errorMessage(state: any, details: string): string {\n  const pageName =\n    (state.filename || '').split(state.cwd || '').pop() || 'unknown'\n  return `Invalid page config export found. ${details} in file ${pageName}. See: https://nextjs.org/docs/messages/invalid-page-config`\n}\n\ninterface ConfigState extends PluginPass {\n  bundleDropped?: boolean\n}\n\n// config to parsing pageConfig for client bundles\nexport default function nextPageConfig({\n  types: t,\n}: {\n  types: typeof BabelTypes\n}): PluginObj {\n  return {\n    visitor: {\n      Program: {\n        enter(path, state) {\n          path.traverse(\n            {\n              ExportDeclaration(exportPath, exportState) {\n                if (\n                  BabelTypes.isExportNamedDeclaration(exportPath.node) &&\n                  exportPath.node.specifiers?.some((specifier) => {\n                    return (\n                      (t.isIdentifier(specifier.exported)\n                        ? specifier.exported.name\n                        : specifier.exported.value) === CONFIG_KEY\n                    )\n                  }) &&\n                  BabelTypes.isStringLiteral(\n                    (exportPath.node as BabelTypes.ExportNamedDeclaration)\n                      .source\n                  )\n                ) {\n                  throw new Error(\n                    errorMessage(\n                      exportState,\n                      'Expected object but got export from'\n                    )\n                  )\n                }\n              },\n              ExportNamedDeclaration(\n                exportPath: NodePath<BabelTypes.ExportNamedDeclaration>,\n                exportState: any\n              ) {\n                if (\n                  exportState.bundleDropped ||\n                  (!exportPath.node.declaration &&\n                    exportPath.node.specifiers.length === 0)\n                ) {\n                  return\n                }\n\n                const config: PageConfig = {}\n                const declarations: BabelTypes.VariableDeclarator[] = [\n                  ...((\n                    exportPath.node\n                      .declaration as BabelTypes.VariableDeclaration\n                  )?.declarations || []),\n                  exportPath.scope.getBinding(CONFIG_KEY)?.path\n                    .node as BabelTypes.VariableDeclarator,\n                ].filter(Boolean)\n\n                for (const specifier of exportPath.node.specifiers) {\n                  if (\n                    (t.isIdentifier(specifier.exported)\n                      ? specifier.exported.name\n                      : specifier.exported.value) === CONFIG_KEY\n                  ) {\n                    // export {} from 'somewhere'\n                    if (BabelTypes.isStringLiteral(exportPath.node.source)) {\n                      throw new Error(\n                        errorMessage(\n                          exportState,\n                          `Expected object but got import`\n                        )\n                      )\n                      // import hello from 'world'\n                      // export { hello as config }\n                    } else if (\n                      BabelTypes.isIdentifier(\n                        (specifier as BabelTypes.ExportSpecifier).local\n                      )\n                    ) {\n                      if (\n                        BabelTypes.isImportSpecifier(\n                          exportPath.scope.getBinding(\n                            (specifier as BabelTypes.ExportSpecifier).local.name\n                          )?.path.node\n                        )\n                      ) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Expected object but got import`\n                          )\n                        )\n                      }\n                    }\n                  }\n                }\n\n                for (const declaration of declarations) {\n                  if (\n                    !BabelTypes.isIdentifier(declaration.id, {\n                      name: CONFIG_KEY,\n                    })\n                  ) {\n                    continue\n                  }\n\n                  let { init } = declaration\n                  if (BabelTypes.isTSAsExpression(init)) {\n                    init = init.expression\n                  }\n\n                  if (!BabelTypes.isObjectExpression(init)) {\n                    const got = init ? init.type : 'undefined'\n                    throw new Error(\n                      errorMessage(\n                        exportState,\n                        `Expected object but got ${got}`\n                      )\n                    )\n                  }\n\n                  for (const prop of init.properties) {\n                    if (BabelTypes.isSpreadElement(prop)) {\n                      throw new Error(\n                        errorMessage(\n                          exportState,\n                          `Property spread is not allowed`\n                        )\n                      )\n                    }\n                    const { name } = prop.key as BabelTypes.Identifier\n                    if (BabelTypes.isIdentifier(prop.key, { name: 'amp' })) {\n                      if (!BabelTypes.isObjectProperty(prop)) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Invalid property \"${name}\"`\n                          )\n                        )\n                      }\n                      if (\n                        !BabelTypes.isBooleanLiteral(prop.value) &&\n                        !BabelTypes.isStringLiteral(prop.value)\n                      ) {\n                        throw new Error(\n                          errorMessage(\n                            exportState,\n                            `Invalid value for \"${name}\"`\n                          )\n                        )\n                      }\n                      config.amp = prop.value.value as PageConfig['amp']\n                    }\n                  }\n                }\n\n                if (config.amp === true) {\n                  if (!exportState.file?.opts?.caller.isDev) {\n                    // don't replace bundle in development so HMR can track\n                    // dependencies and trigger reload when they are changed\n                    replaceBundle(exportPath, t)\n                  }\n                  exportState.bundleDropped = true\n                  return\n                }\n              },\n            },\n            state\n          )\n        },\n      },\n    } as Visitor<ConfigState>,\n  }\n}\n"], "names": ["nextPageConfig", "CONFIG_KEY", "replaceBundle", "path", "t", "parentPath", "replaceWith", "program", "variableDeclaration", "variableDeclarator", "identifier", "STRING_LITERAL_DROP_BUNDLE", "stringLiteral", "Date", "now", "errorMessage", "state", "details", "pageName", "filename", "split", "cwd", "pop", "types", "visitor", "Program", "enter", "traverse", "ExportDeclaration", "exportPath", "exportState", "BabelTypes", "isExportNamedDeclaration", "node", "specifiers", "some", "specifier", "isIdentifier", "exported", "name", "value", "isStringLiteral", "source", "Error", "ExportNamedDeclaration", "bundleDropped", "declaration", "length", "config", "declarations", "scope", "getBinding", "filter", "Boolean", "local", "isImportSpecifier", "id", "init", "isTSAsExpression", "expression", "isObjectExpression", "got", "type", "prop", "properties", "isSpreadElement", "key", "isObjectProperty", "isBooleanLiteral", "amp", "file", "opts", "caller", "isDev"], "mappings": ";;;;+BAuCA,kDAAkD;AAClD;;;eAAwBA;;;sBAxCY;2BAQO;AAE3C,MAAMC,aAAa;AAEnB,qEAAqE;AACrE,SAASC,cAAcC,IAAS,EAAEC,CAAoB;IACpDD,KAAKE,UAAU,CAACC,WAAW,CACzBF,EAAEG,OAAO,CACP;QACEH,EAAEI,mBAAmB,CAAC,SAAS;YAC7BJ,EAAEK,kBAAkB,CAClBL,EAAEM,UAAU,CAACC,qCAA0B,GACvCP,EAAEQ,aAAa,CAAC,GAAGD,qCAA0B,CAAC,CAAC,EAAEE,KAAKC,GAAG,IAAI;SAEhE;KACF,EACD,EAAE;AAGR;AAEA,SAASC,aAAaC,KAAU,EAAEC,OAAe;IAC/C,MAAMC,WACJ,AAACF,CAAAA,MAAMG,QAAQ,IAAI,EAAC,EAAGC,KAAK,CAACJ,MAAMK,GAAG,IAAI,IAAIC,GAAG,MAAM;IACzD,OAAO,CAAC,kCAAkC,EAAEL,QAAQ,SAAS,EAAEC,SAAS,2DAA2D,CAAC;AACtI;AAOe,SAASlB,eAAe,EACrCuB,OAAOnB,CAAC,EAGT;IACC,OAAO;QACLoB,SAAS;YACPC,SAAS;gBACPC,OAAMvB,IAAI,EAAEa,KAAK;oBACfb,KAAKwB,QAAQ,CACX;wBACEC,mBAAkBC,UAAU,EAAEC,WAAW;gCAGrCD;4BAFF,IACEE,WAAU,CAACC,wBAAwB,CAACH,WAAWI,IAAI,OACnDJ,8BAAAA,WAAWI,IAAI,CAACC,UAAU,qBAA1BL,4BAA4BM,IAAI,CAAC,CAACC;gCAChC,OACE,AAAChC,CAAAA,EAAEiC,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOvC;4BAEtC,OACA8B,WAAU,CAACU,eAAe,CACxB,AAACZ,WAAWI,IAAI,CACbS,MAAM,GAEX;gCACA,MAAM,qBAKL,CALK,IAAIC,MACR5B,aACEe,aACA,yCAHE,qBAAA;2CAAA;gDAAA;kDAAA;gCAKN;4BACF;wBACF;wBACAc,wBACEf,UAAuD,EACvDC,WAAgB;gCAaZD,8BAGFA;4BAdF,IACEC,YAAYe,aAAa,IACxB,CAAChB,WAAWI,IAAI,CAACa,WAAW,IAC3BjB,WAAWI,IAAI,CAACC,UAAU,CAACa,MAAM,KAAK,GACxC;gCACA;4BACF;4BAEA,MAAMC,SAAqB,CAAC;4BAC5B,MAAMC,eAAgD;mCAChD,EACFpB,+BAAAA,WAAWI,IAAI,CACZa,WAAW,qBAFZ,AACFjB,6BAECoB,YAAY,KAAI,EAAE;iCACrBpB,+BAAAA,WAAWqB,KAAK,CAACC,UAAU,CAAClD,gCAA5B4B,6BAAyC1B,IAAI,CAC1C8B,IAAI;6BACR,CAACmB,MAAM,CAACC;4BAET,KAAK,MAAMjB,aAAaP,WAAWI,IAAI,CAACC,UAAU,CAAE;gCAClD,IACE,AAAC9B,CAAAA,EAAEiC,YAAY,CAACD,UAAUE,QAAQ,IAC9BF,UAAUE,QAAQ,CAACC,IAAI,GACvBH,UAAUE,QAAQ,CAACE,KAAK,AAAD,MAAOvC,YAClC;oCACA,6BAA6B;oCAC7B,IAAI8B,WAAU,CAACU,eAAe,CAACZ,WAAWI,IAAI,CAACS,MAAM,GAAG;wCACtD,MAAM,qBAKL,CALK,IAAIC,MACR5B,aACEe,aACA,CAAC,8BAA8B,CAAC,IAH9B,qBAAA;mDAAA;wDAAA;0DAAA;wCAKN;oCACA,4BAA4B;oCAC5B,6BAA6B;oCAC/B,OAAO,IACLC,WAAU,CAACM,YAAY,CACrB,AAACD,UAAyCkB,KAAK,GAEjD;4CAGIzB;wCAFJ,IACEE,WAAU,CAACwB,iBAAiB,EAC1B1B,gCAAAA,WAAWqB,KAAK,CAACC,UAAU,CACzB,AAACf,UAAyCkB,KAAK,CAACf,IAAI,sBADtDV,8BAEG1B,IAAI,CAAC8B,IAAI,GAEd;4CACA,MAAM,qBAKL,CALK,IAAIU,MACR5B,aACEe,aACA,CAAC,8BAA8B,CAAC,IAH9B,qBAAA;uDAAA;4DAAA;8DAAA;4CAKN;wCACF;oCACF;gCACF;4BACF;4BAEA,KAAK,MAAMgB,eAAeG,aAAc;gCACtC,IACE,CAAClB,WAAU,CAACM,YAAY,CAACS,YAAYU,EAAE,EAAE;oCACvCjB,MAAMtC;gCACR,IACA;oCACA;gCACF;gCAEA,IAAI,EAAEwD,IAAI,EAAE,GAAGX;gCACf,IAAIf,WAAU,CAAC2B,gBAAgB,CAACD,OAAO;oCACrCA,OAAOA,KAAKE,UAAU;gCACxB;gCAEA,IAAI,CAAC5B,WAAU,CAAC6B,kBAAkB,CAACH,OAAO;oCACxC,MAAMI,MAAMJ,OAAOA,KAAKK,IAAI,GAAG;oCAC/B,MAAM,qBAKL,CALK,IAAInB,MACR5B,aACEe,aACA,CAAC,wBAAwB,EAAE+B,KAAK,IAH9B,qBAAA;+CAAA;oDAAA;sDAAA;oCAKN;gCACF;gCAEA,KAAK,MAAME,QAAQN,KAAKO,UAAU,CAAE;oCAClC,IAAIjC,WAAU,CAACkC,eAAe,CAACF,OAAO;wCACpC,MAAM,qBAKL,CALK,IAAIpB,MACR5B,aACEe,aACA,CAAC,8BAA8B,CAAC,IAH9B,qBAAA;mDAAA;wDAAA;0DAAA;wCAKN;oCACF;oCACA,MAAM,EAAES,IAAI,EAAE,GAAGwB,KAAKG,GAAG;oCACzB,IAAInC,WAAU,CAACM,YAAY,CAAC0B,KAAKG,GAAG,EAAE;wCAAE3B,MAAM;oCAAM,IAAI;wCACtD,IAAI,CAACR,WAAU,CAACoC,gBAAgB,CAACJ,OAAO;4CACtC,MAAM,qBAKL,CALK,IAAIpB,MACR5B,aACEe,aACA,CAAC,kBAAkB,EAAES,KAAK,CAAC,CAAC,IAH1B,qBAAA;uDAAA;4DAAA;8DAAA;4CAKN;wCACF;wCACA,IACE,CAACR,WAAU,CAACqC,gBAAgB,CAACL,KAAKvB,KAAK,KACvC,CAACT,WAAU,CAACU,eAAe,CAACsB,KAAKvB,KAAK,GACtC;4CACA,MAAM,qBAKL,CALK,IAAIG,MACR5B,aACEe,aACA,CAAC,mBAAmB,EAAES,KAAK,CAAC,CAAC,IAH3B,qBAAA;uDAAA;4DAAA;8DAAA;4CAKN;wCACF;wCACAS,OAAOqB,GAAG,GAAGN,KAAKvB,KAAK,CAACA,KAAK;oCAC/B;gCACF;4BACF;4BAEA,IAAIQ,OAAOqB,GAAG,KAAK,MAAM;oCAClBvC,wBAAAA;gCAAL,IAAI,GAACA,oBAAAA,YAAYwC,IAAI,sBAAhBxC,yBAAAA,kBAAkByC,IAAI,qBAAtBzC,uBAAwB0C,MAAM,CAACC,KAAK,GAAE;oCACzC,uDAAuD;oCACvD,wDAAwD;oCACxDvE,cAAc2B,YAAYzB;gCAC5B;gCACA0B,YAAYe,aAAa,GAAG;gCAC5B;4BACF;wBACF;oBACF,GACA7B;gBAEJ;YACF;QACF;IACF;AACF"}