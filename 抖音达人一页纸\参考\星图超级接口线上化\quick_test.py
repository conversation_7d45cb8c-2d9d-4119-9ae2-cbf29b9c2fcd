#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.validators import validate_author_id

def test_validation():
    """测试作者ID验证"""
    print("🔍 测试作者ID验证")
    
    test_cases = [
        ("7119367979820646432", "原始问题ID"),
        ("6651749112423120908", "已知有效ID"),
        ("123", "太短的ID"),
        ("1111111111111111111", "全相同数字"),
    ]
    
    for author_id, description in test_cases:
        is_valid = validate_author_id(author_id)
        status = "✅ 有效" if is_valid else "❌ 无效"
        print(f"{status} {author_id} ({description})")

if __name__ == "__main__":
    test_validation()
