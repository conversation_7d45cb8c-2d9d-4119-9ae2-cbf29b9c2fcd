#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test environment variables loading
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

def test_env_vars():
    """Test environment variables"""
    print("🔍 Testing Environment Variables...")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = Path(".env")
    print(f"1. .env file exists: {env_file.exists()}")
    
    if env_file.exists():
        print(f"   .env file path: {env_file.absolute()}")
        print(f"   .env file size: {env_file.stat().st_size} bytes")
    
    # Test direct environment variable access
    print("\n2. Direct environment variable access:")
    env_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID", 
        "COOKIECLOUD_PASSWORD",
        "XINGTU_DOMAIN"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            if "PASSWORD" in var:
                print(f"   {var}: {'*' * len(value)}")
            else:
                print(f"   {var}: {value}")
        else:
            print(f"   {var}: ❌ NOT SET")
    
    # Test settings loading
    print("\n3. Testing settings loading...")
    try:
        from config.settings import settings
        
        print(f"   CookieCloud server: {settings.cookiecloud.server_url}")
        print(f"   CookieCloud UUID: {settings.cookiecloud.uuid}")
        print(f"   CookieCloud password: {'*' * len(settings.cookiecloud.password)}")
        print(f"   Xingtu domain: {settings.xingtu.domain}")
        
        # Check if values are defaults
        if settings.cookiecloud.uuid == "your-uuid-here":
            print("   ❌ UUID is still default value!")
            return False
        
        if settings.cookiecloud.password == "your-password-here":
            print("   ❌ Password is still default value!")
            return False
        
        print("   ✅ Settings loaded correctly")
        return True
        
    except Exception as e:
        print(f"   ❌ Error loading settings: {e}")
        return False


def test_cookiecloud_direct():
    """Test CookieCloud with direct values"""
    print("\n4. Testing CookieCloud with direct values...")
    
    try:
        from PyCookieCloud import PyCookieCloud
        
        # Use values directly from environment
        server_url = os.getenv("COOKIECLOUD_SERVER_URL")
        uuid = os.getenv("COOKIECLOUD_UUID")
        password = os.getenv("COOKIECLOUD_PASSWORD")
        
        print(f"   Server: {server_url}")
        print(f"   UUID: {uuid}")
        print(f"   Password: {'*' * len(password) if password else 'None'}")
        
        if not all([server_url, uuid, password]):
            print("   ❌ Missing required values")
            return False
        
        client = PyCookieCloud(server_url, uuid, password)
        
        # Test connection
        key = client.get_the_key()
        if key:
            print("   ✅ Connection successful")
            
            # Try to get data
            data = client.get_decrypted_data()
            if data:
                print(f"   ✅ Data retrieved: {len(data)} items")
                return True
            else:
                print("   ❌ No data returned")
                return False
        else:
            print("   ❌ Connection failed")
            return False
            
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return False


def main():
    """Main test"""
    print("🚀 Environment Variables Test")
    print("=" * 60)
    
    env_success = test_env_vars()
    direct_success = test_cookiecloud_direct()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary")
    print("=" * 60)
    print(f"Environment Variables: {'✅ PASS' if env_success else '❌ FAIL'}")
    print(f"CookieCloud Direct: {'✅ PASS' if direct_success else '❌ FAIL'}")
    
    return env_success and direct_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
