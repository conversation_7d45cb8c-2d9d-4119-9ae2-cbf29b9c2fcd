#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup script for 星图超级接口线上化
"""

import os
import sys
import subprocess
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e.stderr}")
        return False


def check_python_version():
    """Check Python version"""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11 or higher is required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True


def create_directories():
    """Create necessary directories"""
    directories = ["logs", "exports", "data/logs", "data/exports", "data/nginx-logs"]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"📁 Created directory: {directory}")
    
    return True


def setup_environment():
    """Setup environment file"""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_file.exists() and env_example.exists():
        env_file.write_text(env_example.read_text())
        print("📝 Created .env file from .env.example")
        print("⚠️  Please edit .env file with your configuration")
        return True
    elif env_file.exists():
        print("✅ .env file already exists")
        return True
    else:
        print("❌ .env.example file not found")
        return False


def install_dependencies():
    """Install Python dependencies"""
    if not run_command("pip install -r requirements.txt", "Installing Python dependencies"):
        return False
    return True


def check_docker():
    """Check if Docker is available"""
    if run_command("docker --version", "Checking Docker"):
        if run_command("docker-compose --version", "Checking Docker Compose"):
            return True
    print("⚠️  Docker not available - local development only")
    return False


def validate_environment():
    """Validate environment configuration"""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    required_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID",
        "COOKIECLOUD_PASSWORD"
    ]
    
    env_content = env_file.read_text()
    missing_vars = []
    
    for var in required_vars:
        if f"{var}=" not in env_content or f"{var}=your-" in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Please configure these variables in .env: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment configuration looks good")
    return True


def run_tests():
    """Run basic tests"""
    print("🧪 Running basic tests...")
    
    # Test imports
    try:
        import fastapi
        import uvicorn
        import requests
        import pandas
        import openpyxl
        print("✅ All required packages imported successfully")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Run unit tests if pytest is available
    try:
        import pytest
        if run_command("python -m pytest tests/test_api.py::TestAPI::test_root_endpoint -v", "Running basic API test"):
            print("✅ Basic tests passed")
        else:
            print("⚠️  Some tests failed - check configuration")
    except ImportError:
        print("⚠️  pytest not available - skipping tests")
    
    return True


def main():
    """Main setup function"""
    print("🚀 Setting up 星图超级接口线上化...")
    print("=" * 50)
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Create directories
    if not create_directories():
        success = False
    
    # Setup environment
    if not setup_environment():
        success = False
    
    # Install dependencies
    if not install_dependencies():
        success = False
    
    # Check Docker
    docker_available = check_docker()
    
    # Validate environment
    env_valid = validate_environment()
    
    # Run tests
    if not run_tests():
        success = False
    
    print("\n" + "=" * 50)
    
    if success and env_valid:
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Configure your CookieCloud settings in .env")
        print("2. Start the application:")
        print("   - Local: python main.py")
        if docker_available:
            print("   - Docker: docker-compose up -d")
        print("3. Visit http://localhost:8000/docs for API documentation")
        print("4. Test with: curl http://localhost:8000/health")
    else:
        print("❌ Setup completed with issues")
        print("Please resolve the issues above before running the application")
        if not env_valid:
            print("⚠️  Don't forget to configure your .env file!")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
