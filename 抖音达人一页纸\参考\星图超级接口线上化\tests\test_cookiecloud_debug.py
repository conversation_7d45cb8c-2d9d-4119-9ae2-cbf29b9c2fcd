#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CookieCloud debugging test for 星图超级接口线上化
"""

import os
import sys
import time
import json
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_cookiecloud_connectivity():
    """Test CookieCloud server connectivity"""
    print("🔍 Testing CookieCloud server connectivity...")
    
    # Get configuration from .env
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    env_content = env_file.read_text(encoding='utf-8')
    
    # Extract CookieCloud configuration
    server_url = None
    uuid = None
    password = None
    
    for line in env_content.split('\n'):
        if line.startswith('COOKIECLOUD_SERVER_URL='):
            server_url = line.split('=', 1)[1].strip()
        elif line.startswith('COOKIECLOUD_UUID='):
            uuid = line.split('=', 1)[1].strip()
        elif line.startswith('COOKIECLOUD_PASSWORD='):
            password = line.split('=', 1)[1].strip()
    
    print(f"Server URL: {server_url}")
    print(f"UUID: {uuid}")
    print(f"Password: {'*' * len(password) if password else 'None'}")
    
    if not all([server_url, uuid, password]):
        print("❌ Missing CookieCloud configuration")
        return False
    
    # Test server connectivity
    try:
        print(f"\n🌐 Testing connectivity to: {server_url}")
        
        # Try to connect to the server
        req = urllib.request.Request(server_url, method='GET')
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        with urllib.request.urlopen(req, timeout=10) as response:
            print(f"✅ Server is reachable: {response.status}")
            content = response.read().decode('utf-8')
            print(f"   Response length: {len(content)} bytes")
            return True
            
    except urllib.error.URLError as e:
        print(f"❌ Server connectivity failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_cookiecloud_api():
    """Test CookieCloud API directly"""
    print("\n🔍 Testing CookieCloud API directly...")
    
    try:
        from PyCookieCloud import PyCookieCloud
        
        # Get configuration
        env_file = Path(".env")
        env_content = env_file.read_text(encoding='utf-8')
        
        server_url = None
        uuid = None
        password = None
        
        for line in env_content.split('\n'):
            if line.startswith('COOKIECLOUD_SERVER_URL='):
                server_url = line.split('=', 1)[1].strip()
            elif line.startswith('COOKIECLOUD_UUID='):
                uuid = line.split('=', 1)[1].strip()
            elif line.startswith('COOKIECLOUD_PASSWORD='):
                password = line.split('=', 1)[1].strip()
        
        print(f"Creating PyCookieCloud client...")
        client = PyCookieCloud(server_url, uuid, password)
        print("✅ Client created successfully")
        
        print("Attempting to get cookies...")
        start_time = time.time()
        cookies = client.get_decrypted_data()
        elapsed = time.time() - start_time
        
        print(f"✅ Cookie retrieval completed in {elapsed:.2f}s")
        
        if cookies:
            print(f"   Total domains: {len([d for d in cookies.keys() if d != 'update_time'])}")
            print(f"   Update time: {cookies.get('update_time', 'Unknown')}")
            
            # Check for Xingtu domain
            xingtu_domains = [d for d in cookies.keys() if 'xingtu' in d.lower() or 'douyin' in d.lower()]
            print(f"   Xingtu-related domains: {xingtu_domains}")
            
            # Check for common domains
            common_domains = [d for d in cookies.keys() if d in ['www.xingtu.cn', '.xingtu.cn', 'xingtu.cn']]
            print(f"   Xingtu domains: {common_domains}")
            
            if common_domains:
                domain = common_domains[0]
                domain_cookies = cookies.get(domain, [])
                print(f"   Cookies in {domain}: {len(domain_cookies)}")
                
                # Check for required cookies
                cookie_names = [c.get('name', '') for c in domain_cookies if isinstance(c, dict)]
                required_cookies = ['s_v_web_id', 'tt_webid', 'sessionid']
                found_required = [c for c in required_cookies if c in cookie_names]
                
                print(f"   Required cookies found: {found_required}")
                print(f"   All cookie names: {cookie_names[:10]}...")  # Show first 10
                
                return len(found_required) > 0
            else:
                print("   No Xingtu domain cookies found")
                return False
        else:
            print("❌ No cookies retrieved")
            return False
            
    except ImportError:
        print("❌ PyCookieCloud not installed")
        return False
    except Exception as e:
        print(f"❌ CookieCloud API error: {e}")
        return False


def test_cookie_manager_direct():
    """Test cookie manager directly"""
    print("\n🔍 Testing cookie manager directly...")
    
    try:
        from core.cookie_manager import CookieManager
        
        print("Creating CookieManager...")
        cookie_manager = CookieManager()
        print("✅ CookieManager created")
        
        print("Loading cookies...")
        start_time = time.time()
        success = cookie_manager.load_cookies(force_refresh=True)
        elapsed = time.time() - start_time
        
        print(f"Cookie loading completed in {elapsed:.2f}s: {success}")
        
        if success:
            info = cookie_manager.get_cookies_info()
            print(f"✅ Cookie info: {info}")
            
            xingtu_cookies = cookie_manager.get_xingtu_cookies()
            print(f"   Xingtu cookies: {len(xingtu_cookies)}")
            
            csrf_token = cookie_manager.get_csrf_token()
            print(f"   CSRF token: {'Available' if csrf_token else 'Not found'}")
            
            validation = cookie_manager.validate_xingtu_cookies()
            print(f"   Validation: {validation}")
            
            return validation
        else:
            print("❌ Cookie loading failed")
            return False
            
    except Exception as e:
        print(f"❌ Cookie manager error: {e}")
        return False


def test_network_proxy():
    """Test if proxy is interfering"""
    print("\n🔍 Testing network and proxy settings...")
    
    # Check environment variables for proxy
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
    proxy_found = False
    
    for var in proxy_vars:
        if os.environ.get(var):
            print(f"⚠️  Proxy detected: {var}={os.environ[var]}")
            proxy_found = True
    
    if not proxy_found:
        print("✅ No proxy environment variables found")
    
    # Test basic internet connectivity
    try:
        print("Testing basic internet connectivity...")
        req = urllib.request.Request('https://www.baidu.com', method='GET')
        req.add_header('User-Agent', 'Mozilla/5.0')
        
        with urllib.request.urlopen(req, timeout=10) as response:
            print(f"✅ Internet connectivity OK: {response.status}")
            return True
    except Exception as e:
        print(f"❌ Internet connectivity failed: {e}")
        return False


def main():
    """Main debugging function"""
    print("🚀 CookieCloud Debugging for 星图超级接口线上化")
    print("=" * 60)
    
    tests = [
        ("Network & Proxy", test_network_proxy),
        ("CookieCloud Connectivity", test_cookiecloud_connectivity),
        ("CookieCloud API", test_cookiecloud_api),
        ("Cookie Manager", test_cookie_manager_direct)
    ]
    
    results = []
    
    for name, test_func in tests:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            success = test_func()
            results.append((name, success))
            if success:
                print(f"✅ {name} PASSED")
            else:
                print(f"❌ {name} FAILED")
        except Exception as e:
            print(f"❌ {name} ERROR: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 DEBUGGING SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed >= 3:
        print("\n🎉 CookieCloud should be working!")
        print("Try running the real author data test again.")
    else:
        print("\n⚠️  CookieCloud issues detected.")
        print("Recommendations:")
        print("1. Check CookieCloud server is running")
        print("2. Verify UUID and password are correct")
        print("3. Ensure Xingtu cookies are present in CookieCloud")
        print("4. Check network connectivity")
    
    return passed >= 3


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
