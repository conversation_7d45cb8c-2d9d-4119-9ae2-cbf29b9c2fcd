#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试部署后的API
"""

import requests
import json
import time

def test_api_endpoint(url, description):
    """测试API端点"""
    print(f"\n🔍 测试: {description}")
    print(f"URL: {url}")
    
    try:
        response = requests.get(url, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {response.elapsed.total_seconds():.2f}秒")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ 成功 - JSON响应")
                print(f"响应键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                # 显示部分响应内容
                if isinstance(data, dict):
                    for key, value in list(data.items())[:3]:  # 只显示前3个键
                        if isinstance(value, (str, int, float, bool)):
                            print(f"  {key}: {value}")
                        else:
                            print(f"  {key}: {type(value).__name__}")
                            
            except json.JSONDecodeError:
                print(f"⚠️  响应不是JSON格式")
                print(f"响应内容: {response.text[:200]}...")
        else:
            print(f"❌ 错误 - 状态码 {response.status_code}")
            print(f"错误内容: {response.text[:200]}...")
            
    except requests.exceptions.Timeout:
        print(f"⏰ 超时 - 请求超过30秒")
    except requests.exceptions.ConnectionError:
        print(f"🔌 连接错误 - 无法连接到服务器")
    except Exception as e:
        print(f"❌ 异常 - {e}")

def main():
    """主测试函数"""
    print("🚀 测试Zeabur部署的API")
    print("=" * 50)
    
    base_url = "https://iubdfibsioyfgbsireg.zeabur.app"
    api_key = "XingTu2025SuperSecureKey_dxy_production_v1.0"
    
    # 测试端点列表
    test_cases = [
        (f"{base_url}/", "根端点 - API信息"),
        (f"{base_url}/health", "健康检查端点"),
        (f"{base_url}/debug/auth?api_key={api_key}", "调试认证端点"),
        (f"{base_url}/author/6651749112423120908?api_key={api_key}", "有效作者ID"),
        (f"{base_url}/author/7119367979820646432?api_key={api_key}", "原始问题作者ID"),
        (f"{base_url}/author/123?api_key={api_key}", "无效作者ID"),
    ]
    
    for url, description in test_cases:
        test_api_endpoint(url, description)
        time.sleep(1)  # 避免请求过快
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("💡 如果所有测试都通过，说明API已正常工作")

if __name__ == "__main__":
    main()
