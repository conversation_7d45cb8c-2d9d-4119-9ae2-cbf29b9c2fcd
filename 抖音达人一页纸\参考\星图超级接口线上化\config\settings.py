#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Configuration settings for 星图超级接口线上化
"""

import os
from pathlib import Path

# Load environment variables first
from dotenv import load_dotenv
load_dotenv()


class CookieCloudConfig:
    """CookieCloud configuration"""
    def __init__(self):
        self.server_url = os.getenv("COOKIECLOUD_SERVER_URL", "http://localhost:8088")
        self.uuid = os.getenv("COOKIECLOUD_UUID", "your-uuid-here")
        self.password = os.getenv("COOKIECLOUD_PASSWORD", "your-password-here")


class XingtuConfig:
    """Xingtu API configuration"""
    def __init__(self):
        self.base_url = os.getenv("XINGTU_BASE_URL", "https://www.xingtu.cn")
        self.domain = os.getenv("XINGTU_DOMAIN", "www.xingtu.cn")
        self.timeout = int(os.getenv("REQUEST_TIMEOUT", "30"))
        self.max_retries = int(os.getenv("MAX_RETRIES", "3"))
        self.retry_interval = int(os.getenv("RETRY_INTERVAL", "2"))
        self.rate_limit_per_minute = int(os.getenv("RATE_LIMIT_PER_MINUTE", "60"))


class AppConfig:
    """Application configuration"""
    def __init__(self):
        self.env = os.getenv("APP_ENV", "development")
        self.host = os.getenv("API_HOST", "0.0.0.0")
        self.port = int(os.getenv("API_PORT", "8000"))
        self.debug = os.getenv("DEBUG", "true").lower() == "true"
        self.log_level = os.getenv("LOG_LEVEL", "INFO")
        self.api_key = os.getenv("API_KEY")
        cors_origins_str = os.getenv("CORS_ORIGINS", "*")
        self.cors_origins = ["*"] if cors_origins_str == "*" else [origin.strip() for origin in cors_origins_str.split(",")]
        self.enable_request_logging = os.getenv("ENABLE_REQUEST_LOGGING", "true").lower() == "true"


class ExportConfig:
    """Export configuration"""
    def __init__(self):
        self.export_dir = os.getenv("EXPORT_DIR", "exports")
        self.max_export_size_mb = int(os.getenv("MAX_EXPORT_SIZE_MB", "100"))
        self.excel_format = os.getenv("EXCEL_FORMAT", "xlsx")
        # Create export directory
        Path(self.export_dir).mkdir(parents=True, exist_ok=True)


class MonitoringConfig:
    """Monitoring configuration"""
    def __init__(self):
        self.health_check_interval = int(os.getenv("HEALTH_CHECK_INTERVAL", "60"))
        self.cookie_refresh_interval = int(os.getenv("COOKIE_REFRESH_INTERVAL", "30"))


class Settings:
    """Main application settings"""

    def __init__(self):
        self.cookiecloud = CookieCloudConfig()
        self.xingtu = XingtuConfig()
        self.app = AppConfig()
        self.export = ExportConfig()
        self.monitoring = MonitoringConfig()


# Global settings instance
settings = Settings()


# Validate required environment variables
def validate_settings():
    """Validate that all required settings are present"""
    required_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID", 
        "COOKIECLOUD_PASSWORD"
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")
    
    return True


# Common HTTP headers for Xingtu requests
XINGTU_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Encoding': 'gzip, deflate, br, zstd',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
    'sec-ch-ua-mobile': '?0',
    'agw-js-conv': 'str',
    'x-login-source': '1',
    'dnt': '1',
    'sec-fetch-site': 'same-origin',
    'sec-fetch-mode': 'cors',
    'sec-fetch-dest': 'empty',
    'referer': 'https://www.xingtu.cn/',
    'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8'
}
