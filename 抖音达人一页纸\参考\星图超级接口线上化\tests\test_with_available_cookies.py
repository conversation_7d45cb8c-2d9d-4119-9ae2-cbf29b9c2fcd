#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test with available cookies for 星图超级接口线上化
"""

import os
import sys
import time
import json
import urllib.request
import urllib.parse
import urllib.error
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_cookie_manager_with_debug():
    """Test cookie manager with detailed debugging"""
    print("🔍 Testing cookie manager with detailed debugging...")
    
    try:
        from core.cookie_manager import <PERSON>ieManager
        from PyCookieCloud import PyCookieCloud
        
        # Get configuration
        env_file = Path(".env")
        env_content = env_file.read_text(encoding='utf-8')
        
        server_url = None
        uuid = None
        password = None
        
        for line in env_content.split('\n'):
            if line.startswith('COOKIECLOUD_SERVER_URL='):
                server_url = line.split('=', 1)[1].strip()
            elif line.startswith('COOKIECLOUD_UUID='):
                uuid = line.split('=', 1)[1].strip()
            elif line.startswith('COOKIECLOUD_PASSWORD='):
                password = line.split('=', 1)[1].strip()
        
        # Test direct PyCookieCloud first
        print("1. Testing direct PyCookieCloud...")
        client = PyCookieCloud(server_url, uuid, password)
        cookies = client.get_decrypted_data()
        
        if cookies:
            print(f"   ✅ Got {len(cookies)} domains from CookieCloud")
            
            # Check Xingtu domains
            xingtu_domains = [d for d in cookies.keys() if 'xingtu' in d.lower()]
            print(f"   Xingtu domains: {xingtu_domains}")
            
            for domain in xingtu_domains:
                domain_cookies = cookies.get(domain, [])
                print(f"   {domain}: {len(domain_cookies)} cookies")
                
                if isinstance(domain_cookies, list) and domain_cookies:
                    cookie_names = [c.get('name', '') for c in domain_cookies if isinstance(c, dict)]
                    print(f"     Cookie names: {cookie_names}")
        
        # Test our cookie manager
        print("\n2. Testing our CookieManager...")
        cookie_manager = CookieManager()
        
        # Manually set the cookies to bypass the loading issue
        cookie_manager.all_cookies = cookies
        cookie_manager.last_refresh = time.time()
        
        print("   Manually set cookies in manager")
        
        # Test getting Xingtu cookies
        xingtu_cookies = cookie_manager.get_xingtu_cookies()
        print(f"   ✅ Xingtu cookies: {len(xingtu_cookies)}")
        print(f"   Cookie names: {list(xingtu_cookies.keys())}")
        
        # Test validation
        validation = cookie_manager.validate_xingtu_cookies()
        print(f"   Validation result: {validation}")
        
        # Test CSRF token
        csrf_token = cookie_manager.get_csrf_token()
        print(f"   CSRF token: {'Found' if csrf_token else 'Not found'}")
        
        # Test cookie header
        cookie_header = cookie_manager.get_cookie_header()
        print(f"   Cookie header length: {len(cookie_header)}")
        
        return validation
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_api_with_manual_cookies():
    """Test API with manually set cookies"""
    print("\n🔍 Testing API with manually set cookies...")
    
    try:
        from fastapi.testclient import TestClient
        from core.cookie_manager import CookieManager
        from core.xingtu_client import XingtuClient
        from PyCookieCloud import PyCookieCloud
        
        # Get cookies directly
        env_file = Path(".env")
        env_content = env_file.read_text(encoding='utf-8')
        
        server_url = None
        uuid = None
        password = None
        
        for line in env_content.split('\n'):
            if line.startswith('COOKIECLOUD_SERVER_URL='):
                server_url = line.split('=', 1)[1].strip()
            elif line.startswith('COOKIECLOUD_UUID='):
                uuid = line.split('=', 1)[1].strip()
            elif line.startswith('COOKIECLOUD_PASSWORD='):
                password = line.split('=', 1)[1].strip()
        
        client = PyCookieCloud(server_url, uuid, password)
        cookies = client.get_decrypted_data()
        
        if not cookies:
            print("❌ No cookies available")
            return False
        
        # Create cookie manager and manually set cookies
        cookie_manager = CookieManager()
        cookie_manager.all_cookies = cookies
        cookie_manager.last_refresh = time.time()
        
        # Test cookie manager functions
        xingtu_cookies = cookie_manager.get_xingtu_cookies()
        print(f"Available Xingtu cookies: {len(xingtu_cookies)}")
        
        if len(xingtu_cookies) == 0:
            print("❌ No Xingtu cookies found")
            return False
        
        # Create Xingtu client
        xingtu_client = XingtuClient(cookie_manager)
        
        # Test health status
        health_status = xingtu_client.get_health_status()
        print(f"Health status: {health_status}")
        
        # Test with a simple endpoint
        author_id = "7119367979820646432"
        print(f"\nTesting author endpoint with ID: {author_id}")
        
        # Import the app and test
        from main import app
        test_client = TestClient(app)
        
        # Monkey patch the cookie manager in the app
        import main
        main.cookie_manager = cookie_manager
        main.xingtu_client = XingtuClient(cookie_manager)
        
        # Test health endpoint
        response = test_client.get("/health")
        print(f"Health endpoint: {response.status_code}")
        
        if response.status_code in [200, 503]:
            data = response.json()
            print(f"Health status: {data.get('status', 'unknown')}")
            
            components = data.get("components", {})
            cookie_healthy = components.get("cookie_manager", {}).get("healthy", False)
            client_healthy = components.get("xingtu_client", {}).get("healthy", False)
            
            print(f"Cookie manager healthy: {cookie_healthy}")
            print(f"Xingtu client healthy: {client_healthy}")
            
            if cookie_healthy and client_healthy:
                print("✅ System is healthy with manual cookies!")
                
                # Try author endpoint
                print(f"\nTesting author endpoint...")
                response = test_client.get(f"/author/{author_id}?endpoints=base_info")
                print(f"Author endpoint: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    metadata = data.get("_metadata", {})
                    print(f"✅ Author data retrieved!")
                    print(f"   Success rate: {metadata.get('success_rate', 0):.2%}")
                    
                    if "base_info" in data and data["base_info"].get("success"):
                        base_data = data["base_info"].get("data", {})
                        print(f"   Author name: {base_data.get('nickname', 'Unknown')}")
                    
                    return True
                else:
                    print(f"❌ Author endpoint failed: {response.status_code}")
                    if response.status_code == 500:
                        error_data = response.json()
                        print(f"   Error: {error_data.get('detail', 'Unknown')}")
        
        return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("🚀 Testing with Available Cookies for 星图超级接口线上化")
    print("=" * 60)
    
    tests = [
        ("Cookie Manager Debug", test_cookie_manager_with_debug),
        ("API with Manual Cookies", test_api_with_manual_cookies)
    ]
    
    results = []
    
    for name, test_func in tests:
        print(f"\n{'='*20} {name} {'='*20}")
        try:
            success = test_func()
            results.append((name, success))
            if success:
                print(f"✅ {name} PASSED")
            else:
                print(f"❌ {name} FAILED")
        except Exception as e:
            print(f"❌ {name} ERROR: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 MANUAL COOKIE TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed >= 1:
        print("\n🎉 SUCCESS! We can work with the available cookies!")
        print("The system can retrieve author data with the current cookie setup.")
        return True
    else:
        print("\n⚠️  Cookie issues persist.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
