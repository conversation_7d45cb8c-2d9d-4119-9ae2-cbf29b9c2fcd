#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Setup validation script for 星图超级接口线上化
"""

import os
import sys
import importlib
from pathlib import Path


def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    if sys.version_info < (3, 11):
        print(f"❌ Python 3.11+ required, found {sys.version_info.major}.{sys.version_info.minor}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} OK")
    return True


def check_dependencies():
    """Check required dependencies"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        "fastapi",
        "uvicorn", 
        "requests",
        "pandas",
        "openpyxl",
        "structlog",
        "pydantic",
        "httpx"
    ]
    
    missing = []
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - missing")
            missing.append(package)
    
    if missing:
        print(f"\n❌ Missing packages: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True


def check_project_structure():
    """Check project structure"""
    print("📁 Checking project structure...")
    
    required_files = [
        "main.py",
        "requirements.txt",
        ".env.example",
        "Dockerfile",
        "docker-compose.yml",
        "config/__init__.py",
        "config/settings.py",
        "config/api_endpoints.py",
        "core/__init__.py",
        "core/cookie_manager.py",
        "core/xingtu_client.py",
        "core/excel_exporter.py",
        "utils/__init__.py",
        "utils/logger.py",
        "utils/validators.py"
    ]
    
    missing = []
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ {file_path} - missing")
            missing.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing:
        print(f"\n❌ Missing files: {', '.join(missing)}")
        return False
    
    return True


def check_configuration():
    """Check configuration"""
    print("⚙️ Checking configuration...")

    # Check .env file
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found - copy from .env.example")
        return False

    try:
        # Try to read with UTF-8 first
        env_content = env_file.read_text(encoding='utf-8')
    except UnicodeDecodeError:
        try:
            # Try other encodings
            env_content = env_file.read_text(encoding='gbk')
            # Convert to UTF-8
            env_file.write_text(env_content, encoding='utf-8')
            env_content = env_file.read_text(encoding='utf-8')
            print("✅ Fixed .env file encoding")
        except Exception as e:
            print(f"❌ Cannot read .env file: {e}")
            return False

    # Check environment variables
    required_vars = [
        "COOKIECLOUD_SERVER_URL",
        "COOKIECLOUD_UUID",
        "COOKIECLOUD_PASSWORD"
    ]

    configured = []
    unconfigured = []

    for var in required_vars:
        if f"{var}=" in env_content and f"{var}=your-" not in env_content and f"{var}=test-" not in env_content:
            configured.append(var)
            print(f"✅ {var}")
        else:
            unconfigured.append(var)
            print(f"⚠️  {var} - needs configuration")

    if unconfigured:
        print(f"\n⚠️  Unconfigured variables: {', '.join(unconfigured)}")
        print("Please edit .env file with your CookieCloud settings")
        print("For testing purposes, you can use placeholder values")
        return True  # Allow testing with placeholder values

    return True


def test_imports():
    """Test critical imports"""
    print("🧪 Testing imports...")
    
    try:
        # Test config imports
        from config.settings import settings
        print("✅ config.settings")
        
        from config.api_endpoints import XINGTU_API_ENDPOINTS
        print("✅ config.api_endpoints")
        
        # Test core imports
        from core.cookie_manager import CookieManager
        print("✅ core.cookie_manager")
        
        from core.xingtu_client import XingtuClient
        print("✅ core.xingtu_client")
        
        from core.excel_exporter import ExcelExporter
        print("✅ core.excel_exporter")
        
        # Test utils imports
        from utils.logger import setup_logger
        print("✅ utils.logger")
        
        from utils.validators import validate_author_id
        print("✅ utils.validators")
        
        # Test main app
        from main import app
        print("✅ main.app")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


def test_basic_functionality():
    """Test basic functionality"""
    print("🔧 Testing basic functionality...")
    
    try:
        # Test settings
        from config.settings import settings
        assert hasattr(settings, 'cookiecloud')
        assert hasattr(settings, 'xingtu')
        print("✅ Settings configuration")
        
        # Test API endpoints
        from config.api_endpoints import XINGTU_API_ENDPOINTS
        assert len(XINGTU_API_ENDPOINTS) > 0
        print(f"✅ API endpoints ({len(XINGTU_API_ENDPOINTS)} endpoints)")
        
        # Test validators
        from utils.validators import validate_author_id
        assert validate_author_id("1234567890") == True
        assert validate_author_id("invalid") == False
        print("✅ Validators")
        
        # Test logger
        from utils.logger import setup_logger
        logger = setup_logger("test")
        logger.info("Test log message")
        print("✅ Logger")
        
        return True
        
    except Exception as e:
        print(f"❌ Functionality test error: {e}")
        return False


def check_docker():
    """Check Docker setup"""
    print("🐳 Checking Docker setup...")
    
    import subprocess
    
    try:
        # Check Docker
        result = subprocess.run(["docker", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker available")
        else:
            print("⚠️  Docker not available")
            return False
        
        # Check Docker Compose
        result = subprocess.run(["docker-compose", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Docker Compose available")
        else:
            print("⚠️  Docker Compose not available")
            return False
        
        # Check Dockerfile
        if Path("Dockerfile").exists():
            print("✅ Dockerfile present")
        else:
            print("❌ Dockerfile missing")
            return False
        
        # Check docker-compose.yml
        if Path("docker-compose.yml").exists():
            print("✅ docker-compose.yml present")
        else:
            print("❌ docker-compose.yml missing")
            return False
        
        return True
        
    except FileNotFoundError:
        print("⚠️  Docker not installed")
        return False


def main():
    """Main validation function"""
    print("🚀 Validating 星图超级接口线上化 setup...")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Project Structure", check_project_structure),
        ("Configuration", check_configuration),
        ("Imports", test_imports),
        ("Basic Functionality", test_basic_functionality),
        ("Docker Setup", check_docker)
    ]
    
    results = []
    
    for name, check_func in checks:
        print(f"\n{name}:")
        print("-" * 30)
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} check failed: {e}")
            results.append((name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {name}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All checks passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Configure CookieCloud settings in .env")
        print("2. Start the application: python main.py")
        print("3. Test with: curl http://localhost:8000/health")
        print("4. Deploy with: python scripts/deploy.py")
        return True
    else:
        print(f"\n⚠️  {total - passed} checks failed. Please fix the issues above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
