#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie management using CookieCloud for Xingtu authentication
"""

import time
from typing import Dict, Optional, List
from PyCookieCloud import PyCookieCloud
import structlog

from config.settings import settings
from utils.logger import log_cookie_operation, log_error
from utils.validators import validate_cookie_format


class CookieManager:
    """
    Manages cookies using CookieCloud for Xingtu authentication
    """
    
    def __init__(self):
        """Initialize CookieManager with CookieCloud configuration"""
        self.logger = structlog.get_logger("cookie_manager")
        self.config = settings.cookiecloud
        self.xingtu_domain = settings.xingtu.domain
        self.client = None
        self.all_cookies = None
        self.last_refresh = 0
        self.refresh_interval = settings.monitoring.cookie_refresh_interval * 60  # Convert to seconds
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize CookieCloud client"""
        try:
            self.client = PyCookieCloud(
                self.config.server_url,
                self.config.uuid,
                self.config.password
            )
            log_cookie_operation(
                self.logger,
                "initialize",
                self.xingtu_domain,
                True,
                "CookieCloud client initialized"
            )

            # Test the connection immediately
            try:
                test_cookies = self.client.get_decrypted_data()
                if test_cookies:
                    self.all_cookies = test_cookies
                    self.last_refresh = time.time()
                    log_cookie_operation(
                        self.logger,
                        "initialize",
                        self.xingtu_domain,
                        True,
                        f"Initial cookie load successful: {len(test_cookies)} domains"
                    )
            except Exception as e:
                log_error(self.logger, e, {"operation": "initial_cookie_load"})
                # Don't fail initialization, just log the error

        except Exception as e:
            log_error(self.logger, e, {"operation": "initialize_cookiecloud_client"})
            raise
    
    def load_cookies(self, force_refresh: bool = False) -> bool:
        """
        Load all cookies from CookieCloud
        
        Args:
            force_refresh: Force refresh even if within refresh interval
            
        Returns:
            True if cookies loaded successfully
        """
        current_time = time.time()
        
        # Check if we need to refresh
        if not force_refresh and self.all_cookies and (current_time - self.last_refresh) < self.refresh_interval:
            self.logger.debug("Using cached cookies", time_since_refresh=current_time - self.last_refresh)
            return True
        
        try:
            self.logger.info("Loading cookies from CookieCloud...")
            self.all_cookies = self.client.get_decrypted_data()

            if not self.all_cookies:
                log_cookie_operation(
                    self.logger,
                    "load",
                    self.xingtu_domain,
                    False,
                    "No cookies returned from CookieCloud"
                )
                return False

            # Check if we got valid data
            if not isinstance(self.all_cookies, dict):
                log_cookie_operation(
                    self.logger,
                    "load",
                    self.xingtu_domain,
                    False,
                    "Invalid cookie data format from CookieCloud"
                )
                return False

            self.last_refresh = current_time
            domain_count = len([d for d in self.all_cookies.keys() if d != 'update_time'])

            log_cookie_operation(
                self.logger,
                "load",
                self.xingtu_domain,
                True,
                f"Successfully loaded cookies for {domain_count} domains"
            )
            return True

        except Exception as e:
            log_error(self.logger, e, {"operation": "load_cookies"})
            # Reset cookies on error
            self.all_cookies = None
            return False
    
    def get_domain_cookies(self, domain: str) -> List[Dict]:
        """
        Get cookies for a specific domain
        
        Args:
            domain: Target domain (e.g., 'www.xingtu.cn')
            
        Returns:
            List of cookie dictionaries for the domain
        """
        if not self.all_cookies:
            if not self.load_cookies():
                return []
        
        domain_cookies = []
        
        # Check exact domain match
        if domain in self.all_cookies:
            domain_cookies.extend(self.all_cookies[domain])
        
        # Check for cookies that apply to subdomains
        for cookie_domain, cookies in self.all_cookies.items():
            if cookie_domain == 'update_time':
                continue
                
            # Check if cookie domain matches or is a parent domain
            if (cookie_domain.startswith('.') and domain.endswith(cookie_domain[1:])) or \
               (domain == cookie_domain) or \
               (cookie_domain.startswith('.') and cookie_domain[1:] in domain):
                if isinstance(cookies, list):
                    domain_cookies.extend(cookies)
        
        self.logger.debug("Retrieved domain cookies", domain=domain, cookie_count=len(domain_cookies))
        return domain_cookies
    
    def get_xingtu_cookies(self) -> Dict[str, str]:
        """
        Get Xingtu cookies in requests-compatible format
        
        Returns:
            Dictionary of cookie name-value pairs for Xingtu domain
        """
        cookies = self.get_domain_cookies(self.xingtu_domain)
        
        cookie_dict = {}
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_dict[name] = value
        
        if not validate_cookie_format(cookie_dict):
            self.logger.warning("Invalid cookie format detected")
            return {}
        
        log_cookie_operation(
            self.logger, 
            "get_xingtu", 
            self.xingtu_domain, 
            True, 
            f"Prepared {len(cookie_dict)} cookies for Xingtu authentication"
        )
        return cookie_dict
    
    def get_cookie_header(self, domain: str = None) -> str:
        """
        Get cookies formatted as HTTP Cookie header
        
        Args:
            domain: Target domain (defaults to Xingtu domain)
            
        Returns:
            Cookie header string
        """
        if domain is None:
            domain = self.xingtu_domain
            
        cookies = self.get_domain_cookies(domain)
        cookie_pairs = []
        
        for cookie in cookies:
            name = cookie.get('name')
            value = cookie.get('value')
            if name and value:
                cookie_pairs.append(f"{name}={value}")
        
        return '; '.join(cookie_pairs)
    
    def get_csrf_token(self) -> Optional[str]:
        """
        Extract CSRF token from cookies
        
        Returns:
            CSRF token if found, None otherwise
        """
        cookies = self.get_xingtu_cookies()
        
        # Common CSRF token cookie names
        csrf_names = ['csrftoken', 'csrf_token', 'X-CSRFToken', '_token']
        
        for name in csrf_names:
            if name in cookies:
                token = cookies[name]
                self.logger.debug("Found CSRF token", token_name=name)
                return token
        
        self.logger.warning("No CSRF token found in cookies")
        return None
    
    def refresh_cookies(self) -> bool:
        """
        Force refresh cookies from CookieCloud
        
        Returns:
            True if refresh successful
        """
        log_cookie_operation(
            self.logger, 
            "refresh", 
            self.xingtu_domain, 
            True, 
            "Forcing cookie refresh"
        )
        self.all_cookies = None
        self.last_refresh = 0
        return self.load_cookies(force_refresh=True)
    
    def validate_xingtu_cookies(self) -> bool:
        """
        Validate that required Xingtu cookies are present

        Returns:
            True if required cookies are available
        """
        xingtu_cookies = self.get_xingtu_cookies()

        # Check for essential Xingtu cookies (relaxed validation)
        required_cookies = ['s_v_web_id']  # Only require the most essential one
        optional_cookies = ['tt_webid', 'sessionid', 'csrf_session_id']

        missing_required = [cookie for cookie in required_cookies if cookie not in xingtu_cookies]
        missing_optional = [cookie for cookie in optional_cookies if cookie not in xingtu_cookies]

        if missing_required:
            log_cookie_operation(
                self.logger,
                "validate",
                self.xingtu_domain,
                False,
                f"Missing required cookies: {missing_required}"
            )
            return False

        # Log missing optional cookies but don't fail
        if missing_optional:
            log_cookie_operation(
                self.logger,
                "validate",
                self.xingtu_domain,
                True,
                f"Some optional cookies missing: {missing_optional}, but validation passed"
            )
        else:
            log_cookie_operation(
                self.logger,
                "validate",
                self.xingtu_domain,
                True,
                "All Xingtu cookies validation passed"
            )
        return True
    
    def get_cookies_info(self) -> Dict:
        """
        Get information about available cookies
        
        Returns:
            Dictionary with cookie statistics
        """
        if not self.all_cookies:
            self.load_cookies()
        
        if not self.all_cookies:
            return {
                "total_domains": 0,
                "total_cookies": 0,
                "xingtu_cookies": 0,
                "domains": [],
                "update_time": "Unknown",
                "last_refresh": self.last_refresh,
                "csrf_token_available": False
            }
        
        total_cookies = 0
        domains = []
        
        for domain, cookies in self.all_cookies.items():
            if domain == 'update_time':
                continue
            if isinstance(cookies, list):
                total_cookies += len(cookies)
                domains.append(domain)
        
        xingtu_cookies = self.get_xingtu_cookies()
        
        return {
            "total_domains": len(domains),
            "total_cookies": total_cookies,
            "xingtu_cookies": len(xingtu_cookies),
            "domains": domains,
            "update_time": self.all_cookies.get('update_time', 'Unknown'),
            "last_refresh": self.last_refresh,
            "csrf_token_available": self.get_csrf_token() is not None
        }
    
    def is_healthy(self) -> bool:
        """
        Check if cookie manager is healthy
        
        Returns:
            True if healthy
        """
        try:
            # Check if we can load cookies
            if not self.load_cookies():
                return False
            
            # Check if we have Xingtu cookies
            xingtu_cookies = self.get_xingtu_cookies()
            if not xingtu_cookies:
                return False
            
            # Check if cookies are valid
            return self.validate_xingtu_cookies()
            
        except Exception as e:
            log_error(self.logger, e, {"operation": "health_check"})
            return False
