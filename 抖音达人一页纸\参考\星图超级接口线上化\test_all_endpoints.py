#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面测试所有API端点 - 并将结果保存到JSON文件
"""

import asyncio
import sys
import json
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from core.cookie_manager import CookieManager
from core.xingtu_client import XingtuClient
from utils.logger import setup_logger
from config.api_endpoints import XINGTU_API_ENDPOINTS


async def test_single_endpoint_detailed(xingtu_client, author_id, endpoint_config):
    """详细测试单个端点"""
    endpoint_name = endpoint_config.name
    
    print(f"🔍 测试端点: {endpoint_name}")
    
    test_result = {
        "endpoint_name": endpoint_name,
        "endpoint_path": endpoint_config.path,
        "endpoint_category": endpoint_config.category,
        "endpoint_description": endpoint_config.description,
        "endpoint_params": endpoint_config.specific_params,
        "id_param_name": endpoint_config.id_param_name,
        "test_author_id": author_id,
        "timestamp": datetime.now().isoformat(),
        "success": False,
        "error": None,
        "response_data": None,
        "response_size_bytes": 0,
        "response_time_ms": 0,
        "status_code": None,
        "has_data": False,
        "data_keys": [],
        "raw_response_sample": None
    }
    
    start_time = time.time()
    
    try:
        # 调用API
        response = await xingtu_client._make_request(endpoint_config, author_id)
        
        test_result["response_time_ms"] = round((time.time() - start_time) * 1000, 2)
        
        if isinstance(response, dict):
            test_result["success"] = response.get("error") is None
            test_result["error"] = response.get("error")
            test_result["response_data"] = response.get("data")
            
            # 检查是否有实际数据
            if response.get("data"):
                test_result["has_data"] = True
                if isinstance(response["data"], dict):
                    test_result["data_keys"] = list(response["data"].keys())
                    # 保存响应数据的前100个字符作为样本
                    test_result["raw_response_sample"] = str(response["data"])[:500] + "..." if len(str(response["data"])) > 500 else str(response["data"])
                
            # 检查状态码
            if response.get("data") and isinstance(response["data"], dict):
                base_resp = response["data"].get("base_resp", {})
                if isinstance(base_resp, dict):
                    test_result["status_code"] = base_resp.get("status_code")
                    if base_resp.get("status_message"):
                        test_result["api_message"] = base_resp.get("status_message")
            
            print(f"   ✅ 完成 - {'有数据' if test_result['has_data'] else '无数据'} ({test_result['response_time_ms']}ms)")
        else:
            test_result["error"] = f"响应格式异常: {type(response)}"
            test_result["raw_response_sample"] = str(response)[:200]
            print(f"   ❌ 响应格式异常")
            
    except Exception as e:
        test_result["error"] = str(e)
        test_result["response_time_ms"] = round((time.time() - start_time) * 1000, 2)
        print(f"   ❌ 异常: {e}")
    
    return test_result


async def test_all_endpoints():
    """测试所有API端点"""
    
    print("🧪 全面测试所有API端点")
    print("=" * 60)
    
    # 设置日志
    logger = setup_logger("test_all_endpoints", "INFO")
    
    # 测试结果容器
    test_results = {
        "test_info": {
            "test_time": datetime.now().isoformat(),
            "test_author_id": "6651749112423120908",
            "total_endpoints": len(XINGTU_API_ENDPOINTS),
            "test_duration_seconds": 0
        },
        "summary": {
            "total_tested": 0,
            "successful": 0,
            "failed": 0,
            "with_data": 0,
            "without_data": 0,
            "by_category": {}
        },
        "endpoints": []
    }
    
    start_time = time.time()
    
    try:
        # 初始化组件
        print("📁 初始化组件...")
        cookie_manager = CookieManager()
        xingtu_client = XingtuClient(cookie_manager)
        
        # 检查组件状态
        cookie_healthy = cookie_manager.is_healthy()
        client_status = xingtu_client.get_health_status()
        
        test_results["test_info"]["cookie_manager_healthy"] = cookie_healthy
        test_results["test_info"]["client_session_ready"] = client_status["session_ready"]
        test_results["test_info"]["client_cookies_available"] = client_status["cookies_available"]
        
        print(f"🍪 Cookie管理器: {'✅' if cookie_healthy else '❌'}")
        print(f"🔗 Xingtu客户端: {'✅' if client_status['session_ready'] else '❌'}")
        
        # 测试用的作者ID
        test_author_id = "6651749112423120908"
        print(f"\n🎯 测试作者ID: {test_author_id}")
        print(f"📡 总共要测试的端点: {len(XINGTU_API_ENDPOINTS)}个")
        
        # 按分类统计
        categories = {}
        for endpoint in XINGTU_API_ENDPOINTS:
            category = endpoint.category
            if category not in categories:
                categories[category] = 0
            categories[category] += 1
        
        print(f"📂 端点分类:")
        for category, count in categories.items():
            print(f"   {category}: {count}个")
        
        print(f"\n🚀 开始测试所有端点...")
        print("-" * 60)
        
        # 逐个测试所有端点
        for i, endpoint_config in enumerate(XINGTU_API_ENDPOINTS, 1):
            print(f"\n[{i}/{len(XINGTU_API_ENDPOINTS)}] ", end="")
            
            result = await test_single_endpoint_detailed(xingtu_client, test_author_id, endpoint_config)
            test_results["endpoints"].append(result)
            
            # 更新统计
            test_results["summary"]["total_tested"] += 1
            
            if result["success"] and not result["error"]:
                test_results["summary"]["successful"] += 1
            else:
                test_results["summary"]["failed"] += 1
            
            if result["has_data"]:
                test_results["summary"]["with_data"] += 1
            else:
                test_results["summary"]["without_data"] += 1
            
            # 按分类统计
            category = result["endpoint_category"]
            if category not in test_results["summary"]["by_category"]:
                test_results["summary"]["by_category"][category] = {
                    "total": 0, "successful": 0, "with_data": 0
                }
            
            test_results["summary"]["by_category"][category]["total"] += 1
            if result["success"] and not result["error"]:
                test_results["summary"]["by_category"][category]["successful"] += 1
            if result["has_data"]:
                test_results["summary"]["by_category"][category]["with_data"] += 1
            
            # 避免请求过快，稍微延迟
            await asyncio.sleep(0.1)
        
        # 计算总测试时间
        test_results["test_info"]["test_duration_seconds"] = round(time.time() - start_time, 2)
        
        print(f"\n" + "=" * 60)
        print(f"🏁 测试完成！")
        
        # 保存结果到JSON文件
        output_file = "test_results_all_endpoints.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        
        print(f"💾 结果已保存到: {output_file}")
        
        # 显示测试总结
        summary = test_results["summary"]
        print(f"\n📊 测试总结:")
        print(f"   📡 总端点数: {summary['total_tested']}")
        print(f"   ✅ 成功: {summary['successful']}")
        print(f"   ❌ 失败: {summary['failed']}")
        print(f"   📊 有数据: {summary['with_data']}")
        print(f"   📭 无数据: {summary['without_data']}")
        print(f"   ⏱️  总耗时: {test_results['test_info']['test_duration_seconds']}秒")
        
        print(f"\n📂 按分类统计:")
        for category, stats in summary["by_category"].items():
            success_rate = (stats["successful"] / stats["total"] * 100) if stats["total"] > 0 else 0
            data_rate = (stats["with_data"] / stats["total"] * 100) if stats["total"] > 0 else 0
            print(f"   {category}: {stats['total']}个 | 成功率: {success_rate:.1f}% | 有数据: {data_rate:.1f}%")
        
        # 显示有问题的端点
        failed_endpoints = [ep for ep in test_results["endpoints"] if ep["error"]]
        if failed_endpoints:
            print(f"\n⚠️  有问题的端点 ({len(failed_endpoints)}个):")
            for ep in failed_endpoints:
                print(f"   ❌ {ep['endpoint_name']}: {ep['error']}")
        
        # 显示无数据的端点
        no_data_endpoints = [ep for ep in test_results["endpoints"] if not ep["has_data"] and not ep["error"]]
        if no_data_endpoints:
            print(f"\n📭 无数据返回的端点 ({len(no_data_endpoints)}个):")
            for ep in no_data_endpoints[:10]:  # 只显示前10个
                print(f"   📭 {ep['endpoint_name']}")
            if len(no_data_endpoints) > 10:
                print(f"   ... 还有 {len(no_data_endpoints) - 10} 个")
        
        print(f"\n💡 建议:")
        print(f"   1. 查看 {output_file} 了解每个端点的详细情况")
        print(f"   2. 成功率 {summary['successful']}/{summary['total_tested']} = {summary['successful']/summary['total_tested']*100:.1f}%")
        print(f"   3. 数据获取率 {summary['with_data']}/{summary['total_tested']} = {summary['with_data']/summary['total_tested']*100:.1f}%")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        test_results["test_error"] = str(e)
        import traceback
        traceback.print_exc()
        
        # 即使出错也保存已有结果
        output_file = "test_results_all_endpoints_error.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(test_results, f, ensure_ascii=False, indent=2)
        print(f"💾 错误结果已保存到: {output_file}")


if __name__ == "__main__":
    print("🚀 星图超级接口线上化 - 全端点测试")
    print("ℹ️  此测试将验证所有29个API端点")
    print("ℹ️  结果将保存到JSON文件中供详细查看")
    print("=" * 60)
    
    # 运行全面测试
    try:
        asyncio.run(test_all_endpoints())
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试运行失败: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n👋 测试结束") 