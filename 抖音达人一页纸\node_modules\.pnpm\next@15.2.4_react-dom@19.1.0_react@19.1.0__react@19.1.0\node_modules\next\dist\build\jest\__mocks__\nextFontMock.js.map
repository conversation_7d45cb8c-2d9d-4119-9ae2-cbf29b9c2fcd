{"version": 3, "sources": ["../../../../src/build/jest/__mocks__/nextFontMock.ts"], "sourcesContent": ["module.exports = new Proxy(\n  {},\n  {\n    get: function getter() {\n      return () => ({\n        className: 'className',\n        variable: 'variable',\n        style: { fontFamily: 'fontFamily' },\n      })\n    },\n  }\n)\n"], "names": ["module", "exports", "Proxy", "get", "getter", "className", "variable", "style", "fontFamily"], "mappings": ";AAAAA,OAAOC,OAAO,GAAG,IAAIC,MACnB,CAAC,GACD;IACEC,KAAK,SAASC;QACZ,OAAO,IAAO,CAAA;gBACZC,WAAW;gBACXC,UAAU;gBACVC,OAAO;oBAAEC,YAAY;gBAAa;YACpC,CAAA;IACF;AACF"}